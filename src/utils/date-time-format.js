import localeMatch from './locale-match';
import mem from './mem';

const locales = [...navigator.languages];
try {
  const dtfLocale = new Intl.DateTimeFormat().resolvedOptions().locale;
  if (!locales.includes(dtfLocale)) {
    locales.push(dtfLocale);
  }
} catch {}

const createLocale = mem((language, options = {}) => {
  try {
    return new Intl.Locale(language, options);
  } catch {
    // Fallback to simple string splitting
    // May not work properly due to how complicated this is
    if (!language) return null;

    const parts = language.split('-');

    // https://www.w3.org/International/articles/language-tags/
    // Parts: language-extlang-script-region-variant-extension-privateuse
    const region = parts[3] || parts[2] || parts[1] || null;

    return {
      language: parts[0],
      region,
      ...options,
      toString: () => {
        if (options.language || options.region) {
          // Find which part is the region and reconstruct
          const regionIndex =
            [parts[3], parts[2], parts[1]].findIndex((p) => p === region) + 1;
          const beforeRegion =
            regionIndex > 0 ? parts.slice(0, regionIndex) : parts.slice(0, 1);
          const afterRegion =
            regionIndex > 0 ? parts.slice(regionIndex + 1) : parts.slice(1);

          const lang = options.language || beforeRegion.join('-');
          const finalRegion = options.region || region;
          const suffix =
            afterRegion.length > 0 ? `-${afterRegion.join('-')}` : '';

          return finalRegion
            ? `${lang}-${finalRegion}${suffix}`
            : `${lang}${suffix}`;
        }
        return language;
      },
    };
  }
});

const _DateTimeFormat = (locale, opts) => {
  const options = opts;

  const userLocale = createLocale(locales[0]);
  const appLocale = createLocale(locale);
  const userRegion = userLocale?.region;

  const userRegionLocale =
    userRegion && appLocale && appLocale.region !== userRegion
      ? createLocale(appLocale.language, {
          ...appLocale,
          region: userRegion,
        })?.toString()
      : null;

  const matchedLocale = localeMatch(
    [userRegionLocale, locale, locale?.replace(/-[a-z]+$/i, '')],
    locales,
    locale,
  );

  try {
    return new Intl.DateTimeFormat(matchedLocale, options);
  } catch {
    return new Intl.DateTimeFormat(options);
  }
};

const DateTimeFormat = mem(_DateTimeFormat);

export default DateTimeFormat;
