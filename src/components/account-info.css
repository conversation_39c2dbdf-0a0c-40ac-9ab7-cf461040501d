.account-container {
  /* display: flex; */
  /* flex-direction: column; */
  /* overflow: hidden; */
  overflow-y: auto;
  max-width: 100%;
  --banner-overlap: 44px;
  --posting-stats-size: 8px;
  --original-color: var(--link-color);

  .note {
    font-size: 0.95em;
    line-height: 1.4;
    text-wrap: pretty;
    margin-bottom: 16px;

    &:empty {
      display: none;
    }

    > *:first-child {
      margin-top: 0;
      padding-top: 0;
    }
    > *:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
    }

    &:not(:has(p)):not(:empty) {
      /* Some notes don't have <p> tags, so we need to add some padding */
      padding: 1em 0;
    }
  }

  .posting-stats {
    font-size: 90%;
    color: var(--text-insignificant-color);
    background-color: var(--bg-faded-color);
    padding: 8px 12px;

    &:is(:hover, :focus-within) {
      background-color: var(--link-bg-hover-color);
    }
  }

  .posting-stats-bar {
    --gap: 0.5px;
    --gap-color: var(--outline-color);
    height: var(--posting-stats-size);
    border-radius: var(--posting-stats-size);
    overflow: hidden;
    margin: 8px 0;
    box-shadow:
      inset 0 0 0 1px var(--outline-color),
      inset 0 0 0 1.5px var(--bg-blur-color);
    background-color: var(--bg-color);
    background-repeat: no-repeat;
    animation: swoosh-bg-image 0.3s ease-in-out 0.3s both;
    background-image: linear-gradient(
      var(--to-forward),
      var(--original-color) 0%,
      var(--original-color) calc(var(--originals-percentage) - var(--gap)),
      var(--gap-color) calc(var(--originals-percentage) - var(--gap)),
      var(--gap-color) calc(var(--originals-percentage) + var(--gap)),
      var(--reply-to-color) calc(var(--originals-percentage) + var(--gap)),
      var(--reply-to-color) calc(var(--replies-percentage) - var(--gap)),
      var(--gap-color) calc(var(--replies-percentage) - var(--gap)),
      var(--gap-color) calc(var(--replies-percentage) + var(--gap)),
      var(--reblog-color) calc(var(--replies-percentage) + var(--gap)),
      var(--reblog-color) 100%
    );
  }

  .posting-stats-legends {
    font-size: 12px;
    text-transform: uppercase;
  }

  .posting-stats-legend-item {
    display: inline-block;
    width: var(--posting-stats-size);
    height: var(--posting-stats-size);
    border-radius: var(--posting-stats-size);
    background-color: var(--text-insignificant-color);
    vertical-align: middle;
    margin: 0 4px 2px;
    /* border: 1px solid var(--outline-color); */
    box-shadow:
      inset 0 0 0 1px var(--outline-color),
      inset 0 0 0 1.5px var(--bg-blur-color);

    &.posting-stats-legend-item-originals {
      background-color: var(--original-color);
    }
    &.posting-stats-legend-item-replies {
      background-color: var(--reply-to-color);
    }
    &.posting-stats-legend-item-boosts {
      background-color: var(--reblog-color);
    }
  }
}

.account-container.skeleton {
  color: var(--outline-color);
}

.account-container .account-moved {
  animation: fade-in 0.3s both ease-in-out 0.3s;
  padding: 16px;
  background-color: var(--bg-color);
  position: absolute;
  top: 8px;
  inset-inline: 8px;
  z-index: 3;
  border: 1px solid var(--outline-color);
  box-shadow: 0 8px 16px var(--drop-shadow-color);
  border-radius: calc(16px - 8px);
  overflow: hidden;

  p {
    margin: 0 0 8px;
    padding: 0;
  }

  .account-block {
    background-color: var(--bg-faded-color);
    padding: 8px;
    border-radius: 8px;
    border: 1px solid var(--link-faded-color);

    &:hover {
      background-color: var(--link-bg-hover-color);
      border-color: var(--link-color);
    }

    b {
      color: var(--link-color);
    }
  }

  ~ * {
    /* pointer-events: none; */
    filter: grayscale(0.75) opacity(0.75);
  }
}

.account-container .header-banner {
  /* pointer-events: none; */
  vertical-align: top;
  aspect-ratio: 6 / 1;
  width: 100%;
  height: auto;
  object-fit: cover;
  /* mask fade out bottom of banner */
  mask-image: linear-gradient(
    to bottom,
    hsl(0, 0%, 0%) 0%,
    hsla(0, 0%, 0%, 0.987) 14%,
    hsla(0, 0%, 0%, 0.951) 26.2%,
    hsla(0, 0%, 0%, 0.896) 36.8%,
    hsla(0, 0%, 0%, 0.825) 45.9%,
    hsla(0, 0%, 0%, 0.741) 53.7%,
    hsla(0, 0%, 0%, 0.648) 60.4%,
    hsla(0, 0%, 0%, 0.55) 66.2%,
    hsla(0, 0%, 0%, 0.45) 71.2%,
    hsla(0, 0%, 0%, 0.352) 75.6%,
    hsla(0, 0%, 0%, 0.259) 79.6%,
    hsla(0, 0%, 0%, 0.175) 83.4%,
    hsla(0, 0%, 0%, 0.104) 87.2%,
    hsla(0, 0%, 0%, 0.049) 91.1%,
    hsla(0, 0%, 0%, 0.013) 95.3%,
    hsla(0, 0%, 0%, 0) 100%
  );
  margin-bottom: calc(-1 * var(--banner-overlap));
  user-select: none;
  -webkit-user-drag: none;
  opacity: 0;
  transition: opacity 0.3s ease-out;
}
.account-container .header-banner.loaded {
  opacity: 1;
}
.sheet .account-container .header-banner {
  border-start-start-radius: 16px;
  border-start-end-radius: 16px;
}
.account-container .header-banner.header-is-avatar {
  mask-image: linear-gradient(
    to bottom,
    hsl(0, 0%, 0%) 0%,
    hsla(0, 0%, 0%, 0.987) 8.1%,
    hsla(0, 0%, 0%, 0.951) 15.5%,
    hsla(0, 0%, 0%, 0.896) 22.5%,
    hsla(0, 0%, 0%, 0.825) 29%,
    hsla(0, 0%, 0%, 0.741) 35.3%,
    hsla(0, 0%, 0%, 0.648) 41.2%,
    hsla(0, 0%, 0%, 0.55) 47.1%,
    hsla(0, 0%, 0%, 0.45) 52.9%,
    hsla(0, 0%, 0%, 0.352) 58.8%,
    hsla(0, 0%, 0%, 0.259) 64.7%,
    hsla(0, 0%, 0%, 0.175) 71%,
    hsla(0, 0%, 0%, 0.104) 77.5%,
    hsla(0, 0%, 0%, 0.049) 84.5%,
    hsla(0, 0%, 0%, 0.013) 91.9%,
    hsla(0, 0%, 0%, 0) 100%
  );
  filter: blur(32px) saturate(3) opacity(0.5);
  pointer-events: none;
}
.account-container .header-banner:hover {
  animation: position-object 5s ease-in-out 1s 5;
}
.account-container .header-banner:active {
  mask-image: none;

  & + header {
    background-image: none;
  }

  & + header .avatar-container + * {
    transition: opacity 0.3s ease-in-out;
    opacity: 0 !important;
  }

  &,
  & + header .avatar-container {
    transition: filter 0.3s ease-in-out;
    filter: none !important;
  }

  & + header .avatar img {
    transition: border-radius 0.3s ease-in-out;
    border-radius: 8px;
  }
}

@media (min-height: 480px) {
  .account-container .header-banner:not(.header-is-avatar) {
    aspect-ratio: 3 / 1;
  }
}

.account-container header {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  text-shadow:
    -8px 0 12px -6px var(--bg-color),
    8px 0 12px -6px var(--bg-color),
    -8px 0 24px var(--header-color-3, --bg-color),
    8px 0 24px var(--header-color-4, --bg-color);
  animation: fade-in 0.3s both ease-in-out 0.1s;

  .avatar-container {
    filter: drop-shadow(-2px 0 4px var(--header-color-3, --bg-color))
      drop-shadow(2px 0 4px var(--header-color-4, --bg-color));
  }

  .avatar {
    /* box-shadow: -8px 0 24px var(--header-color-3, --bg-color),
    8px 0 24px var(--header-color-4, --bg-color); */
    /* overflow: initial; */

    &:not(.has-alpha) img {
      border-radius: inherit;
    }
  }
}

.account-container main > *:first-child {
  animation: fade-in 0.3s both ease-in-out 0.15s;
}
.account-container main > *:first-child ~ * {
  animation: fade-in 0.3s both ease-in-out 0.2s;
}

.account-container .account-block .account-block-acct {
  display: block;
  opacity: 0.7;
}

.private-note-tag {
  z-index: 1;
  appearance: none;
  display: inline-block;
  color: var(--private-note-text-color);
  background-color: var(--private-note-bg-color);
  border: 1px solid var(--private-note-border-color);
  padding: 4px;
  line-height: normal;
  font-size: smaller;
  border-radius: 0;
  align-self: center !important;
  /* clip a dog ear on top right */
  clip-path: polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 0 100%);
  &:dir(rtl) {
    /* top left */
    clip-path: polygon(4px 0, 100% 0, 100% 100%, 0 100%, 0 4px);
  }
  /* 4x4px square on top right */
  background-size: 4px 4px;
  background-repeat: no-repeat;
  background-position: top right;
  &:dir(rtl) {
    background-position: top left;
  }
  background-image: linear-gradient(
    to bottom,
    var(--private-note-border-color),
    var(--private-note-border-color)
  );
  transition: transform 0.15s ease-in-out;
  overflow-wrap: anywhere;

  span {
    color: inherit;
    opacity: 0.75;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    display: box;
    -webkit-box-orient: vertical;
    box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    text-align: start;
  }

  &:hover:not(:active) {
    filter: none !important;
    transform: rotate(-0.5deg) scale(1.05);

    span {
      opacity: 1;
    }
  }
}
.account-container .private-note {
  font-size: 90%;
  color: var(--text-insignificant-color);
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  padding: 12px;
  background-color: var(--bg-faded-color);
  display: flex;
  gap: 0.5em;
  align-items: center;

  b {
    font-size: 90%;
    text-transform: uppercase;
  }
  p {
    margin: 0;
    padding: 0;
  }
}

.account-container .stats {
  display: flex;
  /* flex-wrap: wrap; */
  column-gap: 24px;
  row-gap: 8px;
  /* opacity: 0.75; */
  font-size: 90%;
  background-color: var(--bg-faded-color);
  padding: 12px;
  /* border-radius: 16px; */
  line-height: 1.25;
  overflow-x: auto !important;
  justify-content: flex-start;
  position: relative;

  [tabindex='0']:is(:hover, :focus) {
    color: var(--text-color);
    cursor: pointer;
    text-decoration-color: var(--text-insignificant-color);
  }

  .stats-avatars-bunch {
    animation: appear 1s both ease-in-out;

    > *:not(:first-child) {
      margin: 0;
      margin-inline-start: -4px;
    }
  }
}
.timeline-start .account-container .stats {
  flex-wrap: wrap;
}
.account-container .stats > * {
  flex-shrink: 0;
  display: flex;
  gap: 0.5em;
}
.account-container .stats a:not(.insignificant) {
  color: inherit;
}
.account-container .stats a:hover {
  color: inherit;
}

.account-container footer {
  padding: 0 16px 16px;
}
.account-container .actions {
  /* margin-block: 8px; */
  display: flex;
  gap: 8px;
  justify-content: space-between;
  min-height: 2.5em;
  align-items: center;
}
.account-container .actions button {
  /* align-self: flex-end; */
}
.account-container .actions .buttons {
  display: flex;
  align-items: center;
}

.account-container .account-metadata-box {
  overflow: hidden;
  border-radius: 16px;
  display: block;
  text-decoration: none;

  & > * {
    margin-bottom: 2px;
    border-radius: 4px;
    overflow: hidden;
  }

  &:has(+ .account-metadata-box) {
    border-end-start-radius: 4px;
    border-end-end-radius: 4px;
  }

  + .account-metadata-box {
    border-start-start-radius: 4px;
    border-start-end-radius: 4px;
    border-end-start-radius: 16px;
    border-end-end-radius: 16px;
  }
}

.account-container .profile-metadata {
  display: flex;
  /* flex-wrap: wrap; */
  gap: 2px;
  overflow: hidden;
  overflow-x: auto;
}
.timeline-start .account-container .profile-metadata {
  flex-wrap: wrap;
}
.account-container .profile-field {
  min-width: 0;
  flex-grow: 1;
  font-size: 90%;
  background-color: var(--bg-faded-color);
  padding: 12px;
  border-radius: 4px;
  filter: saturate(0.75);
  line-height: 1.25;
  flex-shrink: 0;
  max-width: calc(100% - 12px - 2em);
}
.account-container .profile-field:only-child {
  max-width: 100%;
}
.timeline-start .account-container .profile-field {
  flex-shrink: 1;
  max-width: 100%;
}

.account-container :is(.note, .profile-field) .invisible {
  display: none;
}
.account-container :is(.note, .profile-field) .ellipsis::after {
  content: '…';
}

.account-container .profile-field b {
  font-size: 90%;
  color: var(--text-insignificant-color);
  text-transform: uppercase;
}
.account-container .profile-field b .icon {
  color: var(--green-color);
}
.account-container .profile-field p {
  margin: 0;
}

.account-container .common-followers {
  font-size: 90%;
  color: var(--text-insignificant-color);
  background-color: var(--bg-faded-color);
  padding: 8px 12px;
  margin: 0;
}

.timeline-start .account-container {
  border-bottom: 1px solid var(--outline-color);
  position: relative;
}
.timeline-start .account-container header {
  padding: 16px;
  animation: none;
}
.timeline-start .account-container main {
  padding: 1px 16px 16px;
}
.timeline-start .account-container main > * {
  animation: none;
}

.faux-header-bg {
  display: none;
}

@keyframes bye-banner {
  20% {
    filter: blur(0) opacity(1);
  }
  100% {
    filter: blur(16px) opacity(0.2);
  }
}
@keyframes surface-header {
  0% {
    border-bottom-color: transparent;
    box-shadow: none;
  }
  100% {
    border-bottom-color: var(--outline-color);
    box-shadow: 0 8px 16px -8px var(--drop-shadow-color);
  }
}
@keyframes shrink-avatar {
  0% {
    width: 64px;
    height: 64px;
  }
  100% {
    width: 2.5em;
    height: 2.5em;
  }
}
.sheet .account-container {
  border-radius: 16px 16px 0 0;
  overflow-x: hidden;
  max-height: 75vh;
  overscroll-behavior: none;
  scroll-timeline: --account-scroll;

  header {
    padding-bottom: 16px;
    position: sticky;
    top: 0;
    z-index: 2;
    background-image: linear-gradient(
      to bottom,
      transparent 30%,
      var(--bg-color) var(--banner-overlap),
      var(--bg-color) calc(100% - 8px),
      transparent
    );

    .account-block-content {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      line-clamp: 3;
      -webkit-line-clamp: 3;
    }
  }

  .faux-header-bg {
    display: block;
    height: var(--banner-overlap);
    position: sticky;
    top: 0;
    z-index: 1;
    background-color: var(--bg-color);
    margin-top: calc(-1 * var(--banner-overlap));
  }

  @supports ((animation-timeline: scroll()) and (animation-range: 0% 100%)) {
    .header-banner:not(.header-is-avatar):not(:hover):not(:active) {
      animation: bye-banner 1s linear both;
      animation-timeline: view();
      animation-range: contain 100% cover 100%;
    }

    header {
      background-image: linear-gradient(
        to bottom,
        transparent 30%,
        var(--bg-color) var(--banner-overlap)
      );
      border-bottom: 1px solid transparent;
      animation: surface-header 1s linear both;
      animation-timeline: --account-scroll;
      animation-range: 0 150px;
    }

    header .avatar {
      animation: shrink-avatar 1s linear both;
      animation-timeline: --account-scroll;
      animation-range: 0 150px;
    }
  }

  main {
    /* margin-top: -8px; */
    padding-top: 1px;
    padding-bottom: 16px;
  }

  footer {
    min-height: calc(40px + 16px);
    animation: slide-up 0.3s ease-out 0.3s both;
    position: sticky;
    bottom: 0;
    background-color: var(--bg-faded-blur-color);
    backdrop-filter: blur(16px) saturate(3);
    padding: 8px 16px;
    border-top: var(--hairline-width) solid var(--outline-color);
    padding-bottom: max(8px, env(safe-area-inset-bottom));
    box-shadow: 0 -8px 16px -8px var(--drop-shadow-color);
  }
}

@keyframes swoosh-bg-image {
  0% {
    background-position: -320px 0;
    opacity: 0.25;
  }
  100% {
    background-position: 0 0;
    opacity: 1;
  }
}
.account-container .posting-stats-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  color: inherit;
  background-color: var(--bg-faded-color);
  padding: 8px 12px;
  font-size: 90%;
  color: var(--text-insignificant-color);
  line-height: 1;
  vertical-align: text-top;
  border-radius: 4px;

  &:is(:hover, :focus-within) {
    color: var(--text-color);
    background-color: var(--link-bg-hover-color);
    filter: none !important;
  }

  .loader-container {
    margin: 0;
    opacity: 0.5;
    transform: scale(0.75);
  }
}

@keyframes wobble {
  0% {
    transform: rotate(-4deg);
  }
  100% {
    transform: rotate(4deg);
  }
}
@keyframes loading-spin {
  0% {
    transform: rotate(0deg) scale(0.75);
  }
  100% {
    transform: rotate(360deg) scale(0.75);
  }
}
.posting-stats-icon {
  display: inline-block;
  width: 24px;
  height: 8px;
  filter: opacity(0.75);
  animation: wobble 2s linear both infinite alternate !important;

  &.loading {
    animation: loading-spin 0.35s linear both infinite !important;
  }
}

#list-add-remove-container .list-add-remove {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 0;
  padding: 8px 0;
  list-style: none;
}
#list-add-remove-container .list-add-remove button {
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  text-align: start;
}
#list-add-remove-container .list-add-remove button .icon {
  opacity: 0.15;
}
#list-add-remove-container .list-add-remove button.checked {
  border-color: var(--green-color);
  font-weight: bold;
}
#list-add-remove-container .list-add-remove button.checked .icon {
  opacity: 1;
  color: var(--green-color);
}

@media (min-width: 40em) {
  .timeline-start .account-container {
    --banner-overlap: 77px;
    --item-radius: 16px;
    border: 1px solid var(--divider-color);
    margin: 16px 0;
    background-color: var(--bg-color);
    border-radius: var(--item-radius);
    overflow: hidden;
    /* box-shadow: 0px 1px var(--bg-blur-color), 0 0 64px var(--bg-color); */
    --shadow-offset: 16px;
    --shadow-blur: 32px;
    --shadow-spread: calc(var(--shadow-blur) * -0.75);
    box-shadow:
      calc(var(--shadow-offset) * -1) var(--shadow-offset) var(--shadow-blur)
      var(--shadow-spread) var(--header-color-1, var(--drop-shadow-color)),
      var(--shadow-offset) var(--shadow-offset) var(--shadow-blur)
      var(--shadow-spread) var(--header-color-2, var(--drop-shadow-color));
  }
  /* .timeline-start .account-container .header-banner {
    margin-bottom: -77px;
  } */
  .timeline-start .account-container header .account-block {
    font-size: 175%;
    /* margin-bottom: -8px; */
    line-height: 1.1;
    letter-spacing: -0.5px;
    mix-blend-mode: multiply;
    gap: 12px;

    .avatar-container {
      filter: drop-shadow(-8px 0 8px var(--header-color-3, --bg-color))
        drop-shadow(8px 0 8px var(--header-color-4, --bg-color));
    }

    .avatar {
      width: 112px !important;
      height: 112px !important;
    }
  }
}

#private-note-container {
  textarea {
    margin-top: 8px;
    width: 100%;
    resize: vertical;
    height: 33vh;
    min-height: 25vh;
    max-height: 50vh;
    color: var(--private-note-text-color);
    background-color: var(--private-note-bg-color);
    border: 1px solid var(--private-note-border-color);
    box-shadow: 0 2px 8px var(--drop-shadow-color);
    border-radius: 0;
    padding: 16px;
  }

  footer {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;

    * {
      vertical-align: middle;
    }
  }
}

#edit-profile-container {
  p {
    margin-block: 8px;
  }

  label {
    input:not([type='file']),
    textarea {
      display: block;
      width: 100%;
    }

    textarea {
      resize: vertical;
      min-height: 5em;
      max-height: 50vh;
    }
  }

  .edit-profile-media-container {
    margin-block: 8px;
  }

  .edit-profile-media-field {
    display: grid;
    grid-template-columns: 80px 2em 80px;
    gap: 8px;
    margin-top: 8px;
    align-items: center;
    justify-items: center;

    .icon {
      opacity: 0.75;
    }

    .edit-media {
      width: 80px;
      height: 80px;
      border-radius: 4px;
      overflow: hidden;
      border: 1px solid var(--outline-color);
      /* checkerboard background */
      background-image:
        linear-gradient(45deg, var(--img-bg-color) 25%, transparent 25%),
        linear-gradient(-45deg, var(--img-bg-color) 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, var(--img-bg-color) 75%),
        linear-gradient(-45deg, transparent 75%, var(--img-bg-color) 75%);
      background-size: 10px 10px;
      background-position:
        0 0,
        0 5px,
        5px -5px,
        -5px 0px;

      &:hover {
        box-shadow: 0 0 0 2px var(--link-light-color);
        cursor: pointer;
      }

      img {
        object-fit: contain;
        width: 100%;
        height: 100%;
        vertical-align: top;
      }
    }
  }

  table {
    width: 100%;

    th {
      text-align: start;
      color: var(--text-insignificant-color);
      font-weight: normal;
      font-size: 0.8em;
      text-transform: uppercase;
    }

    tbody tr td:first-child {
      width: 40%;
    }

    input {
      width: 100%;
    }
  }

  footer {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;

    * {
      vertical-align: middle;
    }
  }
}

.handle-info {
  .handle-handle {
    display: inline-block;
    margin-block: 5px;

    b {
      font-weight: 600;
      padding: 2px 4px;
      border-radius: 4px;
      display: inline-block;
      box-shadow: 0 0 0 5px var(--bg-blur-color);

      &.handle-username {
        color: var(--orange-fg-color);
        background-color: var(--orange-bg-color);
      }

      &.handle-server {
        color: var(--purple-fg-color);
        background-color: var(--purple-bg-color);
      }
    }
  }

  .handle-at {
    display: inline-block;
    margin-inline: -3px;
    position: relative;
    z-index: 1;
  }

  .handle-legend {
    margin-top: 0.25em;
  }

  .handle-legend-icon {
    overflow: hidden;
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 4px solid transparent;
    border-radius: 8px;
    background-clip: padding-box;

    &.username {
      background-color: var(--orange-fg-color);
      border-color: var(--orange-bg-color);
    }
    &.server {
      background-color: var(--purple-fg-color);
      border-color: var(--purple-bg-color);
    }
  }
}

.endorsements-container {
  padding: 16px;
  overflow-x: auto;
  overflow-y: hidden;
  /* background-image: linear-gradient(
    to bottom,
    var(--bg-faded-color),
    var(--bg-color) calc(16px + 1em)
  ); */
  background-image: radial-gradient(
    ellipse at top center,
    var(--bg-faded-color),
    var(--bg-color)
  );
  background-size: 100% 32px;
  background-repeat: no-repeat;
  mask-image: linear-gradient(
    var(--to-forward),
    transparent,
    black 16px calc(100% - 16px),
    transparent
  );

  h3 {
    margin: 0;
    padding: 0 0 8px;
    font-weight: normal;
    color: var(--text-insignificant-color);
    font-size: 85%;
    position: sticky;
    left: 0;
    text-transform: uppercase;
    font-weight: 600;

    > * {
      vertical-align: text-bottom;
    }
  }

  .endorsements {
    margin: 0;
    padding: 0;
    list-style: none;

    /* grid: 2 rows, infinite columns, no wrap */
    display: grid;
    grid-auto-flow: column;
    grid-template-rows: repeat(2, auto);
    grid-template-columns: repeat(9999, clamp(160px, 90%, 320px));
    white-space: nowrap;
    gap: 8px;

    &.expanded {
      grid-template-rows: repeat(3, auto);
    }

    > li {
      display: flex;
    }

    .account-block {
      overflow: hidden;
      flex-grow: 1;
      padding: 8px 16px;
      background-color: var(--bg-faded-color);
      background-image: linear-gradient(
        15deg,
        var(--link-bg-color),
        var(--bg-faded-color) 8px calc(100% - 16px),
        var(--bg-color)
      );
      &:dir(rtl) {
        background-image: linear-gradient(
          -15deg,
          var(--link-bg-color),
          var(--bg-faded-color) 16px calc(100% - 16px),
          var(--bg-color)
        );
      }
      /* box-shadow: inset 0 0 0 2px var(--bg-color); */
      border-radius: 8px;
      transition: all 0.15s ease-out;

      .avatar {
        transition: transform 0.15s ease-out;
      }

      .account-block-content {
        min-width: 0;
        mask-image: linear-gradient(var(--to-backward), transparent, black 16px);
      }

      .account-block-stats {
        flex-wrap: nowrap;

        > * {
          flex-shrink: 0;
        }
      }

      &:is(:hover, :focus-visible) {
        background-color: var(--link-bg-hover-color);
        box-shadow: inset 0 0 0 2px var(--link-light-color);

        .avatar {
          transform: rotate(15deg);
        }
      }

      &:active {
        transition-duration: 0s;
        filter: brightness(0.8);
      }

      .tag.minimal {
        background-color: var(--bg-color);
      }
    }
  }
}
