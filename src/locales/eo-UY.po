msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: eo\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-08-05 04:12\n"
"Last-Translator: \n"
"Language-Team: Esperanto\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: eo\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:141
msgid "Locked"
msgstr "Ŝlosita"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:163
msgid "Posts: {0}"
msgstr "Afiŝoj: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:168
msgid "Last posted: {0}"
msgstr "Laste afiŝita: {0}"

#: src/components/account-block.jsx:183
#: src/components/account-info.jsx:705
msgid "Automated"
msgstr "Aŭtomatigita"

#: src/components/account-block.jsx:190
#: src/components/account-info.jsx:710
#: src/components/status.jsx:748
msgid "Group"
msgstr "Grupo"

#: src/components/account-block.jsx:200
msgid "Mutual"
msgstr "Reciproka"

#: src/components/account-block.jsx:204
#: src/components/account-info.jsx:1907
msgid "Requested"
msgstr "Petita"

#: src/components/account-block.jsx:208
#: src/components/account-info.jsx:1898
msgid "Following"
msgstr "Sekvante"

#: src/components/account-block.jsx:212
#: src/components/account-info.jsx:1212
msgid "Follows you"
msgstr "Sekvas vin"

#: src/components/account-block.jsx:220
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, one {# sekvanto} other {# sekvantoj}}"

#: src/components/account-block.jsx:229
#: src/components/account-info.jsx:753
msgid "Verified"
msgstr "Kontrolita"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:244
#: src/components/account-info.jsx:892
msgid "Joined <0>{0}</0>"
msgstr "Aliĝis <0>{0}</0>"

#: src/components/account-info.jsx:64
msgid "Forever"
msgstr "Por ĉiam"

#: src/components/account-info.jsx:398
msgid "Unable to load account."
msgstr "Ne eblas ŝargi konton."

#: src/components/account-info.jsx:413
msgid "Go to account page"
msgstr "Iri al konta paĝo"

#: src/components/account-info.jsx:442
#: src/components/account-info.jsx:775
msgid "Followers"
msgstr "Sekvantoj"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:446
#: src/components/account-info.jsx:830
msgid "following.stats"
msgstr "Sekvatoj"

#: src/components/account-info.jsx:449
#: src/pages/account-statuses.jsx:495
#: src/pages/search.jsx:345
#: src/pages/search.jsx:492
msgid "Posts"
msgstr "Afiŝoj"

#: src/components/account-info.jsx:457
#: src/components/account-info.jsx:1268
#: src/components/compose.jsx:2819
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1998
#: src/components/status.jsx:2015
#: src/components/status.jsx:2140
#: src/components/status.jsx:2769
#: src/components/status.jsx:2772
#: src/pages/account-statuses.jsx:539
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1306
#: src/pages/trending.jsx:474
msgid "More"
msgstr "Pli"

#: src/components/account-info.jsx:469
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> indikis, ke ilia nova konto nun estas:"

#: src/components/account-info.jsx:614
#: src/components/account-info.jsx:1496
msgid "Handle copied"
msgstr "Tenilo kopiita"

#: src/components/account-info.jsx:617
#: src/components/account-info.jsx:1499
msgid "Unable to copy handle"
msgstr "Ne eblas kopii la tenilon"

#: src/components/account-info.jsx:623
#: src/components/account-info.jsx:1505
msgid "Copy handle"
msgstr "Kopii tenilon"

#: src/components/account-info.jsx:629
msgid "Go to original profile page"
msgstr "Malfermi la originalan profilpaĝon"

#: src/components/account-info.jsx:647
msgid "View profile image"
msgstr "Vidi rolfiguron"

#: src/components/account-info.jsx:665
msgid "View profile header"
msgstr "Vidi fonbildon"

#: src/components/account-info.jsx:681
#: src/components/account-info.jsx:1806
#: src/components/account-info.jsx:2332
msgid "Edit profile"
msgstr "Redakti la profilon"

#: src/components/account-info.jsx:700
msgid "In Memoriam"
msgstr "Memore"

#: src/components/account-info.jsx:782
#: src/components/account-info.jsx:840
msgid "This user has chosen to not make this information available."
msgstr "Ĉi tiu uzanto elektis ne disponebligi ĉi tiun informon."

#. placeholder {0}: shortenNumber(followersCount)
#. placeholder {1}: shortenNumber(followersCount)
#: src/components/account-info.jsx:802
msgid "{followersCount, plural, one {<0>{0}</0> Follower} other {<1>{1}</1> Followers}}"
msgstr ""

#. placeholder {0}: shortenNumber(followingCount)
#: src/components/account-info.jsx:846
msgid "{followingCount, plural, other {<0>{0}</0> Following}}"
msgstr ""

#. placeholder {0}: shortenNumber(statusesCount)
#. placeholder {1}: shortenNumber(statusesCount)
#: src/components/account-info.jsx:870
msgid "{statusesCount, plural, one {<0>{0}</0> Post} other {<1>{1}</1> Posts}}"
msgstr ""

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:917
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0} originalaj afiŝoj, {1} respondoj, {2} diskonigoj"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:933
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural, one {{1, plural, one {Lasta afiŝo en la lasta tago} other {Lasta afiŝo en la pasintaj {2} tagoj}}} other {{3, plural, one {Lastaj {4} afiŝoj en la lasta tago} other {Lastaj {5} afiŝoj en la pasintaj {6} tagoj}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:949
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {Lasta afiŝo en la pasinta(j) jaro(j)} other {Lastaj {1} afiŝoj en la pasinta(j) jaro(j)}}"

#: src/components/account-info.jsx:974
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "Originala"

#: src/components/account-info.jsx:978
#: src/components/status.jsx:2553
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1025
#: src/pages/status.jsx:1652
msgid "Replies"
msgstr "Respondoj"

#: src/components/account-info.jsx:982
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "Diskonigoj"

#: src/components/account-info.jsx:988
msgid "Post stats unavailable."
msgstr "Afiŝaj statistikoj ne disponeblaj."

#: src/components/account-info.jsx:1019
msgid "View post stats"
msgstr "Vidi afiŝo-statistikojn"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1216
msgid "Last post: <0>{0}</0>"
msgstr "Lasta afiŝo: <0>{0}</0>"

#: src/components/account-info.jsx:1230
msgid "Muted"
msgstr "Silentigita"

#: src/components/account-info.jsx:1235
msgid "Blocked"
msgstr "Blokita"

#: src/components/account-info.jsx:1244
msgid "Private note"
msgstr "Privata noto"

#: src/components/account-info.jsx:1301
msgid "Mention <0>@{username}</0>"
msgstr "Mencii <0>@{username}</0>"

#: src/components/account-info.jsx:1313
msgid "Translate bio"
msgstr "Traduki biografion"

#: src/components/account-info.jsx:1324
msgid "Edit private note"
msgstr "Redakti privatan noton"

#: src/components/account-info.jsx:1324
msgid "Add private note"
msgstr "Aldoni privatan noton"

#: src/components/account-info.jsx:1344
msgid "Notifications enabled for @{username}'s posts."
msgstr "Ŝaltitaj sciigoj por afiŝoj de @{username}."

#: src/components/account-info.jsx:1345
msgid " Notifications disabled for @{username}'s posts."
msgstr " Malŝaltitaj sciigoj por afiŝoj de @{username}."

#: src/components/account-info.jsx:1357
msgid "Disable notifications"
msgstr "Malŝalti sciigojn"

#: src/components/account-info.jsx:1358
msgid "Enable notifications"
msgstr "Ŝalti sciigojn"

#: src/components/account-info.jsx:1375
msgid "Boosts from @{username} enabled."
msgstr "Diskonigoj de @{username} ŝaltitaj."

#: src/components/account-info.jsx:1376
msgid "Boosts from @{username} disabled."
msgstr "Diskonigoj de @{username} malŝaltitaj."

#: src/components/account-info.jsx:1387
msgid "Disable boosts"
msgstr "Malŝalti diskonigojn"

#: src/components/account-info.jsx:1387
msgid "Enable boosts"
msgstr "Ŝalti diskonigojn"

#: src/components/account-info.jsx:1406
msgid "@{username} is no longer featured on your profile."
msgstr ""

#: src/components/account-info.jsx:1416
msgid "@{username} is now featured on your profile."
msgstr ""

#: src/components/account-info.jsx:1424
msgid "Unable to unfeature @{username} on your profile."
msgstr ""

#: src/components/account-info.jsx:1428
msgid "Unable to feature @{username} on your profile."
msgstr ""

#: src/components/account-info.jsx:1437
msgid "Don't feature on profile"
msgstr ""

#: src/components/account-info.jsx:1438
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "Montri en profilo"

#: src/components/account-info.jsx:1447
msgid "Show featured profiles"
msgstr "Montri elstarajn profilojn"

#: src/components/account-info.jsx:1462
#: src/components/account-info.jsx:1472
#: src/components/account-info.jsx:2078
msgid "Add/Remove from Lists"
msgstr "Aldoni/Forigi el Listoj"

#: src/components/account-info.jsx:1522
#: src/components/status.jsx:1420
msgid "Link copied"
msgstr "Ligilo kopiita"

#: src/components/account-info.jsx:1525
#: src/components/status.jsx:1423
msgid "Unable to copy link"
msgstr "Ne eblas kopii la ligilon"

#: src/components/account-info.jsx:1531
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1429
#: src/components/status.jsx:3547
msgid "Copy"
msgstr "Kopii"

#: src/components/account-info.jsx:1546
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1445
msgid "Sharing doesn't seem to work."
msgstr "Kunhavigo ŝajnas ne funkcii."

#: src/components/account-info.jsx:1552
#: src/components/status.jsx:1451
msgid "Share…"
msgstr "Diskonigi…"

#: src/components/account-info.jsx:1572
msgid "Unmuted @{username}"
msgstr "Nesilentigita @{username}"

#: src/components/account-info.jsx:1584
msgid "Unmute <0>@{username}</0>"
msgstr "Ne plu silentigi <0>@{username}</0>"

#: src/components/account-info.jsx:1600
msgid "Mute <0>@{username}</0>…"
msgstr "Silentigi <0>@{username}</0>…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1632
msgid "Muted @{username} for {0}"
msgstr "Silentigita @{username} dum {0}"

#: src/components/account-info.jsx:1644
msgid "Unable to mute @{username}"
msgstr "Ne eblas silentigi @{username}"

#: src/components/account-info.jsx:1665
msgid "Remove <0>@{username}</0> from followers?"
msgstr "Ĉu forigi <0>@{username}</0> el sekvantoj?"

#: src/components/account-info.jsx:1685
msgid "@{username} removed from followers"
msgstr "@{username} forigita de sekvantoj"

#: src/components/account-info.jsx:1697
msgid "Remove follower…"
msgstr "Forigi sekvanton…"

#: src/components/account-info.jsx:1708
msgid "Block <0>@{username}</0>?"
msgstr "Ĉu bloki <0>@{username}</0>?"

#: src/components/account-info.jsx:1732
msgid "Unblocked @{username}"
msgstr "Malblokita @{username}"

#: src/components/account-info.jsx:1740
msgid "Blocked @{username}"
msgstr "Blokita @{username}"

#: src/components/account-info.jsx:1748
msgid "Unable to unblock @{username}"
msgstr "Ne eblas malbloki @{username}"

#: src/components/account-info.jsx:1750
msgid "Unable to block @{username}"
msgstr "Ne eblas bloki @{username}"

#: src/components/account-info.jsx:1760
msgid "Unblock <0>@{username}</0>"
msgstr "Malbloki <0>@{username}</0>"

#: src/components/account-info.jsx:1769
msgid "Block <0>@{username}</0>…"
msgstr "Bloki <0>@{username}</0>…"

#: src/components/account-info.jsx:1786
msgid "Report <0>@{username}</0>…"
msgstr "Raporti <0>@{username}</0>…"

#: src/components/account-info.jsx:1842
msgid "Withdraw follow request?"
msgstr "Ĉu nuligi peton por sekvado?"

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1843
msgid "Unfollow @{0}?"
msgstr "Ĉu ĉesi sekvi @{0}?"

#: src/components/account-info.jsx:1901
msgid "Unfollow…"
msgstr "Ĉesi sekvi…"

#: src/components/account-info.jsx:1910
msgid "Withdraw…"
msgstr "Nuligi…"

#: src/components/account-info.jsx:1917
#: src/components/account-info.jsx:1921
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "Sekvi"

#: src/components/account-info.jsx:2018
#: src/components/account-info.jsx:2073
#: src/components/account-info.jsx:2207
#: src/components/account-info.jsx:2327
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:892
#: src/components/compose.jsx:2775
#: src/components/compose.jsx:3255
#: src/components/compose.jsx:3464
#: src/components/compose.jsx:3694
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:118
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3271
#: src/components/status.jsx:3511
#: src/components/status.jsx:4020
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1393
msgid "Close"
msgstr "Fermi"

#: src/components/account-info.jsx:2023
msgid "Translated Bio"
msgstr "Tradukita biografio"

#: src/components/account-info.jsx:2118
msgid "Unable to remove from list."
msgstr "Ne eblas forigi el listo."

#: src/components/account-info.jsx:2119
msgid "Unable to add to list."
msgstr "Ne eblas aldoni al listo."

#: src/components/account-info.jsx:2138
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "Ne eblas ŝargi listojn."

#: src/components/account-info.jsx:2142
msgid "No lists."
msgstr "Ne estas listoj."

#: src/components/account-info.jsx:2153
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "Nova listo"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2212
msgid "Private note about <0>@{0}</0>"
msgstr "Personaj notoj pri <0>@{0}</0>"

#: src/components/account-info.jsx:2242
msgid "Unable to update private note."
msgstr "Ne eblas ĝisdatigi la privatan noton."

#: src/components/account-info.jsx:2265
#: src/components/account-info.jsx:2563
msgid "Cancel"
msgstr "Nuligi"

#: src/components/account-info.jsx:2270
msgid "Save & close"
msgstr "Konservi kaj fermi"

#: src/components/account-info.jsx:2387
msgid "Unable to update profile."
msgstr "Ne eblas ĝisdatigi la profilon."

#: src/components/account-info.jsx:2394
msgid "Header picture"
msgstr "Kapa bildo"

#: src/components/account-info.jsx:2446
msgid "Profile picture"
msgstr "Profilbildo"

#: src/components/account-info.jsx:2498
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "Nomo"

#: src/components/account-info.jsx:2511
msgid "Bio"
msgstr "Sinprezento"

#: src/components/account-info.jsx:2524
msgid "Extra fields"
msgstr "Profilaj metadatumoj"

#: src/components/account-info.jsx:2530
msgid "Label"
msgstr "Etikedo"

#: src/components/account-info.jsx:2533
msgid "Content"
msgstr "Enhavo"

#: src/components/account-info.jsx:2566
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "Konservi"

#: src/components/account-info.jsx:2620
msgid "username"
msgstr "uzantnomo"

#: src/components/account-info.jsx:2624
msgid "server domain name"
msgstr "servila domajna nomo"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2690
msgid "Profiles featured by @{0}"
msgstr ""

#: src/components/account-info.jsx:2716
msgid "No featured profiles."
msgstr "Ne estas elstaraj profiloj."

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "Ŝtelreĝimo estis malŝaltita"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "Ŝtelreĝimo estis ŝaltita"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "Hejmo"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "Redakti"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr "Planitaj afiŝoj"

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr "Aldonu al fadeno"

#: src/components/compose.jsx:212
msgid "Take photo or video"
msgstr "Fari foton aŭ filmeton"

#: src/components/compose.jsx:213
msgid "Add media"
msgstr "Aldoni plurmedion"

#: src/components/compose.jsx:214
msgid "Add custom emoji"
msgstr "Aldoni propran emoĝion"

#: src/components/compose.jsx:215
msgid "Add GIF"
msgstr "Aldoni GIF-movbildon"

#: src/components/compose.jsx:216
msgid "Add poll"
msgstr "Aldoni balotenketon"

#: src/components/compose.jsx:217
msgid "Schedule post"
msgstr "Planu afiŝon"

#: src/components/compose.jsx:417
msgid "You have unsaved changes. Discard this post?"
msgstr "Vi havas nekonservitajn ŝanĝojn. Ĉu forĵeti ĉi tiun afiŝon?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:655
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr "{0, plural, one {La dosiero {1} ne estas subtenata.} other {La dosieroj {2} ne estas subtenataj.}}"

#: src/components/compose.jsx:665
#: src/components/compose.jsx:683
#: src/components/compose.jsx:1795
#: src/components/compose.jsx:1930
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, one {Vi povas nur kunsendi ĝis 1 dosieron.} other {Vi povas nur kunsendi ĝis # dosierojn.}}"

#: src/components/compose.jsx:873
msgid "Pop out"
msgstr "Foriri"

#: src/components/compose.jsx:880
msgid "Minimize"
msgstr "Minimumigi"

#: src/components/compose.jsx:916
msgid "Looks like you closed the parent window."
msgstr "Ŝajnas, ke vi fermis la patran fenestron."

#: src/components/compose.jsx:923
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Ŝajnas, ke vi jam havas redaktan fenestron malfermita en la ĉefa fenestro kaj vi nun publikigas. Bonvolu atendi, ke ĝi estos publikigita kaj provu denove poste."

#: src/components/compose.jsx:928
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Ŝajnas, ke vi jam havas redaktan fenestron malfermitan en la ĉefa fenestro. Enirante ĉi tiun fenestron forĵetos la ŝanĝojn kiujn vi faris. Ĉu daŭrigi?"

#: src/components/compose.jsx:971
msgid "Pop in"
msgstr "Montri"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:981
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "Respondante al la afiŝo de @{0} (<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:991
msgid "Replying to @{0}’s post"
msgstr "Respondante al la afiŝo de @{0}"

#: src/components/compose.jsx:1004
msgid "Editing source post"
msgstr "Redaktante la originalan afiŝon"

#: src/components/compose.jsx:1057
msgid "Poll must have at least 2 options"
msgstr "Balotenketo devas havi almenaŭ 2 eblojn"

#: src/components/compose.jsx:1061
msgid "Some poll choices are empty"
msgstr "Iuj elektoj de balotado estas malplenaj"

#: src/components/compose.jsx:1074
msgid "Some media have no descriptions. Continue?"
msgstr "Iuj plurmedioj ne havas priskribojn. Ĉu daŭrigi?"

#: src/components/compose.jsx:1126
msgid "Attachment #{i} failed"
msgstr "Eraro aldonante #{i}"

#: src/components/compose.jsx:1222
#: src/components/status.jsx:2328
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "Enhavaverto"

#: src/components/compose.jsx:1238
msgid "Content warning or sensitive media"
msgstr "Enhavaverto aŭ sentema plurmedio"

#: src/components/compose.jsx:1274
#: src/components/status.jsx:99
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "Publika"

#: src/components/compose.jsx:1279
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:100
msgid "Local"
msgstr "Loka"

#: src/components/compose.jsx:1283
#: src/components/status.jsx:101
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "Nelistigita"

#: src/components/compose.jsx:1286
#: src/components/status.jsx:102
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "Nur sekvantoj"

#: src/components/compose.jsx:1289
#: src/components/status.jsx:103
#: src/components/status.jsx:2204
msgid "Private mention"
msgstr "Privata mencio"

#: src/components/compose.jsx:1298
msgid "Post your reply"
msgstr "Afiŝi vian respondon"

#: src/components/compose.jsx:1300
msgid "Edit your post"
msgstr "Redakti vian afiŝon"

#: src/components/compose.jsx:1301
msgid "What are you doing?"
msgstr "Kion vi pensas?"

#: src/components/compose.jsx:1380
msgid "Mark media as sensitive"
msgstr "Aldoni averton de enhavo"

#: src/components/compose.jsx:1417
msgid "Posting on <0/>"
msgstr "Afiŝi la <0/>"

#: src/components/compose.jsx:1448
#: src/components/compose.jsx:3313
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "Aldoni"

#: src/components/compose.jsx:1676
msgid "Schedule"
msgstr "Plani"

#: src/components/compose.jsx:1678
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1192
#: src/components/status.jsx:1978
#: src/components/status.jsx:1979
#: src/components/status.jsx:2673
msgid "Reply"
msgstr "Respondi"

#: src/components/compose.jsx:1680
msgid "Update"
msgstr "Ĝisdatigi"

#: src/components/compose.jsx:1681
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Afiŝi"

#: src/components/compose.jsx:1807
msgid "Downloading GIF…"
msgstr "Elŝutante movbildon…"

#: src/components/compose.jsx:1835
msgid "Failed to download GIF"
msgstr "Malsukcesis elŝuti movbildon"

#: src/components/compose.jsx:2060
#: src/components/compose.jsx:2153
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "Pli…"

#: src/components/compose.jsx:2589
msgid "Uploaded"
msgstr "Ĝisdatigita"

#: src/components/compose.jsx:2602
msgid "Image description"
msgstr "Priskribo de bildo"

#: src/components/compose.jsx:2603
msgid "Video description"
msgstr "Priskribo de filmeto"

#: src/components/compose.jsx:2604
msgid "Audio description"
msgstr "Priskribo de aŭdaĵo"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2639
#: src/components/compose.jsx:2659
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "Dosiergrandeco estas tro granda. Alŝuto eble renkontos problemojn. Provu redukti la dosiergrandecon de {0} al {1} ​​aŭ malpli."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2651
#: src/components/compose.jsx:2671
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr "Dimensio estas tro granda. Alŝuto eble renkontos problemojn. Provu redukti dimension de {0}x{1}px al {2}x{3}px."

#: src/components/compose.jsx:2679
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "Bildrapido estas tro alta. Alŝuto eble renkontos problemojn."

#: src/components/compose.jsx:2739
#: src/components/compose.jsx:2989
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "Forigi"

#: src/components/compose.jsx:2756
#: src/compose.jsx:84
msgid "Error"
msgstr "Eraro"

#: src/components/compose.jsx:2781
msgid "Edit image description"
msgstr "Redakti bildopriskribon"

#: src/components/compose.jsx:2782
msgid "Edit video description"
msgstr "Redakti videopriskribon"

#: src/components/compose.jsx:2783
msgid "Edit audio description"
msgstr "Redakti aŭdpriskribon"

#: src/components/compose.jsx:2828
#: src/components/compose.jsx:2877
msgid "Generating description. Please wait…"
msgstr "Kreante priskribon. Bonvolu atendi…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2848
msgid "Failed to generate description: {0}"
msgstr "Malsukcesis generi priskribon: {0}"

#: src/components/compose.jsx:2849
msgid "Failed to generate description"
msgstr "Malsukcesis krei priskribon"

#: src/components/compose.jsx:2861
#: src/components/compose.jsx:2867
#: src/components/compose.jsx:2913
msgid "Generate description…"
msgstr "Krei priskribon…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2900
msgid "Failed to generate description{0}"
msgstr "Malsukcesis generi priskribon{0}"

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2915
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>— eksperimenta</0>"

#: src/components/compose.jsx:2934
msgid "Done"
msgstr "Farite"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2970
msgid "Choice {0}"
msgstr "Elekti {0}"

#: src/components/compose.jsx:3017
msgid "Multiple choices"
msgstr "Multoblaj elektoj"

#: src/components/compose.jsx:3020
msgid "Duration"
msgstr "Daŭro"

#: src/components/compose.jsx:3051
msgid "Remove poll"
msgstr "Forigi balotenketon"

#: src/components/compose.jsx:3272
msgid "Search accounts"
msgstr "Serĉi kontojn"

#: src/components/compose.jsx:3326
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "Eraro dum ŝargo de kontoj"

#: src/components/compose.jsx:3470
msgid "Custom emojis"
msgstr "Propraj emoĝioj"

#: src/components/compose.jsx:3490
msgid "Search emoji"
msgstr "Serĉi emoĝion"

#: src/components/compose.jsx:3521
msgid "Error loading custom emojis"
msgstr "Eraro dum ŝarĝo de propraj emoĝioj"

#: src/components/compose.jsx:3532
msgid "Recently used"
msgstr "Ofte uzataj"

#: src/components/compose.jsx:3533
msgid "Others"
msgstr "Aliaj"

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3571
msgid "{0} more…"
msgstr "{0} pli…"

#: src/components/compose.jsx:3709
msgid "Search GIFs"
msgstr "Serĉi GIF-movbildojn"

#: src/components/compose.jsx:3724
msgid "Powered by GIPHY"
msgstr "Funkciigita de GIPHY"

#: src/components/compose.jsx:3732
msgid "Type to search GIFs"
msgstr "Tajpi por serĉi movbildojn"

#: src/components/compose.jsx:3830
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "Antaŭa"

#: src/components/compose.jsx:3848
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "Sekva"

#: src/components/compose.jsx:3865
msgid "Error loading GIFs"
msgstr "Eraro dum ŝargo de movbildoj"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "Nesenditaj malnetoj"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "Ŝajnas, ke vi havas nesenditajn malnetojn. Ni daŭrigu kie vi ĉesis."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "Ĉu forigi ĉi tiun malneton?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "Eraro dum forigo de malneto! Bonvolu provi denove."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1595
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "Forigi…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "Eraro ricevi respondan staton!"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "Ĉu forigi ĉiujn malnetojn?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "Eraro dum forigo de malnetoj! Bonvolu provi denove."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "Forigi ĉiujn…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "Neniuj malnetoj trovitaj."

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "Balotenketo"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:375
msgid "Media"
msgstr "Plurmedio"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "Malfermi en nova fenestro"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "Akcepti"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "Malakcepti"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "Akceptita"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "Malakceptita"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:335
#: src/pages/search.jsx:368
msgid "Accounts"
msgstr "Kontoj"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:562
#: src/pages/status.jsx:1426
msgid "Show more…"
msgstr "Montri pli…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:567
msgid "The end."
msgstr "La fino."

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "Nenio por montri"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "Fulmoklavoj"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "Helpo por fulmoklavoj"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "Sekva afiŝo"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "Antaŭa afiŝo"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "Salti karuselon al la sekva afiŝo"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Ŝovo</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "Salti karuselon al la antaŭa afiŝo"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Ŝovo</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "Ŝargi novajn afiŝojn"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "Malfermi detalojn pri afiŝo"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Enen-klavo</0> aŭ <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "Vastigi enhavaverton aŭ<0/>ŝalti vastigitan/kolapsitan fadenon"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "Fermi afiŝon aŭ dialogojn"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc-klavo</0> aŭ <1>Retropaŝo</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "Enfokusigu la kolumnon en plurkolumna reĝimo"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> ĝis <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr "Enfokusigu la sekvan kolumnon en plurkolumna reĝimo"

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr "Enfokusigu la antaŭan kolumnon en plurkolumna reĝimo"

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "Redakti novan afiŝon"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "Redakti novan afiŝon (nova fenestro)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Ŝovo</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "Sendi afiŝon"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Stirklavo</0> + <1>Enen-klavo</1> aŭ <2>⌘</2> + <3>Enen-klavo</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:204
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:47
#: src/pages/search.jsx:317
msgid "Search"
msgstr "Serĉi"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "Respondi (nova fenestro)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Ŝovo</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "Ŝati (Stemulo)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> aŭ <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1200
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
#: src/components/status.jsx:2724
msgid "Boost"
msgstr "Diskonigi"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Ŝovo</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
#: src/components/status.jsx:2749
msgid "Bookmark"
msgstr "Legosigni"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "Baskuligi la ŝtelreĝimon"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Ŝovo</0> + <1>Alt-klavo</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "Redakti liston"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "Ne eblas redakti liston."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "Ne eblas krei liston."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "Montri respondoj al listmembroj"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "Montri respondojn al homoj, kiujn mi sekvas"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "Ne montri respondojn"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "Kaŝi afiŝojn en ĉi tiu listo de Hejmo/Sekvatoj"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "Krei"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "Ĉu forigi ĉi tiun liston?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "Ne eblas forigi liston."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr "Afiŝoj en ĉi tiu listo ne aperos en Hejmo/Sekvatoj"

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "Priskribo de plurmedio"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1306
#: src/components/status.jsx:1315
#: src/components/translation-block.jsx:239
msgid "Translate"
msgstr "Traduki"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1334
msgid "Speak"
msgstr "Paroli"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "Malfermi originalan plurmedion en nova fenestro"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "Malfermi originalan plurmedion"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "Provante priskribi bildon. Bonvolu atendi…"

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "Malsukcesis priskribi la bildon"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "Priskribi la bildon…"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "Vidi afiŝon"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "Sentema plurmedio"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "Filtrila: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3850
#: src/components/status.jsx:3946
#: src/components/status.jsx:4024
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "Filtrila"

#: src/components/media.jsx:479
msgid "Open file"
msgstr "Malfermi dosieron"

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr "Planita afiŝo"

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "Afiŝo publikigita. Rigardu."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr "Planita respondo"

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "Respondo publikigita. Rigardu."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "Afiŝo ĝisdatigita. Rigardu."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "Menuo"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "Ĉu reŝargi la paĝon nun por ĝisdatigi?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "Nova ĝisdatigo disponebla…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr "Sekvatoj"

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "Aktualiĝi"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "Mencioj"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "Sciigoj"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "Nova"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "Profilo"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "Legosignoj"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "Stemuloj"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "Sekvataj kradvortoj"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:341
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "Filtriloj"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "Silentigitaj uzantoj"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "Silentigitaj uzantoj…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "Blokitaj uzantoj"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "Blokitaj uzantoj…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "Kontoj…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:199
#: src/pages/status.jsx:925
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "Ensaluti"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "Populara"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "Fratara"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "Ŝparvojoj / Kolumnoj…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "Agordoj…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "Listoj"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "Ĉiuj listoj"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "Sciigo"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "Ĉi tiu sciigo estas de via alia konto."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "Vidi ĉiujn sciigojn"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} reagis al via afiŝo kun {emojiObject}"

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} publikigis afiŝon."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} diskonigis vian respondon.} other {{account} diskonigis vian afiŝon.}}} other {{account} diskonigis {postsCount} de viaj afiŝoj.}}} other {{postType, select, reply {<0><1>{0}</1> homoj</0> diskonigis vian respondon.} other {<2><3>{1}</3> homoj</2> diskonigis vian afiŝon.}}}}"

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, =1 {{account} sekvis vin.} other {<0><1>{0}</1> homoj</0> sekvis vin.}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} petis sekvi vin."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} ŝatis vian respondon.} other {{account} ŝatis vian afiŝon.}}} other {{account} ŝatis {postsCount} de viaj afiŝoj.}}} other {{postType, select, reply {<0><1>{0}</1> homoj</0> ŝatis vian respondon.} other {<2><3>{1}</3> homoj</2> ŝatis vian afiŝon.}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "Balotenketo, kiun vi voĉdonis aŭ kreis, finiĝis."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "Balotenketo, kiun vi kreis, finiĝis."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "Balotenketo, pri kiu vi voĉdonis, finiĝis."

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "Afiŝo, kun kiu vi interagis, estis redaktita."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} diskonigis kaj ŝatis vian respondon.} other {{account} diskonigis kaj ŝatis vian afiŝon.}}} other {{account} diskonigis kaj ŝatis {postsCount} de viaj afiŝoj.}}} other {{postType, select, reply {<0><1>{0}</1> homoj</0> diskonigis kaj ŝatis vian respondon.} other {<2><3>{1}</3> homoj</2> diskonigis kaj ŝatis vian afiŝon.}}}}"

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} aliĝis."

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} raportis {targetAccount}"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "Perditaj konektoj kun <0>{name}</0>."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "Moderiga averto"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr "Via #Wrapstodon {year} estas ĉi tie!"

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "Administranto de <0>{from}</0> malakceptis <1>{targetName}</1>, kio signifas, ke vi ne plu povas ricevi ĝisdatigojn de ili aŭ interagi kun ili."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "Administranto de <0>{from}</0> blokis <1>{targetName}</1>. Afektitaj sekvantoj: {followersCount}, Sekvantoj: {followingCount}."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "Vi blokis <0>{targetName}</0>. Forigitaj sekvantoj: {followersCount}, sekvantoj: {followingCount}."

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "Via konto ricevis moderan averton."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "Via konto estas malŝaltita."

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "Kelkaj el viaj afiŝoj estis markitaj kiel sentemaj."

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "Kelkaj el viaj afiŝoj estis forigitaj."

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "Viaj afiŝoj estos markitaj kiel sentemaj ekde nun."

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "Via konto estis limigita."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "Via konto estas malakceptita."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[Nekonata tipo de sciigo: {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1277
#: src/components/status.jsx:1287
msgid "Boosted/Liked by…"
msgstr "Diskonigita/Ŝatita de…"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "Ŝatita de…"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "Diskonigita de…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "Sekvita de…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "Lerni pli <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr "Vidu #Wrapstodon"

#: src/components/notification.jsx:801
#: src/components/status.jsx:486
msgid "Read more →"
msgstr "Legi pli →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "Elektita"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr "{optionVotesCount, plural, one {# voĉdono} other {# voĉdonoj}}"

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "Kaŝi rezultojn"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "Voĉdoni"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1295
#: src/pages/status.jsx:1318
msgid "Refresh"
msgstr "Aktualigi"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "Montri rezultojn"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> voĉdono} other {<1>{1}</1> voĉdonoj}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, one {<0>{0}</0> voĉdonanto} other {<1>{1}</1> voĉdonantoj}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "Finita <0/>"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "Finita"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "Finante <0/>"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "Finante"

#: src/components/recent-searches.jsx:27
msgid "Cleared recent searches"
msgstr ""

#: src/components/recent-searches.jsx:49
#: src/components/search-form.jsx:328
msgid "Recent searches"
msgstr ""

#: src/components/recent-searches.jsx:59
msgid "Clear all"
msgstr ""

#: src/components/recent-searches.jsx:94
#: src/pages/account-statuses.jsx:334
msgid "Clear"
msgstr "Malplenigi"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0}s"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0}m"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0}h"

#: src/components/report-modal.jsx:30
msgid "Spam"
msgstr "Trudaĵo"

#: src/components/report-modal.jsx:31
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "Trompaj ligiloj, falsa engaĝiĝo aŭ ripetaj respondoj"

#: src/components/report-modal.jsx:34
msgid "Illegal"
msgstr "Kontraŭleĝa"

#: src/components/report-modal.jsx:35
msgid "Violates the law of your or the server's country"
msgstr "Malobservas la leĝon de via aŭ de la servilo lando"

#: src/components/report-modal.jsx:38
msgid "Server rule violation"
msgstr "Malobservo de servila regulo"

#: src/components/report-modal.jsx:39
msgid "Breaks specific server rules"
msgstr "Malobservas specifajn servilajn regulojn"

#: src/components/report-modal.jsx:40
msgid "Violation"
msgstr "Malobservo"

#: src/components/report-modal.jsx:43
msgid "Other"
msgstr "Alia"

#: src/components/report-modal.jsx:44
msgid "Issue doesn't fit other categories"
msgstr "La problemo ne taŭgas en aliaj kategorioj"

#: src/components/report-modal.jsx:111
msgid "Report Post"
msgstr "Raporti afiŝon"

#: src/components/report-modal.jsx:111
msgid "Report @{username}"
msgstr "Raporti @{username}"

#: src/components/report-modal.jsx:147
msgid "Pending review"
msgstr "Atendante revizion"

#: src/components/report-modal.jsx:189
msgid "Post reported"
msgstr "Afiŝo raportita"

#: src/components/report-modal.jsx:189
msgid "Profile reported"
msgstr "Raportita profilo"

#: src/components/report-modal.jsx:197
msgid "Unable to report post"
msgstr "Ne eblas raporti afiŝon"

#: src/components/report-modal.jsx:198
msgid "Unable to report profile"
msgstr "Ne eblas raporti profilon"

#: src/components/report-modal.jsx:206
msgid "What's the issue with this post?"
msgstr "Kio estas la problema kun ĉi tiu afiŝo?"

#: src/components/report-modal.jsx:207
msgid "What's the issue with this profile?"
msgstr "Kio estas la problemo kun ĉi tiu profilo?"

#: src/components/report-modal.jsx:276
msgid "Additional info"
msgstr "Pliaj informoj"

#: src/components/report-modal.jsx:299
msgid "Forward to <0>{domain}</0>"
msgstr "Sendi al <0>{domain}</0>"

#: src/components/report-modal.jsx:309
msgid "Send Report"
msgstr "Sendi Raporton"

#: src/components/report-modal.jsx:318
msgid "Muted {username}"
msgstr "Silentigita {username}"

#: src/components/report-modal.jsx:321
msgid "Unable to mute {username}"
msgstr "Ne eblas silentigi {username}"

#: src/components/report-modal.jsx:326
msgid "Send Report <0>+ Mute profile</0>"
msgstr "Sendi raporton <0>+ Silentigi profilon</0>"

#: src/components/report-modal.jsx:337
msgid "Blocked {username}"
msgstr "Blokita {username}"

#: src/components/report-modal.jsx:340
msgid "Unable to block {username}"
msgstr "Ne eblas bloki {username}"

#: src/components/report-modal.jsx:345
msgid "Send Report <0>+ Block profile</0>"
msgstr "Sendi raporton <0>+ Bloki profilon</0>"

#: src/components/search-form.jsx:18
msgid "Posts with <0>{query}</0>"
msgstr "Afiŝoj kun <0>{query}</0>"

#: src/components/search-form.jsx:26
msgid "Accounts with <0>{query}</0>"
msgstr "Kontoj kun <0>{query}</0>"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:34
msgid "Posts tagged with <0>#{0}</0>"
msgstr "Afiŝoj etikeditaj kun <0>#{0}</0>"

#: src/components/search-form.jsx:43
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>‒ kontoj, kradvortoj kaj afiŝoj</0>"

#: src/components/search-form.jsx:135
msgid "Look up <0>{query}</0>"
msgstr "Serĉi <0>{query}</0>"

#: src/components/search-form.jsx:361
#: src/pages/home.jsx:251
msgid "See all"
msgstr "Vidi ĉion"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "Hejmo / Sekvatoj"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "Publika (Loka / Fratara)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "Konto"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "Kradvorto"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "Listo ID"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "Nur loka"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:203
msgid "Instance"
msgstr "Nodo"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "Nedeviga, ekz. mastodon.social"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "Serĉi terminon"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "Nedeviga, krom se por plurkolumna reĝimo"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "ekz. PixelArt (Maksimumo 5, spac-separata)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "Nur plurmedio"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "Ŝparvojoj"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "beta"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "Indiki liston de ŝparvojoj, kiuj aperos kiel:"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "Flosanta butono"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "Langetaro/Menubreto"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "Plurkolumno"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "Ne disponebla en nuna vidoreĝimo"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "Moviĝi supren"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "Moviĝi malsupren"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1557
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "Redakti"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "Aldoni pli ol unu ŝparvojon/kolumnon por ke ĉi tio funkciu."

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "Ankoraŭ neniuj kolumnoj. Alklaku la butonon Aldoni kolumnon."

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "Ankoraŭ neniuj ŝparvojoj. Alklaku la butonon Aldoni ŝparvojon."

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "Ĉu vi ne certas, kion aldoni? <0/>Provu unue aldoni <1>Hejmo / Sekvatoj kaj Sciigoj</1>."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "Maks. {SHORTCUTS_LIMIT} kolumnoj"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "Maks. {SHORTCUTS_LIMIT} ŝparvojoj"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "Importi/eksporti"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "Aldoni kolumnon…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "Aldoni ŝparvojon…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "Specifa listo estas laŭvola. Por plurkolumna reĝimo, listo estas postulata, alie la kolumno ne estos montrata."

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "Por plurkolumna reĝimo, serĉtermino estas postulata, alie la kolumno ne estos montrata."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "Plurkradvortoj estas subtenataj. Spac-separataj."

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "Redakti ŝparvojon"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "Aldoni ŝparvojon"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "Templinio"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "Listo"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "Importi/eksporti <0>Ŝparvojoj</0>"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "Importi"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "Alglui ŝparvojojn ĉi tie"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "Elŝutante konservitajn ŝparvojojn de nodservilo…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "Ne eblas elŝuti ŝparvojojn"

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "Elŝuti ŝparvojojn de nodservilo"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "* Ekzistas en nunaj ŝparvojoj"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "Listo eble ne funkcias se ĝi venas de malsama konto."

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "Nevalida agorda formato"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "Ĉu aldoni al nunaj ŝparvojoj?"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr "Nur ŝparvojoj kiuj ne ekzistas en la nunaj ŝparvojoj estos aldonitaj."

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "Ne estas novaj ŝparvojoj por importi"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "Ŝparvojoj importitaj. Superita maksimumo {SHORTCUTS_LIMIT}, do la ceteraj ne estas importitaj."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "Ŝparvojoj importitaj"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "Importi kaj aldoni…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "Ĉu anstataŭi nunajn ŝparvojojn?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "Ĉu importi ŝparvojojn?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "aŭ anstataŭi…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "Importi…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "Eksporti"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "Ŝparvojoj kopiitaj"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "Ne eblas kopii ŝparvojoj"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "Ŝparvojaj agordoj kopiitaj"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "Ne eblas kopii ŝparvojajn agordojn"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "Diskonigi"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "Konservante ŝparvojojn al nodservilo…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "Ŝparvojoj konservitaj"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "Ne eblas konservi ŝparvojojn"

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "Sinkronigi al nodservilo"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0, plural, one {# skribsigno} other {# skribsignoj}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "Ne estas plu ŝparvojoj por importi"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "Importi/eksporti agordojn de/al nodservilo (Tre eksperimenta)"

#: src/components/status.jsx:351
msgid "Unable to format math"
msgstr ""

#: src/components/status.jsx:365
msgid "Math expressions found."
msgstr ""

#. Action to switch from rendered math back to raw (LaTeX) markup
#: src/components/status.jsx:368
msgid "Show markup"
msgstr ""

#. Action to render math expressions from raw (LaTeX) markup
#: src/components/status.jsx:373
msgid "Format math"
msgstr ""

#: src/components/status.jsx:772
msgid "<0/> <1>boosted</1>"
msgstr "<0/> <1>diskonigita</1>"

#: src/components/status.jsx:875
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "Pardonu, la nodo en kiu vi estas ensalutinta ne permesas vin interagi kun ĉi tiu afiŝo de alia nodo."

#. placeholder {0}: username || acct
#: src/components/status.jsx:1029
msgid "Unliked @{0}'s post"
msgstr "Ne plu ŝatis la afiŝon de {0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1030
msgid "Liked @{0}'s post"
msgstr "Ŝatis la afiŝon de {0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1069
msgid "Unbookmarked @{0}'s post"
msgstr "Ne plu legosignis la afiŝon de @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1070
msgid "Bookmarked @{0}'s post"
msgstr "Legosignis la afiŝon de @{0}"

#: src/components/status.jsx:1169
msgid "Some media have no descriptions."
msgstr "Iuj plurmedioj ne havas priskribojn."

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1176
msgid "Old post (<0>{0}</0>)"
msgstr "Malnova afiŝo (<0>{0}</0>)"

#: src/components/status.jsx:1200
#: src/components/status.jsx:1240
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
msgid "Unboost"
msgstr "Ne plu diskonigi"

#: src/components/status.jsx:1216
#: src/components/status.jsx:2715
msgid "Quote"
msgstr "Citaĵo"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1228
#: src/components/status.jsx:1694
msgid "Unboosted @{0}'s post"
msgstr "Ne plu diskonigita la afiŝon de @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1229
#: src/components/status.jsx:1695
msgid "Boosted @{0}'s post"
msgstr "Diskonigita la afiŝon de @{0}"

#: src/components/status.jsx:1241
msgid "Boost…"
msgstr "Diskonigi…"

#: src/components/status.jsx:1253
#: src/components/status.jsx:1988
#: src/components/status.jsx:2736
msgid "Unlike"
msgstr "Ne plu ŝati"

#: src/components/status.jsx:1254
#: src/components/status.jsx:1988
#: src/components/status.jsx:1989
#: src/components/status.jsx:2736
#: src/components/status.jsx:2737
msgid "Like"
msgstr "Ŝatata"

#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
msgid "Unbookmark"
msgstr "Ne plu legosignis"

#: src/components/status.jsx:1346
msgid "Post text copied"
msgstr "Teksto de la afiŝo kopiita"

#: src/components/status.jsx:1349
msgid "Unable to copy post text"
msgstr "Ne eblas kopii tekston de la afiŝo"

#: src/components/status.jsx:1355
msgid "Copy post text"
msgstr "Kopii tekston de la afiŝo"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1373
msgid "View post by <0>@{0}</0>"
msgstr "Vidi afiŝon de <0>@{0}</0>"

#: src/components/status.jsx:1394
msgid "Show Edit History"
msgstr "Montri redaktan historion"

#: src/components/status.jsx:1397
msgid "Edited: {editedDateText}"
msgstr "Redaktita: {editedDateText}"

#: src/components/status.jsx:1464
#: src/components/status.jsx:3516
msgid "Embed post"
msgstr "Enkorpigi afiŝon"

#: src/components/status.jsx:1478
msgid "Conversation unmuted"
msgstr "Konversacion nesilentigita"

#: src/components/status.jsx:1478
msgid "Conversation muted"
msgstr "Konversacion silentigita"

#: src/components/status.jsx:1484
msgid "Unable to unmute conversation"
msgstr "Ne eblas nesilentigi konversacion"

#: src/components/status.jsx:1485
msgid "Unable to mute conversation"
msgstr "Ne eblas silentigi konversacion"

#: src/components/status.jsx:1494
msgid "Unmute conversation"
msgstr "Nesilentigi konversacion"

#: src/components/status.jsx:1501
msgid "Mute conversation"
msgstr "Silentigi konversacion"

#: src/components/status.jsx:1517
msgid "Post unpinned from profile"
msgstr "Afiŝo depinglinta de profilo"

#: src/components/status.jsx:1518
msgid "Post pinned to profile"
msgstr "Afiŝo alpinglita al profilo"

#: src/components/status.jsx:1523
msgid "Unable to unpin post"
msgstr "Ne eblas depingli afiŝon"

#: src/components/status.jsx:1523
msgid "Unable to pin post"
msgstr "Ne eblas alpingli afiŝon"

#: src/components/status.jsx:1532
msgid "Unpin from profile"
msgstr "Depingli de profilo"

#: src/components/status.jsx:1539
msgid "Pin to profile"
msgstr "Alpingli al la profilo"

#: src/components/status.jsx:1568
msgid "Delete this post?"
msgstr "Ĉu forigi ĉi tiun afiŝon?"

#: src/components/status.jsx:1584
msgid "Post deleted"
msgstr "Afiŝo forigita"

#: src/components/status.jsx:1587
msgid "Unable to delete post"
msgstr "Ne eblas forigi afiŝon"

#: src/components/status.jsx:1615
msgid "Report post…"
msgstr "Raporti afiŝon…"

#: src/components/status.jsx:1989
#: src/components/status.jsx:2025
#: src/components/status.jsx:2737
msgid "Liked"
msgstr "Ŝatita"

#: src/components/status.jsx:2022
#: src/components/status.jsx:2724
msgid "Boosted"
msgstr "Diskonigita"

#: src/components/status.jsx:2032
#: src/components/status.jsx:2749
msgid "Bookmarked"
msgstr "Legosignita"

#: src/components/status.jsx:2036
msgid "Pinned"
msgstr "Alpinglita"

#: src/components/status.jsx:2082
#: src/components/status.jsx:2561
msgid "Deleted"
msgstr "Forigita"

#: src/components/status.jsx:2123
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {# respondo} other {# respondoj}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2213
msgid "Thread{0}"
msgstr "Fadeno{0}"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
#: src/components/status.jsx:2457
msgid "Show less"
msgstr "Montru malpli"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
msgid "Show content"
msgstr "Montru envahon"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2453
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Filtrita: {0}"

#: src/components/status.jsx:2457
msgid "Show media"
msgstr "Montri plurmedion"

#: src/components/status.jsx:2597
msgid "Edited"
msgstr "Redaktita"

#: src/components/status.jsx:2674
msgid "Comments"
msgstr "Komentoj"

#. More from [Author]
#: src/components/status.jsx:2974
msgid "More from <0/>"
msgstr "Pli de <0/>"

#: src/components/status.jsx:3276
msgid "Edit History"
msgstr "Redaktan historion"

#: src/components/status.jsx:3280
msgid "Failed to load history"
msgstr "Malsukcesis ŝargi historion"

#: src/components/status.jsx:3285
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "Ŝargante…"

#: src/components/status.jsx:3521
msgid "HTML Code"
msgstr "HTML-kodo"

#: src/components/status.jsx:3538
msgid "HTML code copied"
msgstr "HTML-kodo kopiita"

#: src/components/status.jsx:3541
msgid "Unable to copy HTML code"
msgstr "Ne eblas kopii HTML-kodon"

#: src/components/status.jsx:3553
msgid "Media attachments:"
msgstr "Plurmediaj aldonaĵoj:"

#: src/components/status.jsx:3575
msgid "Account Emojis:"
msgstr "Emoĝioj de la konto:"

#: src/components/status.jsx:3606
#: src/components/status.jsx:3651
msgid "static URL"
msgstr "statika URL"

#: src/components/status.jsx:3620
msgid "Emojis:"
msgstr "Emoĝioj:"

#: src/components/status.jsx:3665
msgid "Notes:"
msgstr "Notoj:"

#: src/components/status.jsx:3669
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "Ĉi tio estas senmova, ne stilita kaj senskriba. Vi eble bezonos apliki viajn proprajn stilojn kaj redakti laŭbezone."

#: src/components/status.jsx:3675
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "Balotenketoj ne estas interagaj kaj estos konvertitaj al listo kun balotkalkuloj."

#: src/components/status.jsx:3680
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "Plurmediaj aldonaĵoj povas esti bildoj, filmetoj, sonaĵoj aŭ ajnaj dosiertipoj."

#: src/components/status.jsx:3686
msgid "Post could be edited or deleted later."
msgstr "Afiŝo povus esti redaktita aŭ forigita poste."

#: src/components/status.jsx:3692
msgid "Preview"
msgstr "Antaŭrigardo"

#: src/components/status.jsx:3701
msgid "Note: This preview is lightly styled."
msgstr "Noto: Ĉi tiu antaŭmontro estas malpeze stilita."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3954
msgid "<0/> <1/> boosted"
msgstr "<0/> <1/> diskonigita"

#: src/components/status.jsx:4056
msgid "Post hidden by your filters"
msgstr ""

#: src/components/status.jsx:4057
msgid "Post pending"
msgstr ""

#: src/components/status.jsx:4058
#: src/components/status.jsx:4059
#: src/components/status.jsx:4060
#: src/components/status.jsx:4061
msgid "Post unavailable"
msgstr ""

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "Novaj afiŝoj"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1078
#: src/pages/status.jsx:1455
msgid "Try again"
msgstr "Provu denove"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, one {# Diskonigo} other {# Diskonigoj}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr "Alpinglitaj afiŝoj"

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "Fadeno"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>Filtrita</0>: <1>{0}</1>"

#: src/components/translation-block.jsx:196
msgid "Auto-translated from {sourceLangText}"
msgstr "Aŭtomate tradukita el {sourceLangText}"

#: src/components/translation-block.jsx:234
msgid "Translating…"
msgstr "Tradukante…"

#: src/components/translation-block.jsx:237
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "Traduki el {sourceLangText} (aŭtomate detektita)"

#: src/components/translation-block.jsx:238
msgid "Translate from {sourceLangText}"
msgstr "Traduki el {sourceLangText}"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:266
msgid "Auto ({0})"
msgstr "Aŭtomata ({0})"

#: src/components/translation-block.jsx:279
msgid "Failed to translate"
msgstr "Malsukcesis traduki"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "Redaktante la originalan mesaĝon"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "Respondi al @{0}"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "Vi povas fermi ĉi tiun paĝon nun."

#: src/compose.jsx:71
msgid "Close window"
msgstr "Fermi fenestron"

#: src/compose.jsx:87
msgid "Login required."
msgstr "Ensaluto bezonata."

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:280
msgid "Go home"
msgstr "Iru hejmen"

#: src/pages/account-statuses.jsx:241
msgid "Account posts"
msgstr "Afiŝoj de la konto"

#: src/pages/account-statuses.jsx:248
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ Respondoj)"

#: src/pages/account-statuses.jsx:250
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- Diskonigoj)"

#: src/pages/account-statuses.jsx:252
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:254
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (Plurmedio)"

#: src/pages/account-statuses.jsx:260
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:331
msgid "Clear filters"
msgstr "Malplenigi filtrojn"

#: src/pages/account-statuses.jsx:348
msgid "Showing post with replies"
msgstr "Montrante afiŝon kun respondoj"

#: src/pages/account-statuses.jsx:353
msgid "+ Replies"
msgstr "+ Respondoj"

#: src/pages/account-statuses.jsx:359
msgid "Showing posts without boosts"
msgstr "Montrante afiŝojn sen diskonigoj"

#: src/pages/account-statuses.jsx:364
msgid "- Boosts"
msgstr "- Diskonigoj"

#: src/pages/account-statuses.jsx:370
msgid "Showing posts with media"
msgstr "Montrante afiŝojn kun plurmedio"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:387
msgid "Showing posts tagged with #{0}"
msgstr "Montrante afiŝojn etikeditaj kun #{0}"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:426
msgid "Showing posts in {0}"
msgstr "Montrante afiŝojn en {0}"

#: src/pages/account-statuses.jsx:516
msgid "Nothing to see here yet."
msgstr "Nenio vidinda ĉi tie ankoraŭ."

#: src/pages/account-statuses.jsx:517
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "Ne eblas ŝargi afiŝojn"

#: src/pages/account-statuses.jsx:558
#: src/pages/account-statuses.jsx:588
msgid "Unable to fetch account info"
msgstr "Ne eblas ricevi informojn pri konto"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:565
msgid "Switch to account's instance {0}"
msgstr "Iri al la nodo de la konto {0}"

#: src/pages/account-statuses.jsx:595
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "Iri al mia nodo (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:668
msgid "Month"
msgstr "Monato"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "Nuna"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "Defaŭlto"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "Ŝalti al ĉi tiu konto"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "Ŝalti en novan langeton/fenestron"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "Vidi profilon…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "Agordi kiel defaŭltan"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr "Ĉu elsaluti <0>@{0}</0>?"

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "Elsaluti…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr ""

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "Aldoni ekzistantan konton"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "Noto: <0>Defaŭlta</0> konto ĉiam estos uzata por la unua ŝarĝo. Ŝanĝitaj kontoj daŭros dum la sesio."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "Ankoraŭ neniuj legosignoj. Iru legosigni ion!"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "Ne eblas ŝargi legosignoj."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "la lasta horo"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "la lastaj 2 horoj"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "la lastaj 3 horoj"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "la lastaj 4 horoj"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "la lastaj 5 horoj"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "la lastaj 6 horoj"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "la lastaj 7 horoj"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "la lastaj 8 horoj"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "la lastaj 9 horoj"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "la lastaj 10 horoj"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "la lastaj 11 horoj"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "la lastaj 12 horoj"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "pli ol 12 horoj"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "Sekvataj etikedoj"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "Grupoj"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "Montrante {selectedFilterCategory, select, all {ĉiuj afiŝoj} original {originalaj afiŝoj} replies {respondoj} boosts {diskonigoj} followedTags {sekvitaj etikedoj} groups {grupoj} filtered {filtritaj afiŝoj}}, {sortBy, select, createdAt {{sortOrder, select, asc {plej malnova} desc {lastaj}}} reblogsCount {{sortOrder, select, asc {plej malmultaj diskonigoj} desc {plej multaj diskonigoj}}} favouritesCount {{sortOrder, select, asc {plej malmutaj ŝatataj} desc {plej multaj ŝatataj}}} repliesCount {{sortOrder, select, asc {plej malmultaj respondoj} desc {plej multaj respondoj}}} density {{sortOrder, select, asc {malplej densa} desc {plej densa}}}} first{groupBy, select, account {, grupigitaj per aŭtoroj} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "Aktualiĝi <0>beta</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "Helpo"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "Kio estas ĉi tio?"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "Aktualiĝi estas aparta templinio por viaj sekvantaroj, ofertante altnivelan vidon je unu ekrigardo, kun simpla, retpoŝta interfaco por senpene ordigi kaj filtri afiŝojn."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Antaŭrigardo de Aktualiĝo UI"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "Ni atingu"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "Ni atingu la afiŝojn de viaj sekvantoj."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "Montri al mi ĉiujn afiŝojn de…"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "ĝis la maksimumo"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "Atingi"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "Interkovri kun via lasta aktualiĝo"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "Ĝis la lasta aktualiĝo ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "Noto: Via nodo povus nur montri maksimume 800 afiŝojn en la Hejma templinio sendepende de la tempointervalo. Povus esti malpli aŭ pli."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "Antaŭe…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, one {# afiŝo} other {# afiŝoj}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "Ĉu forigi ĉi tiun aktualiĝon?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr "Forigante aktualiĝon {0}"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr "Aktualiĝo {0} forigita"

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "Noto: Nur maksimume 3 estos konservitaj. La resto estos aŭtomate forigita."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "Ricevante afiŝojn…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "Ĉi tio eble daŭros iom da tempo."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "Restartigi filtrilojn"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "Popularaj ligiloj"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "Diskonigita de {0}"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:330
msgid "All"
msgstr "Ĉiuj"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, one {# aŭtoro} other {# aŭtoroj}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "Ordigi"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Dato"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Denseco"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr "Filtri"

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "Aŭtoroj"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "Nenio"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "Montri ĉiujn aŭtorojn"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "Vi ne devas legi ĉion."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "Jen ĉio."

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "Supren"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "Ligiloj diskonigitaj per sekvantoj, ordigitaj laŭ komunaj kalkuloj, diskonigoj kaj ŝatoj."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "Ordigo: Denseco"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "Afiŝoj estas ordigitaj laŭ informa denseco aŭ profundo. Pli mallongaj afiŝoj estas \"pli malpezaj\" dum pli longaj afiŝoj estas \"pli pezaj\". Afiŝoj kun fotoj estas \"pli pezaj\" ol afiŝoj sen fotoj."

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "Grupo: Aŭtoroj"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "Afiŝoj estas grupigitaj laŭ aŭtoroj, ordigitaj laŭ nombro de afiŝoj per aŭtoro."

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "Sekva aŭtoro"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "Antaŭa aŭtoro"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "Rulumi supren"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "Ankoraŭ neniuj stemuloj. Iru, trovu ion por ŝati!"

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "Ne eblas ŝargi stelumojn."

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "Hejmo kaj listoj"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "Publikaj templinioj"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "Konversacioj"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "Profiloj"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "Neniam"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "Nova filtrilo"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0, plural, one {# filtrilo} other {# filtriloj}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "Ne eblas ŝargi filtrilojn."

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "Ankoraŭ ne estas filtriloj."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "Aldoni filtrilon"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "Redakti filtrilon"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "Ne eblas redakti filtrilon"

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "Ne eblas krei filtrilon"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "Titolo"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "Tuta vorto"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "Ne estas ŝlosilvortoj. Aldonu unu."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "Aldoni ŝlosilvorton"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, one {# ŝlosilvorto} other {# ŝlosilvortoj}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "Filtri de…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* Ankoraŭ ne disponigita"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "Stato: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "Ŝanĝi eksvalidiĝon"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "Eksvalidiĝo"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "Filtrita afiŝo estos…"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr "obskurita (nur amaskomunikilaro)"

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "minimumigita"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "kaŝita"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "Ĉu forigi ĉi tiun filtrilon?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "Ne eblas forigi filtrilon."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "Finiĝita"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "Finiĝanta <0/>"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "Neniam finiĝas"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural, one {# kradvorto} other {# kradvortoj}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "Ne eblas ŝargi sekvatajn kradvortojn."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "Neniuj kradvortoj ankoraŭ sekvis."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "Nenio vidinda ĉi tie."

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "Ne eblas ŝargi afiŝojn."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{hashtagTitle} (Nur plurmedio) en {instance}"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{hashtagTitle} en {instance}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (Nur plurmedio)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "Neniu ankoraŭ afiŝis ion kun ĉi tiu kradvorto."

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "Ne eblas ŝargi afiŝojn kun tiu ĉi kradvorto"

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "Ĉu ĉesi sekvi #{hashtag}?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "Nesekvita #{hashtag}"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "Sekvita #{hashtag}"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "Sekvatoj…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "Neprezentita en profilo"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "Ne eblas nemontri en profilo"

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "Prezentita en profilo"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr "{TOTAL_TAGS_LIMIT, plural, other {Maks. # kradvortoj}}"

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "Aldoni kradvorton"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "Forigi kradvorton"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr "{SHORTCUTS_LIMIT, plural, one {Maks. # ŝparvojo atingita. Ne eblas aldoni ŝparvojon.} other {Maks. # ŝparvojoj atingitaj. Ne eblas aldoni ŝparvojon.}}"

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "Ĉi tiu ŝparvojo jam ekzistas"

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr "Kradvorto-ŝparvojo aldonita"

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "Aldoni al ŝparvojoj"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "Entajpi novan nodon, ekz. \"mastodon.social\""

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "Senvalida nodo"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "Iri al alia nodo…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "Iri al mia nodo (<0>{currentInstance}</0>)"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "Ne eblas ŝarĝi sciigojn."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<0>Nova</0> <1>Petoj de sekvado</1>"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "Solvante…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "Ne eblas solvi retadreson"

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "Ankoraŭ nenio."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "Administri membrojn"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "Ĉu forigi <0>@{0}</0> el listo?"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "Forigi…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, one {#listo} other {# listoj}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "Ankoraŭ ne estas listoj."

#: src/pages/login.jsx:122
#: src/pages/login.jsx:132
msgid "Failed to register application"
msgstr "Malsukcesis registri aplikaĵon"

#: src/pages/login.jsx:218
msgid "instance domain"
msgstr "noda domajno"

#: src/pages/login.jsx:242
msgid "e.g. “mastodon.social”"
msgstr "ekz. “mastodon.social”"

#: src/pages/login.jsx:253
msgid "Failed to log in. Please try again or try another instance."
msgstr "Malsukcesis ensaluti. Bonvolu provi denove aŭ provi alian nodon."

#: src/pages/login.jsx:265
msgid "Continue with {selectedInstanceText}"
msgstr "Daŭrigi per {selectedInstanceText}"

#: src/pages/login.jsx:266
msgid "Continue"
msgstr "Daŭrigi"

#: src/pages/login.jsx:274
msgid "Don't have an account? Create one!"
msgstr "Ĉu vi ankoraŭ ne havas konton? Kreu unu!"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "Privataj mencioj"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "Privata"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "Neniu menciis vin :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "Ne eblas ŝargi menciojn."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "Vi ne sekvas"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "Kiuj ne sekvas vin"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "Kun nova konto"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr "Kiu nepetite privata mencias vin"

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr "Kiuj estas limigitaj de servilaj moderigantoj"

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "Agordoj pri sciigoj"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "Novaj sciigoj"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, one {Anonco} other {Anoncoj}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "Petoj de sekvado"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, one {# peto de sekvado} other {# petoj de sekvado}}"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr "{0, plural, one {Filtritaj sciigoj de # persono} other {Filtritaj sciigoj de # personoj}}"

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "Nur mencioj"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "Hodiaŭ"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "Vi estas aktualiĝa."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "Hieraŭ"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "Ne eblas ŝargi sciigojn"

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "Sciigaj agordoj ĝisdatigitaj"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "Filtri sciigojn de homoj:"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "Filtrilo"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "Ignori"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "Ĝisdatigita <0>{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr "Vidi sciigojn de <0>@{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr "Sciigoj de <0>@{0}</0>"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr "Sciigoj de @{0} ne estos filtritaj ekde nun."

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr "Ne eblas akcepti sciigan peton"

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "Permesi"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr "Sciigoj de @{0} ne aperos en Filtritaj sciigoj ekde nun."

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr "Ne eblas malakcepti sciigan peton"

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "Fermi"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "Fermita"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "Loka templinio ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "Fratara templinio ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "Loka templinio"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "Fratara templinio"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "Neniu ankoraŭ afiŝis ion."

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "Ŝalti al Federacia"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "Ŝalti al Loka"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr "Neniuj planitaj afiŝoj."

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr "Planita <0><1/></0> <2>({0})</2>"

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr "Planita <0><1/></0>"

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr "Planita afiŝo replanita"

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr "Malsukcesis replani afiŝon"

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr "Replani"

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr "Ĉu forigi planitan afiŝon?"

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr "Planita afiŝo forigita"

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr "Malsukcesis forigi planitan afiŝon"

#: src/pages/search.jsx:51
msgid "Search: {q} (Posts)"
msgstr "Serĉi: {q} (Afiŝoj)"

#: src/pages/search.jsx:54
msgid "Search: {q} (Accounts)"
msgstr "Serĉi: {q} (Kontoj)"

#: src/pages/search.jsx:57
msgid "Search: {q} (Hashtags)"
msgstr "Serĉi: {q} (Kradvortoj)"

#: src/pages/search.jsx:60
msgid "Search: {q}"
msgstr "Serĉi: {q}"

#: src/pages/search.jsx:340
#: src/pages/search.jsx:422
msgid "Hashtags"
msgstr "Kradvortoj"

#: src/pages/search.jsx:372
#: src/pages/search.jsx:426
#: src/pages/search.jsx:496
msgid "See more"
msgstr "Vidi pli"

#: src/pages/search.jsx:398
msgid "See more accounts"
msgstr "Vidi pli da kontoj"

#: src/pages/search.jsx:412
msgid "No accounts found."
msgstr "Neniuj kontoj trovitaj."

#: src/pages/search.jsx:468
msgid "See more hashtags"
msgstr "Vidi pli da kradvortoj"

#: src/pages/search.jsx:482
msgid "No hashtags found."
msgstr "Neniuj kradvortoj trovitaj."

#: src/pages/search.jsx:526
msgid "See more posts"
msgstr "Vidi pli da afiŝoj"

#: src/pages/search.jsx:540
msgid "No posts found."
msgstr "Neniuj afiŝoj trovitaj."

#: src/pages/search.jsx:585
msgid "Enter your search term or paste a URL above to get started."
msgstr "Enigi vian serĉterminon aŭ alglui URL-on supre por komenci."

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "Agordoj"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "Apero"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "Hela"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "Malhela"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "Aŭtomata"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "Tekstogrando"

#. Preview of one character, in largest size
#. Preview of one character, in smallest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "A"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "Lingvo de la fasado"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "Volontulaj tradukoj"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "Afiŝante"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "Defaŭlta videblo"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "Sinkronigita"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "Malsukcesis ĝisdatigi afiŝan privatecon"

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "Sinkronigita kun la agordoj de via nodservilo. <0>Iru al via nodo ({instance}) por pliaj agordoj.</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "Eksperimentoj"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "Aŭtomate aktualigi templiniajn afiŝojn"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "Karuselon de diskonigoj"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "Traduko de afiŝoj"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr "Traduki al "

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "Sistemlingvo ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr "{0, plural, =0 {Kaŝi la butonon \"Traduki\" por:} other {Kaŝi la butonon \"Traduki\" por (#):}}"

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr "Noto: Ĉi tiu funkcio uzas eksterajn tradukservojn, funkciigitajn de <0>{TRANSLATION_API_NAME}</0>."

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "Maŝintradukado"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr "Aŭtomate montri tradukon por afiŝoj en templinio. Funkcias nur por <0>mallongaj</0> afiŝoj sen enhavaverto, plurmedio kaj balotenketo."

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "GIF-Elektilo por redaktado"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "Noto: Ĉi tiu funkcio uzas eksteran GIF-serĉan servon, funkciigitan de <0>GIPHY</0>. G-taksita (taŭga por spektado de ĉiuj aĝoj), spuraj parametroj estas nudigitaj, referencaj informoj estas preterlasitaj de petoj, sed serĉdemandoj kaj IP-adresinformoj ankoraŭ atingos iliajn servilojn."

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "Generilo pri priskribo de bildoj"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr "Nur por novaj bildoj dum verkado de novaj afiŝoj."

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "Noto: Ĉi tiu funkcio uzas eksteran AI-servon, funkciigitan de <0>img-alt-api</0>. Eble ne funkcias bone. Nur por bildoj kaj en la angla."

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr "Servil-flankaj grupigitaj sciigoj"

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr "Funkcio en alfa-versio. Eble plibonigita grupigfenestro, sed kun baza grupiglogiko."

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "\"Nuba\" importo/eksporto por ŝparvojaj agordoj"

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr "⚠️⚠️⚠️ Tre eksperimenta.<0/>Konservita en la notoj de via propra profilo. Profilaj (privataj) notoj estas ĉefe uzataj por aliaj profiloj, kaj kaŝitaj por propra profilo."

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "Noto: Ĉi tiu funkcio uzas la API de la aktuale ligita nodservilo."

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr "Ŝtelreĝimo <0>(<1>Teksto</1> → <2>████</2>)</0>"

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr "Anstataŭigi tekston kiel blokojn, utilajn kiam oni prenas ekrankopiojn, pro privatecaj kialoj."

#: src/pages/settings.jsx:710
msgid "About"
msgstr "Pri"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr "<0>Konstruita</0> de <1>@cheeaun</1>"

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "Patroni"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Donaci"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr "Lastatempaj ŝanĝoj"

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "Privateca politiko"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>Retejo:</0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>Versio:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "Versioĉeno kopiita"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr "Ne eblas kopii versioĉenon"

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr "Malsukcesis ĝisdatigi abonon. Bonvolu provi denove."

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr "Malsukcesis forigi abonon. Bonvolu provi denove."

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "Puŝaj sciigoj (beta)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr "Puŝaj sciigoj estas blokitaj. Bonvolu aktivigi ilin en via retumila agordo."

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "Permesi de <0>{0}</0>"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "iu ajn"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "homoj, kiujn mi sekvas"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "sekvantoj"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "Sekvas"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "Balotenketoj"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "Redaktoj de la afiŝo"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr "Puŝo-permeso ne estis donita ekde via lasta ensaluto. Vi devos <0><1>ensaluti</1> denove por doni puŝo-permeson</0>."

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "NOTO: Puŝaj sciigoj funkcias nur por <0>unu konto</0>."

#. js-lingui-explicit-id
#: src/pages/status.jsx:658
#: src/pages/status.jsx:1221
msgid "post.title"
msgstr "Afiŝo"

#: src/pages/status.jsx:912
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "Vi ne estas ensalutinta. Interagoj (respondi, diskonigi, ktp) ne eblas."

#: src/pages/status.jsx:932
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "Ĉi tiu afiŝo estas de alia nodo (<0>{instance}</0>). Interagoj (respondi, diskonigi, ktp) ne eblas."

#: src/pages/status.jsx:960
msgid "Error: {e}"
msgstr "Eraro: {e}"

#: src/pages/status.jsx:967
msgid "Switch to my instance to enable interactions"
msgstr "Iri al mia nodo por ŝalti interagojn"

#: src/pages/status.jsx:1069
msgid "Unable to load replies."
msgstr "Ne eblas ŝargi respondojn."

#: src/pages/status.jsx:1181
msgid "Back"
msgstr "Reen"

#: src/pages/status.jsx:1212
msgid "Go to main post"
msgstr "Iri al ĉefa afiŝo"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1235
msgid "{0} posts above ‒ Go to top"
msgstr "{0} afiŝoj supre ‒ Iri supren"

#: src/pages/status.jsx:1282
#: src/pages/status.jsx:1345
msgid "Switch to Side Peek view"
msgstr "Ŝalti al Flanka Vido"

#: src/pages/status.jsx:1346
msgid "Switch to Full view"
msgstr "Ŝalti al Plena Vido"

#: src/pages/status.jsx:1364
msgid "Show all sensitive content"
msgstr "Montri ĉiujn sentemajn enhavojn"

#: src/pages/status.jsx:1369
msgid "Experimental"
msgstr "Eksperimenta"

#: src/pages/status.jsx:1378
msgid "Unable to switch"
msgstr "Ne eblas ŝalti"

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1385
msgid "Switch to post's instance ({0})"
msgstr "Iri al nodo de la afiŝo ({0})"

#: src/pages/status.jsx:1388
msgid "Switch to post's instance"
msgstr "Iri al la nodo de la afiŝo"

#: src/pages/status.jsx:1446
msgid "Unable to load post"
msgstr "Ne eblas ŝargi afiŝon"

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1582
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, one {# respondo} other {<0>{1}</0> respondoj}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1600
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, one {# komento} other {<0>{0}</0> komentoj}}"

#: src/pages/status.jsx:1622
msgid "View post with its replies"
msgstr "Vidi afiŝon kun siaj respondoj"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "Populara ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "Popularaj Novaĵoj"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr "Per {0}"

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "Reen al montrado de popularaj afiŝoj"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "Montrante afiŝojn kiuj mencias <0>{0}</0>"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "Popularaj afiŝoj"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "Neniuj popularaj afiŝoj."

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "Minimuma retkliento por Mastodon, kiu faras aferojn laŭ via maniero."

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Ensaluti per Mastodon"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "Aliĝi"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "Konekti vian ekzistantan Mastodon/Fediverson-konton.<0/>Viaj ensalutiloj ne estas konservitaj en ĉi tiu servilo."

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr "<0>Konstruita</0> de <1>@cheeaun</1>. <2>Regularo pri privateco</2>."

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "Ekrankopio de Karuselon de diskonigoj"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "Karuselon de diskonigoj"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "Vide apartigu originalajn afiŝojn kaj redividitajn afiŝojn (diskonigitajn afiŝojn)."

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr "Ekrankopio de grupigita komenta fadeno"

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr "Grupigita komenta fadeno"

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "Sekvi konversaciojn perfekte. Duonfaldeblaj respondoj."

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr "Ekrankopio de grupigitaj sciigoj"

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "Grupigitaj sciigoj"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr "Similaj sciigoj estas grupigitaj kaj kolapsitaj por redukti malordon."

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "Ekrankopio de plurkolumna uzantinterfaco"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "Ununuro aŭ plurkolumno"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "Defaŭlte, ununura kolumno por serĉantoj de zen-reĝimo. Agordebla plurkolumno por altnivelaj uzantoj."

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "Ekrankopio de plurkradvorta templinio kun formo por aldoni pliajn kradvortojn"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "Plurkradvorta templinio"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "Ĝis 5 kradvortoj kombinitaj en ununuran templinion."

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "Ŝajnas, ke via retumilo blokas ŝprucfenestrojn."

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr "Malneto afiŝo estas nuntempe minimumigita. Afiŝu aŭ forĵetu ĝin antaŭ ol krei novan."

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr "Afiŝo estas nuntempe malfermita. Afiŝu aŭ forĵetu ĝin antaŭ ol krei novan."

