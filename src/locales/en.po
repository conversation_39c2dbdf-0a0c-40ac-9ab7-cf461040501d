msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: en\n"
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Plural-Forms: \n"

#: src/components/account-block.jsx:141
msgid "Locked"
msgstr ""

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:163
msgid "Posts: {0}"
msgstr ""

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:168
msgid "Last posted: {0}"
msgstr ""

#: src/components/account-block.jsx:183
#: src/components/account-info.jsx:705
msgid "Automated"
msgstr ""

#: src/components/account-block.jsx:190
#: src/components/account-info.jsx:710
#: src/components/status.jsx:748
msgid "Group"
msgstr ""

#: src/components/account-block.jsx:200
msgid "Mutual"
msgstr ""

#: src/components/account-block.jsx:204
#: src/components/account-info.jsx:1907
msgid "Requested"
msgstr ""

#: src/components/account-block.jsx:208
#: src/components/account-info.jsx:1898
msgid "Following"
msgstr ""

#: src/components/account-block.jsx:212
#: src/components/account-info.jsx:1212
msgid "Follows you"
msgstr ""

#: src/components/account-block.jsx:220
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr ""

#: src/components/account-block.jsx:229
#: src/components/account-info.jsx:753
msgid "Verified"
msgstr ""

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:244
#: src/components/account-info.jsx:892
msgid "Joined <0>{0}</0>"
msgstr ""

#: src/components/account-info.jsx:64
msgid "Forever"
msgstr ""

#: src/components/account-info.jsx:398
msgid "Unable to load account."
msgstr ""

#: src/components/account-info.jsx:413
msgid "Go to account page"
msgstr ""

#: src/components/account-info.jsx:442
#: src/components/account-info.jsx:775
msgid "Followers"
msgstr ""

#. js-lingui-explicit-id
#: src/components/account-info.jsx:446
#: src/components/account-info.jsx:830
msgid "following.stats"
msgstr "Following"

#: src/components/account-info.jsx:449
#: src/pages/account-statuses.jsx:495
#: src/pages/search.jsx:345
#: src/pages/search.jsx:492
msgid "Posts"
msgstr ""

#: src/components/account-info.jsx:457
#: src/components/account-info.jsx:1268
#: src/components/compose.jsx:2819
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1996
#: src/components/status.jsx:2013
#: src/components/status.jsx:2138
#: src/components/status.jsx:2767
#: src/components/status.jsx:2770
#: src/pages/account-statuses.jsx:539
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1306
#: src/pages/trending.jsx:474
msgid "More"
msgstr ""

#: src/components/account-info.jsx:469
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr ""

#: src/components/account-info.jsx:614
#: src/components/account-info.jsx:1496
msgid "Handle copied"
msgstr "Handle copied"

#: src/components/account-info.jsx:617
#: src/components/account-info.jsx:1499
msgid "Unable to copy handle"
msgstr "Unable to copy handle"

#: src/components/account-info.jsx:623
#: src/components/account-info.jsx:1505
msgid "Copy handle"
msgstr ""

#: src/components/account-info.jsx:629
msgid "Go to original profile page"
msgstr ""

#: src/components/account-info.jsx:647
msgid "View profile image"
msgstr ""

#: src/components/account-info.jsx:665
msgid "View profile header"
msgstr ""

#: src/components/account-info.jsx:681
#: src/components/account-info.jsx:1806
#: src/components/account-info.jsx:2332
msgid "Edit profile"
msgstr ""

#: src/components/account-info.jsx:700
msgid "In Memoriam"
msgstr ""

#: src/components/account-info.jsx:782
#: src/components/account-info.jsx:840
msgid "This user has chosen to not make this information available."
msgstr "This user has chosen to not make this information available."

#. placeholder {0}: shortenNumber(followersCount)
#. placeholder {1}: shortenNumber(followersCount)
#: src/components/account-info.jsx:802
msgid "{followersCount, plural, one {<0>{0}</0> Follower} other {<1>{1}</1> Followers}}"
msgstr "{followersCount, plural, one {<0>{0}</0> Follower} other {<1>{1}</1> Followers}}"

#. placeholder {0}: shortenNumber(followingCount)
#: src/components/account-info.jsx:846
msgid "{followingCount, plural, other {<0>{0}</0> Following}}"
msgstr "{followingCount, plural, other {<0>{0}</0> Following}}"

#. placeholder {0}: shortenNumber(statusesCount)
#. placeholder {1}: shortenNumber(statusesCount)
#: src/components/account-info.jsx:870
msgid "{statusesCount, plural, one {<0>{0}</0> Post} other {<1>{1}</1> Posts}}"
msgstr "{statusesCount, plural, one {<0>{0}</0> Post} other {<1>{1}</1> Posts}}"

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:917
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0} original posts, {1} replies, {2} boosts"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:933
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr ""

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:949
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr ""

#: src/components/account-info.jsx:974
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr ""

#: src/components/account-info.jsx:978
#: src/components/status.jsx:2551
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1025
#: src/pages/status.jsx:1652
msgid "Replies"
msgstr ""

#: src/components/account-info.jsx:982
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr ""

#: src/components/account-info.jsx:988
msgid "Post stats unavailable."
msgstr ""

#: src/components/account-info.jsx:1019
msgid "View post stats"
msgstr ""

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1216
msgid "Last post: <0>{0}</0>"
msgstr ""

#: src/components/account-info.jsx:1230
msgid "Muted"
msgstr ""

#: src/components/account-info.jsx:1235
msgid "Blocked"
msgstr ""

#: src/components/account-info.jsx:1244
msgid "Private note"
msgstr "Private note"

#: src/components/account-info.jsx:1301
msgid "Mention <0>@{username}</0>"
msgstr ""

#: src/components/account-info.jsx:1313
msgid "Translate bio"
msgstr ""

#: src/components/account-info.jsx:1324
msgid "Edit private note"
msgstr "Edit private note"

#: src/components/account-info.jsx:1324
msgid "Add private note"
msgstr "Add private note"

#: src/components/account-info.jsx:1344
msgid "Notifications enabled for @{username}'s posts."
msgstr "Notifications enabled for @{username}'s posts."

#: src/components/account-info.jsx:1345
msgid " Notifications disabled for @{username}'s posts."
msgstr " Notifications disabled for @{username}'s posts."

#: src/components/account-info.jsx:1357
msgid "Disable notifications"
msgstr "Disable notifications"

#: src/components/account-info.jsx:1358
msgid "Enable notifications"
msgstr "Enable notifications"

#: src/components/account-info.jsx:1375
msgid "Boosts from @{username} enabled."
msgstr "Boosts from @{username} enabled."

#: src/components/account-info.jsx:1376
msgid "Boosts from @{username} disabled."
msgstr "Boosts from @{username} disabled."

#: src/components/account-info.jsx:1387
msgid "Disable boosts"
msgstr "Disable boosts"

#: src/components/account-info.jsx:1387
msgid "Enable boosts"
msgstr "Enable boosts"

#: src/components/account-info.jsx:1406
msgid "@{username} is no longer featured on your profile."
msgstr "@{username} is no longer featured on your profile."

#: src/components/account-info.jsx:1416
msgid "@{username} is now featured on your profile."
msgstr "@{username} is now featured on your profile."

#: src/components/account-info.jsx:1424
msgid "Unable to unfeature @{username} on your profile."
msgstr "Unable to unfeature @{username} on your profile."

#: src/components/account-info.jsx:1428
msgid "Unable to feature @{username} on your profile."
msgstr "Unable to feature @{username} on your profile."

#: src/components/account-info.jsx:1437
msgid "Don't feature on profile"
msgstr "Don't feature on profile"

#: src/components/account-info.jsx:1438
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr ""

#: src/components/account-info.jsx:1447
msgid "Show featured profiles"
msgstr "Show featured profiles"

#: src/components/account-info.jsx:1462
#: src/components/account-info.jsx:1472
#: src/components/account-info.jsx:2078
msgid "Add/Remove from Lists"
msgstr ""

#: src/components/account-info.jsx:1522
#: src/components/status.jsx:1418
msgid "Link copied"
msgstr ""

#: src/components/account-info.jsx:1525
#: src/components/status.jsx:1421
msgid "Unable to copy link"
msgstr ""

#: src/components/account-info.jsx:1531
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1427
#: src/components/status.jsx:3545
msgid "Copy"
msgstr ""

#: src/components/account-info.jsx:1546
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1443
msgid "Sharing doesn't seem to work."
msgstr ""

#: src/components/account-info.jsx:1552
#: src/components/status.jsx:1449
msgid "Share…"
msgstr ""

#: src/components/account-info.jsx:1572
msgid "Unmuted @{username}"
msgstr "Unmuted @{username}"

#: src/components/account-info.jsx:1584
msgid "Unmute <0>@{username}</0>"
msgstr ""

#: src/components/account-info.jsx:1600
msgid "Mute <0>@{username}</0>…"
msgstr ""

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1632
msgid "Muted @{username} for {0}"
msgstr "Muted @{username} for {0}"

#: src/components/account-info.jsx:1644
msgid "Unable to mute @{username}"
msgstr "Unable to mute @{username}"

#: src/components/account-info.jsx:1665
msgid "Remove <0>@{username}</0> from followers?"
msgstr ""

#: src/components/account-info.jsx:1685
msgid "@{username} removed from followers"
msgstr "@{username} removed from followers"

#: src/components/account-info.jsx:1697
msgid "Remove follower…"
msgstr ""

#: src/components/account-info.jsx:1708
msgid "Block <0>@{username}</0>?"
msgstr ""

#: src/components/account-info.jsx:1732
msgid "Unblocked @{username}"
msgstr "Unblocked @{username}"

#: src/components/account-info.jsx:1740
msgid "Blocked @{username}"
msgstr "Blocked @{username}"

#: src/components/account-info.jsx:1748
msgid "Unable to unblock @{username}"
msgstr "Unable to unblock @{username}"

#: src/components/account-info.jsx:1750
msgid "Unable to block @{username}"
msgstr "Unable to block @{username}"

#: src/components/account-info.jsx:1760
msgid "Unblock <0>@{username}</0>"
msgstr ""

#: src/components/account-info.jsx:1769
msgid "Block <0>@{username}</0>…"
msgstr ""

#: src/components/account-info.jsx:1786
msgid "Report <0>@{username}</0>…"
msgstr ""

#: src/components/account-info.jsx:1842
msgid "Withdraw follow request?"
msgstr "Withdraw follow request?"

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1843
msgid "Unfollow @{0}?"
msgstr "Unfollow @{0}?"

#: src/components/account-info.jsx:1901
msgid "Unfollow…"
msgstr ""

#: src/components/account-info.jsx:1910
msgid "Withdraw…"
msgstr ""

#: src/components/account-info.jsx:1917
#: src/components/account-info.jsx:1921
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr ""

#: src/components/account-info.jsx:2018
#: src/components/account-info.jsx:2073
#: src/components/account-info.jsx:2207
#: src/components/account-info.jsx:2327
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:892
#: src/components/compose.jsx:2775
#: src/components/compose.jsx:3255
#: src/components/compose.jsx:3464
#: src/components/compose.jsx:3694
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:118
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3269
#: src/components/status.jsx:3509
#: src/components/status.jsx:4018
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1393
msgid "Close"
msgstr ""

#: src/components/account-info.jsx:2023
msgid "Translated Bio"
msgstr ""

#: src/components/account-info.jsx:2118
msgid "Unable to remove from list."
msgstr "Unable to remove from list."

#: src/components/account-info.jsx:2119
msgid "Unable to add to list."
msgstr "Unable to add to list."

#: src/components/account-info.jsx:2138
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr ""

#: src/components/account-info.jsx:2142
msgid "No lists."
msgstr ""

#: src/components/account-info.jsx:2153
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr ""

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2212
msgid "Private note about <0>@{0}</0>"
msgstr ""

#: src/components/account-info.jsx:2242
msgid "Unable to update private note."
msgstr "Unable to update private note."

#: src/components/account-info.jsx:2265
#: src/components/account-info.jsx:2563
msgid "Cancel"
msgstr ""

#: src/components/account-info.jsx:2270
msgid "Save & close"
msgstr ""

#: src/components/account-info.jsx:2387
msgid "Unable to update profile."
msgstr "Unable to update profile."

#: src/components/account-info.jsx:2394
msgid "Header picture"
msgstr "Header picture"

#: src/components/account-info.jsx:2446
msgid "Profile picture"
msgstr "Profile picture"

#: src/components/account-info.jsx:2498
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr ""

#: src/components/account-info.jsx:2511
msgid "Bio"
msgstr ""

#: src/components/account-info.jsx:2524
msgid "Extra fields"
msgstr ""

#: src/components/account-info.jsx:2530
msgid "Label"
msgstr ""

#: src/components/account-info.jsx:2533
msgid "Content"
msgstr ""

#: src/components/account-info.jsx:2566
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr ""

#: src/components/account-info.jsx:2620
msgid "username"
msgstr ""

#: src/components/account-info.jsx:2624
msgid "server domain name"
msgstr ""

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2690
msgid "Profiles featured by @{0}"
msgstr "Profiles featured by @{0}"

#: src/components/account-info.jsx:2716
msgid "No featured profiles."
msgstr "No featured profiles."

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr ""

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr ""

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr ""

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr ""

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr "Scheduled Posts"

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr "Add to thread"

#: src/components/compose.jsx:212
msgid "Take photo or video"
msgstr "Take photo or video"

#: src/components/compose.jsx:213
msgid "Add media"
msgstr "Add media"

#: src/components/compose.jsx:214
msgid "Add custom emoji"
msgstr ""

#: src/components/compose.jsx:215
msgid "Add GIF"
msgstr "Add GIF"

#: src/components/compose.jsx:216
msgid "Add poll"
msgstr ""

#: src/components/compose.jsx:217
msgid "Schedule post"
msgstr "Schedule post"

#: src/components/compose.jsx:417
msgid "You have unsaved changes. Discard this post?"
msgstr "You have unsaved changes. Discard this post?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:655
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"

#: src/components/compose.jsx:665
#: src/components/compose.jsx:683
#: src/components/compose.jsx:1795
#: src/components/compose.jsx:1930
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr ""

#: src/components/compose.jsx:873
msgid "Pop out"
msgstr "Pop out"

#: src/components/compose.jsx:880
msgid "Minimize"
msgstr "Minimize"

#: src/components/compose.jsx:916
msgid "Looks like you closed the parent window."
msgstr "Looks like you closed the parent window."

#: src/components/compose.jsx:923
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."

#: src/components/compose.jsx:928
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"

#: src/components/compose.jsx:971
msgid "Pop in"
msgstr "Pop in"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:981
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr ""

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:991
msgid "Replying to @{0}’s post"
msgstr ""

#: src/components/compose.jsx:1004
msgid "Editing source post"
msgstr ""

#: src/components/compose.jsx:1057
msgid "Poll must have at least 2 options"
msgstr "Poll must have at least 2 options"

#: src/components/compose.jsx:1061
msgid "Some poll choices are empty"
msgstr "Some poll choices are empty"

#: src/components/compose.jsx:1074
msgid "Some media have no descriptions. Continue?"
msgstr "Some media have no descriptions. Continue?"

#: src/components/compose.jsx:1126
msgid "Attachment #{i} failed"
msgstr "Attachment #{i} failed"

#: src/components/compose.jsx:1222
#: src/components/status.jsx:2326
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr ""

#: src/components/compose.jsx:1238
msgid "Content warning or sensitive media"
msgstr "Content warning or sensitive media"

#: src/components/compose.jsx:1274
#: src/components/status.jsx:99
#: src/pages/settings.jsx:318
msgid "Public"
msgstr ""

#: src/components/compose.jsx:1279
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:100
msgid "Local"
msgstr ""

#: src/components/compose.jsx:1283
#: src/components/status.jsx:101
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr ""

#: src/components/compose.jsx:1286
#: src/components/status.jsx:102
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr ""

#: src/components/compose.jsx:1289
#: src/components/status.jsx:103
#: src/components/status.jsx:2202
msgid "Private mention"
msgstr ""

#: src/components/compose.jsx:1298
msgid "Post your reply"
msgstr "Post your reply"

#: src/components/compose.jsx:1300
msgid "Edit your post"
msgstr "Edit your post"

#: src/components/compose.jsx:1301
msgid "What are you doing?"
msgstr "What are you doing?"

#: src/components/compose.jsx:1380
msgid "Mark media as sensitive"
msgstr ""

#: src/components/compose.jsx:1417
msgid "Posting on <0/>"
msgstr "Posting on <0/>"

#: src/components/compose.jsx:1448
#: src/components/compose.jsx:3313
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr ""

#: src/components/compose.jsx:1676
msgid "Schedule"
msgstr "Schedule"

#: src/components/compose.jsx:1678
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1190
#: src/components/status.jsx:1976
#: src/components/status.jsx:1977
#: src/components/status.jsx:2671
msgid "Reply"
msgstr ""

#: src/components/compose.jsx:1680
msgid "Update"
msgstr "Update"

#: src/components/compose.jsx:1681
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Post"

#: src/components/compose.jsx:1807
msgid "Downloading GIF…"
msgstr "Downloading GIF…"

#: src/components/compose.jsx:1835
msgid "Failed to download GIF"
msgstr "Failed to download GIF"

#: src/components/compose.jsx:2060
#: src/components/compose.jsx:2153
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr ""

#: src/components/compose.jsx:2589
msgid "Uploaded"
msgstr ""

#: src/components/compose.jsx:2602
msgid "Image description"
msgstr "Image description"

#: src/components/compose.jsx:2603
msgid "Video description"
msgstr "Video description"

#: src/components/compose.jsx:2604
msgid "Audio description"
msgstr "Audio description"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2639
#: src/components/compose.jsx:2659
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2651
#: src/components/compose.jsx:2671
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."

#: src/components/compose.jsx:2679
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "Frame rate too high. Uploading might encounter issues."

#: src/components/compose.jsx:2739
#: src/components/compose.jsx:2989
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr ""

#: src/components/compose.jsx:2756
#: src/compose.jsx:84
msgid "Error"
msgstr ""

#: src/components/compose.jsx:2781
msgid "Edit image description"
msgstr "Edit image description"

#: src/components/compose.jsx:2782
msgid "Edit video description"
msgstr "Edit video description"

#: src/components/compose.jsx:2783
msgid "Edit audio description"
msgstr "Edit audio description"

#: src/components/compose.jsx:2828
#: src/components/compose.jsx:2877
msgid "Generating description. Please wait…"
msgstr "Generating description. Please wait…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2848
msgid "Failed to generate description: {0}"
msgstr "Failed to generate description: {0}"

#: src/components/compose.jsx:2849
msgid "Failed to generate description"
msgstr "Failed to generate description"

#: src/components/compose.jsx:2861
#: src/components/compose.jsx:2867
#: src/components/compose.jsx:2913
msgid "Generate description…"
msgstr ""

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2900
msgid "Failed to generate description{0}"
msgstr "Failed to generate description{0}"

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2915
msgid "({0}) <0>— experimental</0>"
msgstr ""

#: src/components/compose.jsx:2934
msgid "Done"
msgstr ""

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2970
msgid "Choice {0}"
msgstr "Choice {0}"

#: src/components/compose.jsx:3017
msgid "Multiple choices"
msgstr ""

#: src/components/compose.jsx:3020
msgid "Duration"
msgstr ""

#: src/components/compose.jsx:3051
msgid "Remove poll"
msgstr ""

#: src/components/compose.jsx:3272
msgid "Search accounts"
msgstr "Search accounts"

#: src/components/compose.jsx:3326
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr ""

#: src/components/compose.jsx:3470
msgid "Custom emojis"
msgstr ""

#: src/components/compose.jsx:3490
msgid "Search emoji"
msgstr "Search emoji"

#: src/components/compose.jsx:3521
msgid "Error loading custom emojis"
msgstr ""

#: src/components/compose.jsx:3532
msgid "Recently used"
msgstr "Recently used"

#: src/components/compose.jsx:3533
msgid "Others"
msgstr "Others"

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3571
msgid "{0} more…"
msgstr ""

#: src/components/compose.jsx:3709
msgid "Search GIFs"
msgstr "Search GIFs"

#: src/components/compose.jsx:3724
msgid "Powered by GIPHY"
msgstr "Powered by GIPHY"

#: src/components/compose.jsx:3732
msgid "Type to search GIFs"
msgstr ""

#: src/components/compose.jsx:3830
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr ""

#: src/components/compose.jsx:3848
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr ""

#: src/components/compose.jsx:3865
msgid "Error loading GIFs"
msgstr ""

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr ""

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr ""

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr ""

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr ""

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1593
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr ""

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr ""

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr ""

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr ""

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr ""

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr ""

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr ""

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:375
msgid "Media"
msgstr ""

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr ""

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr ""

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr ""

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr ""

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr ""

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:335
#: src/pages/search.jsx:368
msgid "Accounts"
msgstr ""

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:562
#: src/pages/status.jsx:1426
msgid "Show more…"
msgstr ""

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:567
msgid "The end."
msgstr ""

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr "Focus next column in multi-column mode"

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr "Focus previous column in multi-column mode"

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:204
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:47
#: src/pages/search.jsx:317
msgid "Search"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1198
#: src/components/status.jsx:2698
#: src/components/status.jsx:2721
#: src/components/status.jsx:2722
msgid "Boost"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1261
#: src/components/status.jsx:2746
#: src/components/status.jsx:2747
msgid "Bookmark"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr ""

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr ""

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr ""

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr ""

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr ""

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr ""

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr ""

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr ""

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr ""

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr ""

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr ""

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr "Posts on this list are hidden from Home/Following"

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr ""

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1304
#: src/components/status.jsx:1313
#: src/components/translation-block.jsx:239
msgid "Translate"
msgstr ""

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1332
msgid "Speak"
msgstr ""

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr ""

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr ""

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr ""

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr ""

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr ""

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr ""

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr ""

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr ""

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3848
#: src/components/status.jsx:3944
#: src/components/status.jsx:4022
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr ""

#: src/components/media.jsx:479
msgid "Open file"
msgstr "Open file"

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr "Post scheduled"

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr ""

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr "Reply scheduled"

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr ""

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr ""

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr ""

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr ""

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr ""

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr "Following"

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr ""

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr ""

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr ""

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr ""

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr ""

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr ""

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr ""

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr ""

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:341
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr ""

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr ""

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr ""

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr ""

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr ""

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr ""

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:199
#: src/pages/status.jsx:925
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr ""

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr ""

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr ""

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr ""

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr ""

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr ""

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr ""

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr ""

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr ""

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr ""

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr ""

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr ""

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr ""

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr ""

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr ""

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr ""

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr ""

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr ""

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr ""

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr ""

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr ""

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr ""

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr ""

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr ""

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr ""

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr "Your {year} #Wrapstodon is here!"

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr ""

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr ""

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr ""

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr ""

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr ""

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr ""

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr ""

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr ""

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr ""

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr ""

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr ""

#: src/components/notification.jsx:451
#: src/components/status.jsx:1275
#: src/components/status.jsx:1285
msgid "Boosted/Liked by…"
msgstr ""

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr ""

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr ""

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr ""

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr ""

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr "View #Wrapstodon"

#: src/components/notification.jsx:801
#: src/components/status.jsx:486
msgid "Read more →"
msgstr ""

#: src/components/poll.jsx:113
msgid "Voted"
msgstr ""

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr "{optionVotesCount, plural, one {# vote} other {# votes}}"

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr ""

#: src/components/poll.jsx:188
msgid "Vote"
msgstr ""

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1295
#: src/pages/status.jsx:1318
msgid "Refresh"
msgstr ""

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr ""

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr ""

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr ""

#: src/components/poll.jsx:272
msgid "Ended"
msgstr ""

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr ""

#: src/components/poll.jsx:279
msgid "Ending"
msgstr ""

#: src/components/recent-searches.jsx:27
msgid "Cleared recent searches"
msgstr "Cleared recent searches"

#: src/components/recent-searches.jsx:49
#: src/components/search-form.jsx:328
msgid "Recent searches"
msgstr "Recent searches"

#: src/components/recent-searches.jsx:59
msgid "Clear all"
msgstr "Clear all"

#: src/components/recent-searches.jsx:94
#: src/pages/account-statuses.jsx:334
msgid "Clear"
msgstr ""

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:47
msgid "{0}s"
msgstr ""

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:52
msgid "{0}m"
msgstr ""

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:57
msgid "{0}h"
msgstr ""

#: src/components/report-modal.jsx:30
msgid "Spam"
msgstr ""

#: src/components/report-modal.jsx:31
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr ""

#: src/components/report-modal.jsx:34
msgid "Illegal"
msgstr ""

#: src/components/report-modal.jsx:35
msgid "Violates the law of your or the server's country"
msgstr ""

#: src/components/report-modal.jsx:38
msgid "Server rule violation"
msgstr ""

#: src/components/report-modal.jsx:39
msgid "Breaks specific server rules"
msgstr ""

#: src/components/report-modal.jsx:40
msgid "Violation"
msgstr ""

#: src/components/report-modal.jsx:43
msgid "Other"
msgstr ""

#: src/components/report-modal.jsx:44
msgid "Issue doesn't fit other categories"
msgstr ""

#: src/components/report-modal.jsx:111
msgid "Report Post"
msgstr ""

#: src/components/report-modal.jsx:111
msgid "Report @{username}"
msgstr ""

#: src/components/report-modal.jsx:147
msgid "Pending review"
msgstr ""

#: src/components/report-modal.jsx:189
msgid "Post reported"
msgstr ""

#: src/components/report-modal.jsx:189
msgid "Profile reported"
msgstr ""

#: src/components/report-modal.jsx:197
msgid "Unable to report post"
msgstr ""

#: src/components/report-modal.jsx:198
msgid "Unable to report profile"
msgstr ""

#: src/components/report-modal.jsx:206
msgid "What's the issue with this post?"
msgstr ""

#: src/components/report-modal.jsx:207
msgid "What's the issue with this profile?"
msgstr ""

#: src/components/report-modal.jsx:276
msgid "Additional info"
msgstr ""

#: src/components/report-modal.jsx:299
msgid "Forward to <0>{domain}</0>"
msgstr ""

#: src/components/report-modal.jsx:309
msgid "Send Report"
msgstr ""

#: src/components/report-modal.jsx:318
msgid "Muted {username}"
msgstr ""

#: src/components/report-modal.jsx:321
msgid "Unable to mute {username}"
msgstr ""

#: src/components/report-modal.jsx:326
msgid "Send Report <0>+ Mute profile</0>"
msgstr ""

#: src/components/report-modal.jsx:337
msgid "Blocked {username}"
msgstr ""

#: src/components/report-modal.jsx:340
msgid "Unable to block {username}"
msgstr ""

#: src/components/report-modal.jsx:345
msgid "Send Report <0>+ Block profile</0>"
msgstr ""

#: src/components/search-form.jsx:18
msgid "Posts with <0>{query}</0>"
msgstr ""

#: src/components/search-form.jsx:26
msgid "Accounts with <0>{query}</0>"
msgstr ""

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:34
msgid "Posts tagged with <0>#{0}</0>"
msgstr ""

#: src/components/search-form.jsx:43
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr ""

#: src/components/search-form.jsx:135
msgid "Look up <0>{query}</0>"
msgstr ""

#: src/components/search-form.jsx:361
#: src/pages/home.jsx:251
msgid "See all"
msgstr ""

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr ""

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr ""

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr ""

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr ""

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr ""

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr ""

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:203
msgid "Instance"
msgstr ""

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr ""

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr ""

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr ""

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr ""

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr ""

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr ""

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr ""

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr ""

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr ""

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr ""

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr ""

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr ""

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr ""

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr ""

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1555
#: src/pages/list.jsx:195
msgid "Edit"
msgstr ""

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr ""

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr ""

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr ""

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr ""

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr ""

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr ""

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr ""

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr ""

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr ""

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr ""

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr ""

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr ""

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr ""

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr ""

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr ""

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr ""

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr ""

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr ""

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr ""

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr ""

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr ""

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr ""

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr ""

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr ""

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr ""

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr ""

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr ""

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr ""

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr ""

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr ""

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr ""

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr ""

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr ""

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr ""

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr ""

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr ""

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr ""

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr ""

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr ""

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr ""

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr ""

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr ""

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr ""

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr ""

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr ""

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr ""

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr ""

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr ""

#: src/components/status.jsx:351
msgid "Unable to format math"
msgstr "Unable to format math"

#: src/components/status.jsx:365
msgid "Math expressions found."
msgstr "Math expressions found."

#. Action to switch from rendered math back to raw (LaTeX) markup
#: src/components/status.jsx:368
msgid "Show markup"
msgstr "Show markup"

#. Action to render math expressions from raw (LaTeX) markup
#: src/components/status.jsx:373
msgid "Format math"
msgstr "Format math"

#: src/components/status.jsx:772
msgid "<0/> <1>boosted</1>"
msgstr "<0/> <1>boosted</1>"

#: src/components/status.jsx:875
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1029
msgid "Unliked @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1030
msgid "Liked @{0}'s post"
msgstr "Liked @{0}'s post"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1069
msgid "Unbookmarked @{0}'s post"
msgstr "Unbookmarked @{0}'s post"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1070
msgid "Bookmarked @{0}'s post"
msgstr "Bookmarked @{0}'s post"

#: src/components/status.jsx:1167
msgid "Some media have no descriptions."
msgstr ""

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1174
msgid "Old post (<0>{0}</0>)"
msgstr ""

#: src/components/status.jsx:1198
#: src/components/status.jsx:1238
#: src/components/status.jsx:2698
#: src/components/status.jsx:2721
msgid "Unboost"
msgstr ""

#: src/components/status.jsx:1214
#: src/components/status.jsx:2713
msgid "Quote"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1226
#: src/components/status.jsx:1692
msgid "Unboosted @{0}'s post"
msgstr "Unboosted @{0}'s post"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1227
#: src/components/status.jsx:1693
msgid "Boosted @{0}'s post"
msgstr "Boosted @{0}'s post"

#: src/components/status.jsx:1239
msgid "Boost…"
msgstr ""

#: src/components/status.jsx:1251
#: src/components/status.jsx:1986
#: src/components/status.jsx:2734
msgid "Unlike"
msgstr ""

#: src/components/status.jsx:1252
#: src/components/status.jsx:1986
#: src/components/status.jsx:1987
#: src/components/status.jsx:2734
#: src/components/status.jsx:2735
msgid "Like"
msgstr ""

#: src/components/status.jsx:1261
#: src/components/status.jsx:2746
msgid "Unbookmark"
msgstr ""

#: src/components/status.jsx:1344
msgid "Post text copied"
msgstr "Post text copied"

#: src/components/status.jsx:1347
msgid "Unable to copy post text"
msgstr "Unable to copy post text"

#: src/components/status.jsx:1353
msgid "Copy post text"
msgstr "Copy post text"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1371
msgid "View post by <0>@{0}</0>"
msgstr ""

#: src/components/status.jsx:1392
msgid "Show Edit History"
msgstr ""

#: src/components/status.jsx:1395
msgid "Edited: {editedDateText}"
msgstr ""

#: src/components/status.jsx:1462
#: src/components/status.jsx:3514
msgid "Embed post"
msgstr ""

#: src/components/status.jsx:1476
msgid "Conversation unmuted"
msgstr ""

#: src/components/status.jsx:1476
msgid "Conversation muted"
msgstr ""

#: src/components/status.jsx:1482
msgid "Unable to unmute conversation"
msgstr ""

#: src/components/status.jsx:1483
msgid "Unable to mute conversation"
msgstr ""

#: src/components/status.jsx:1492
msgid "Unmute conversation"
msgstr ""

#: src/components/status.jsx:1499
msgid "Mute conversation"
msgstr ""

#: src/components/status.jsx:1515
msgid "Post unpinned from profile"
msgstr ""

#: src/components/status.jsx:1516
msgid "Post pinned to profile"
msgstr ""

#: src/components/status.jsx:1521
msgid "Unable to unpin post"
msgstr ""

#: src/components/status.jsx:1521
msgid "Unable to pin post"
msgstr ""

#: src/components/status.jsx:1530
msgid "Unpin from profile"
msgstr ""

#: src/components/status.jsx:1537
msgid "Pin to profile"
msgstr ""

#: src/components/status.jsx:1566
msgid "Delete this post?"
msgstr ""

#: src/components/status.jsx:1582
msgid "Post deleted"
msgstr ""

#: src/components/status.jsx:1585
msgid "Unable to delete post"
msgstr ""

#: src/components/status.jsx:1613
msgid "Report post…"
msgstr ""

#: src/components/status.jsx:1987
#: src/components/status.jsx:2023
#: src/components/status.jsx:2735
msgid "Liked"
msgstr ""

#: src/components/status.jsx:2020
#: src/components/status.jsx:2722
msgid "Boosted"
msgstr ""

#: src/components/status.jsx:2030
#: src/components/status.jsx:2747
msgid "Bookmarked"
msgstr ""

#: src/components/status.jsx:2034
msgid "Pinned"
msgstr ""

#: src/components/status.jsx:2080
#: src/components/status.jsx:2559
msgid "Deleted"
msgstr ""

#: src/components/status.jsx:2121
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr ""

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2211
msgid "Thread{0}"
msgstr ""

#: src/components/status.jsx:2289
#: src/components/status.jsx:2351
#: src/components/status.jsx:2455
msgid "Show less"
msgstr ""

#: src/components/status.jsx:2289
#: src/components/status.jsx:2351
msgid "Show content"
msgstr ""

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2451
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Filtered: {0}"

#: src/components/status.jsx:2455
msgid "Show media"
msgstr ""

#: src/components/status.jsx:2595
msgid "Edited"
msgstr ""

#: src/components/status.jsx:2672
msgid "Comments"
msgstr ""

#. More from [Author]
#: src/components/status.jsx:2972
msgid "More from <0/>"
msgstr "More from <0/>"

#: src/components/status.jsx:3274
msgid "Edit History"
msgstr ""

#: src/components/status.jsx:3278
msgid "Failed to load history"
msgstr ""

#: src/components/status.jsx:3283
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr ""

#: src/components/status.jsx:3519
msgid "HTML Code"
msgstr ""

#: src/components/status.jsx:3536
msgid "HTML code copied"
msgstr ""

#: src/components/status.jsx:3539
msgid "Unable to copy HTML code"
msgstr ""

#: src/components/status.jsx:3551
msgid "Media attachments:"
msgstr ""

#: src/components/status.jsx:3573
msgid "Account Emojis:"
msgstr ""

#: src/components/status.jsx:3604
#: src/components/status.jsx:3649
msgid "static URL"
msgstr ""

#: src/components/status.jsx:3618
msgid "Emojis:"
msgstr ""

#: src/components/status.jsx:3663
msgid "Notes:"
msgstr ""

#: src/components/status.jsx:3667
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr ""

#: src/components/status.jsx:3673
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr ""

#: src/components/status.jsx:3678
msgid "Media attachments can be images, videos, audios or any file types."
msgstr ""

#: src/components/status.jsx:3684
msgid "Post could be edited or deleted later."
msgstr ""

#: src/components/status.jsx:3690
msgid "Preview"
msgstr ""

#: src/components/status.jsx:3699
msgid "Note: This preview is lightly styled."
msgstr ""

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3952
msgid "<0/> <1/> boosted"
msgstr "<0/> <1/> boosted"

#: src/components/status.jsx:4054
msgid "Post hidden by your filters"
msgstr "Post hidden by your filters"

#: src/components/status.jsx:4055
msgid "Post pending"
msgstr "Post pending"

#: src/components/status.jsx:4056
#: src/components/status.jsx:4057
#: src/components/status.jsx:4058
#: src/components/status.jsx:4059
msgid "Post unavailable"
msgstr "Post unavailable"

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr ""

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1078
#: src/pages/status.jsx:1455
msgid "Try again"
msgstr ""

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, one {# Boost} other {# Boosts}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr "Pinned posts"

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr ""

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr ""

#: src/components/translation-block.jsx:196
msgid "Auto-translated from {sourceLangText}"
msgstr ""

#: src/components/translation-block.jsx:234
msgid "Translating…"
msgstr ""

#: src/components/translation-block.jsx:237
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr ""

#: src/components/translation-block.jsx:238
msgid "Translate from {sourceLangText}"
msgstr ""

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:266
msgid "Auto ({0})"
msgstr ""

#: src/components/translation-block.jsx:279
msgid "Failed to translate"
msgstr ""

#: src/compose.jsx:33
msgid "Editing source status"
msgstr ""

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr ""

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr ""

#: src/compose.jsx:71
msgid "Close window"
msgstr ""

#: src/compose.jsx:87
msgid "Login required."
msgstr "Login required."

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:280
msgid "Go home"
msgstr ""

#: src/pages/account-statuses.jsx:241
msgid "Account posts"
msgstr ""

#: src/pages/account-statuses.jsx:248
msgid "{accountDisplay} (+ Replies)"
msgstr ""

#: src/pages/account-statuses.jsx:250
msgid "{accountDisplay} (- Boosts)"
msgstr ""

#: src/pages/account-statuses.jsx:252
msgid "{accountDisplay} (#{tagged})"
msgstr ""

#: src/pages/account-statuses.jsx:254
msgid "{accountDisplay} (Media)"
msgstr ""

#: src/pages/account-statuses.jsx:260
msgid "{accountDisplay} ({monthYear})"
msgstr ""

#: src/pages/account-statuses.jsx:331
msgid "Clear filters"
msgstr ""

#: src/pages/account-statuses.jsx:348
msgid "Showing post with replies"
msgstr ""

#: src/pages/account-statuses.jsx:353
msgid "+ Replies"
msgstr ""

#: src/pages/account-statuses.jsx:359
msgid "Showing posts without boosts"
msgstr ""

#: src/pages/account-statuses.jsx:364
msgid "- Boosts"
msgstr ""

#: src/pages/account-statuses.jsx:370
msgid "Showing posts with media"
msgstr ""

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:387
msgid "Showing posts tagged with #{0}"
msgstr ""

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:426
msgid "Showing posts in {0}"
msgstr "Showing posts in {0}"

#: src/pages/account-statuses.jsx:516
msgid "Nothing to see here yet."
msgstr ""

#: src/pages/account-statuses.jsx:517
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr ""

#: src/pages/account-statuses.jsx:558
#: src/pages/account-statuses.jsx:588
msgid "Unable to fetch account info"
msgstr ""

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:565
msgid "Switch to account's instance {0}"
msgstr ""

#: src/pages/account-statuses.jsx:595
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr ""

#: src/pages/account-statuses.jsx:668
msgid "Month"
msgstr ""

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr ""

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr ""

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "Switch to this account"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "Switch in new tab/window"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr ""

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr ""

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr ""

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr ""

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr "Connected on {0} (<0/>)"

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr ""

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr ""

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "No bookmarks yet. Go bookmark something!"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr ""

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr ""

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr ""

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr ""

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr ""

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr ""

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr ""

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr ""

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr ""

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr ""

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr ""

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr ""

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr ""

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr ""

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr ""

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr ""

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr ""

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr ""

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr ""

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr ""

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Preview of Catch-up UI"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr ""

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr ""

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr ""

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "until the max"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr ""

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr ""

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr ""

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr ""

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr ""

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr ""

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "Remove this catch-up?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr "Removing Catch-up {0}"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr "Catch-up {0} removed"

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr ""

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr ""

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr ""

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr ""

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr ""

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr ""

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:330
msgid "All"
msgstr ""

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr ""

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr ""

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Date"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Density"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr "Group"

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "Authors"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "None"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr ""

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "You don't have to read everything."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "That's all."

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr ""

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr ""

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr ""

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr ""

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr ""

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr ""

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr ""

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr ""

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr ""

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "No likes yet. Go like something!"

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr ""

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr ""

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr ""

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr ""

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr ""

#: src/pages/filters.jsx:42
msgid "Never"
msgstr ""

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr ""

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr ""

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr ""

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr ""

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr ""

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr ""

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr ""

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr ""

#: src/pages/filters.jsx:356
msgid "Title"
msgstr ""

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr ""

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr ""

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr ""

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr ""

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr ""

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr ""

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr ""

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr ""

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr ""

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr ""

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr "obscured (media only)"

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr ""

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr ""

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr ""

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr ""

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr ""

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr ""

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr ""

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr ""

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr ""

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr ""

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr ""

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr ""

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr ""

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr ""

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr ""

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr ""

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr ""

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr ""

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "Unfollow #{hashtag}?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr ""

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr ""

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr ""

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr ""

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr ""

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr ""

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr ""

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr ""

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr ""

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr ""

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr ""

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr ""

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr ""

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr ""

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr ""

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr ""

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr ""

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr ""

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr ""

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr ""

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr ""

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr ""

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr ""

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr ""

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr ""

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr ""

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr ""

#: src/pages/login.jsx:122
#: src/pages/login.jsx:132
msgid "Failed to register application"
msgstr "Failed to register application"

#: src/pages/login.jsx:218
msgid "instance domain"
msgstr "instance domain"

#: src/pages/login.jsx:242
msgid "e.g. “mastodon.social”"
msgstr ""

#: src/pages/login.jsx:253
msgid "Failed to log in. Please try again or try another instance."
msgstr ""

#: src/pages/login.jsx:265
msgid "Continue with {selectedInstanceText}"
msgstr ""

#: src/pages/login.jsx:266
msgid "Continue"
msgstr ""

#: src/pages/login.jsx:274
msgid "Don't have an account? Create one!"
msgstr ""

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr ""

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr ""

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr ""

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr ""

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr ""

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr ""

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr ""

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr ""

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr ""

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr ""

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr ""

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr ""

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr ""

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr ""

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr ""

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr ""

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr ""

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr ""

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr ""

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr ""

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr ""

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr ""

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr ""

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr ""

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr ""

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr ""

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr ""

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr ""

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr ""

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr ""

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr "Notifications from @{0} will not show up in Filtered notifications from now on."

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr ""

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr ""

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr ""

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr ""

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr ""

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr ""

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr ""

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr ""

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "Switch to Federated"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "Switch to Local"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr "No scheduled posts."

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr "Scheduled <0><1/></0> <2>({0})</2>"

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr "Scheduled <0><1/></0>"

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr "Scheduled post rescheduled"

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr "Failed to reschedule post"

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr "Reschedule"

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr "Delete scheduled post?"

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr "Scheduled post deleted"

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr "Failed to delete scheduled post"

#: src/pages/search.jsx:51
msgid "Search: {q} (Posts)"
msgstr ""

#: src/pages/search.jsx:54
msgid "Search: {q} (Accounts)"
msgstr ""

#: src/pages/search.jsx:57
msgid "Search: {q} (Hashtags)"
msgstr ""

#: src/pages/search.jsx:60
msgid "Search: {q}"
msgstr ""

#: src/pages/search.jsx:340
#: src/pages/search.jsx:422
msgid "Hashtags"
msgstr ""

#: src/pages/search.jsx:372
#: src/pages/search.jsx:426
#: src/pages/search.jsx:496
msgid "See more"
msgstr ""

#: src/pages/search.jsx:398
msgid "See more accounts"
msgstr ""

#: src/pages/search.jsx:412
msgid "No accounts found."
msgstr ""

#: src/pages/search.jsx:468
msgid "See more hashtags"
msgstr ""

#: src/pages/search.jsx:482
msgid "No hashtags found."
msgstr ""

#: src/pages/search.jsx:526
msgid "See more posts"
msgstr ""

#: src/pages/search.jsx:540
msgid "No posts found."
msgstr ""

#: src/pages/search.jsx:585
msgid "Enter your search term or paste a URL above to get started."
msgstr ""

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr ""

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr ""

#: src/pages/settings.jsx:180
msgid "Light"
msgstr ""

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr ""

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr ""

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr ""

#. Preview of one character, in largest size
#. Preview of one character, in smallest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr ""

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr ""

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "Volunteer translations"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr ""

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr ""

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr ""

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr ""

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr ""

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr ""

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr ""

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr ""

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr ""

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr "Translate to "

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr ""

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr ""

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr ""

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr ""

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr ""

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr ""

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr ""

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr ""

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr ""

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr ""

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr ""

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr ""

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr ""

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr ""

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr ""

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr ""

#: src/pages/settings.jsx:710
msgid "About"
msgstr ""

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr ""

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr ""

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Donate"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr "What's new"

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr ""

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr ""

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr ""

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr ""

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr ""

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr ""

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr ""

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr ""

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr ""

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr ""

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr ""

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr ""

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr ""

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr ""

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr ""

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr ""

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr ""

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr ""

#. js-lingui-explicit-id
#: src/pages/status.jsx:658
#: src/pages/status.jsx:1221
msgid "post.title"
msgstr "Post"

#: src/pages/status.jsx:912
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr ""

#: src/pages/status.jsx:932
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr ""

#: src/pages/status.jsx:960
msgid "Error: {e}"
msgstr ""

#: src/pages/status.jsx:967
msgid "Switch to my instance to enable interactions"
msgstr ""

#: src/pages/status.jsx:1069
msgid "Unable to load replies."
msgstr ""

#: src/pages/status.jsx:1181
msgid "Back"
msgstr ""

#: src/pages/status.jsx:1212
msgid "Go to main post"
msgstr ""

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1235
msgid "{0} posts above ‒ Go to top"
msgstr ""

#: src/pages/status.jsx:1282
#: src/pages/status.jsx:1345
msgid "Switch to Side Peek view"
msgstr ""

#: src/pages/status.jsx:1346
msgid "Switch to Full view"
msgstr ""

#: src/pages/status.jsx:1364
msgid "Show all sensitive content"
msgstr ""

#: src/pages/status.jsx:1369
msgid "Experimental"
msgstr ""

#: src/pages/status.jsx:1378
msgid "Unable to switch"
msgstr ""

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1385
msgid "Switch to post's instance ({0})"
msgstr "Switch to post's instance ({0})"

#: src/pages/status.jsx:1388
msgid "Switch to post's instance"
msgstr ""

#: src/pages/status.jsx:1446
msgid "Unable to load post"
msgstr ""

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1582
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr ""

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1600
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr ""

#: src/pages/status.jsx:1622
msgid "View post with its replies"
msgstr ""

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr ""

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr ""

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr "By {0}"

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr ""

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr ""

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr ""

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr ""

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr ""

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr ""

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr ""

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr ""

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr ""

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr ""

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr ""

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr ""

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr ""

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr ""

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr ""

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr ""

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr ""

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr ""

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr ""

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr ""

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr ""

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr ""

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr ""

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr ""

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr ""

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr ""

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr ""
