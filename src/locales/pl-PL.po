msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: pl\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-08-05 04:12\n"
"Last-Translator: \n"
"Language-Team: Polish\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: pl\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:141
msgid "Locked"
msgstr "Zablokowane"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:163
msgid "Posts: {0}"
msgstr "Wpisy: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:168
msgid "Last posted: {0}"
msgstr "Ostatni wpis: {0}"

#: src/components/account-block.jsx:183
#: src/components/account-info.jsx:705
msgid "Automated"
msgstr "Bot"

#: src/components/account-block.jsx:190
#: src/components/account-info.jsx:710
#: src/components/status.jsx:748
msgid "Group"
msgstr "Grupa"

#: src/components/account-block.jsx:200
msgid "Mutual"
msgstr "Znajomi"

#: src/components/account-block.jsx:204
#: src/components/account-info.jsx:1907
msgid "Requested"
msgstr "Oczekująca prośba"

#: src/components/account-block.jsx:208
#: src/components/account-info.jsx:1898
msgid "Following"
msgstr "Obserwujesz"

#: src/components/account-block.jsx:212
#: src/components/account-info.jsx:1212
msgid "Follows you"
msgstr "Obserwuje cię"

#: src/components/account-block.jsx:220
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, one {# obserwujący} few {# obserwujących} many {# obserwujących} other {# obserwujących}}"

#: src/components/account-block.jsx:229
#: src/components/account-info.jsx:753
msgid "Verified"
msgstr "Zweryfikowano"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:244
#: src/components/account-info.jsx:892
msgid "Joined <0>{0}</0>"
msgstr "Jest tutaj od <0>{0}</0>"

#: src/components/account-info.jsx:64
msgid "Forever"
msgstr "Na zawsze"

#: src/components/account-info.jsx:398
msgid "Unable to load account."
msgstr "Nie udało się załadować konta."

#: src/components/account-info.jsx:413
msgid "Go to account page"
msgstr "Przejdź na stronę konta"

#: src/components/account-info.jsx:442
#: src/components/account-info.jsx:775
msgid "Followers"
msgstr "Obserwujący"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:446
#: src/components/account-info.jsx:830
msgid "following.stats"
msgstr "obserwowanych"

#: src/components/account-info.jsx:449
#: src/pages/account-statuses.jsx:495
#: src/pages/search.jsx:345
#: src/pages/search.jsx:492
msgid "Posts"
msgstr "Wpisy"

#: src/components/account-info.jsx:457
#: src/components/account-info.jsx:1268
#: src/components/compose.jsx:2819
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1998
#: src/components/status.jsx:2015
#: src/components/status.jsx:2140
#: src/components/status.jsx:2769
#: src/components/status.jsx:2772
#: src/pages/account-statuses.jsx:539
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1306
#: src/pages/trending.jsx:474
msgid "More"
msgstr "Więcej"

#: src/components/account-info.jsx:469
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> używa teraz nowego konta:"

#: src/components/account-info.jsx:614
#: src/components/account-info.jsx:1496
msgid "Handle copied"
msgstr "Skopiowano identyfikator"

#: src/components/account-info.jsx:617
#: src/components/account-info.jsx:1499
msgid "Unable to copy handle"
msgstr "Nie udało się skopiować identyfikatora"

#: src/components/account-info.jsx:623
#: src/components/account-info.jsx:1505
msgid "Copy handle"
msgstr "Kopiuj identyfikator"

#: src/components/account-info.jsx:629
msgid "Go to original profile page"
msgstr "Przejdź na oryginalną stronę profilową"

#: src/components/account-info.jsx:647
msgid "View profile image"
msgstr "Pokaż zdjęcie profilowe"

#: src/components/account-info.jsx:665
msgid "View profile header"
msgstr "Pokaż baner profilu"

#: src/components/account-info.jsx:681
#: src/components/account-info.jsx:1806
#: src/components/account-info.jsx:2332
msgid "Edit profile"
msgstr "Edytuj profil"

#: src/components/account-info.jsx:700
msgid "In Memoriam"
msgstr "In memoriam"

#: src/components/account-info.jsx:782
#: src/components/account-info.jsx:840
msgid "This user has chosen to not make this information available."
msgstr "Ta osoba nie udostępnia tych informacji."

#. placeholder {0}: shortenNumber(followersCount)
#. placeholder {1}: shortenNumber(followersCount)
#: src/components/account-info.jsx:802
msgid "{followersCount, plural, one {<0>{0}</0> Follower} other {<1>{1}</1> Followers}}"
msgstr ""

#. placeholder {0}: shortenNumber(followingCount)
#: src/components/account-info.jsx:846
msgid "{followingCount, plural, other {<0>{0}</0> Following}}"
msgstr ""

#. placeholder {0}: shortenNumber(statusesCount)
#. placeholder {1}: shortenNumber(statusesCount)
#: src/components/account-info.jsx:870
msgid "{statusesCount, plural, one {<0>{0}</0> Post} other {<1>{1}</1> Posts}}"
msgstr ""

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:917
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "wpisy: {0}, odpowiedzi: {1}, podbicia: {2}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:933
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural, one {{1, plural, one {Ostatni wpis w ciągu minionych 24 godzin} few {Ostatni wpis w ciągu minionych {2} dni} many {Ostatni wpis w ciągu minionych {2} dni} other {Ostatni wpis w ciągu minionych {2} dni}}} few {{3, plural, one {Ostatni {4} wpis w ciągu minionych 24 godzin} few {Ostatnie {5} wpisy w ciągu minionych {6} dni} many {Ostatnie {5} wpisów w ciągu minionych {6} dni} other {Ostatnie {5} wpisów w ciągu minionych {6} dni}}} many {{3, plural, one {Ostatni {4} wpis w ciągu minionych 24 godzin} few {Ostatnie {5} wpisy w ciągu minionych {6} dni} many {Ostatnie {5} wpisów w ciągu minionych {6} dni} other {Ostatnie {5} wpisów w ciągu minionych {6} dni}}} other {{3, plural, one {Ostatni wpis w ciągu minionych 24 godzin} few {Ostatnie {5} wpisy w ciągu minionych {6} dni} many {Ostatnie {5} wpisów w ciągu minionych {6} dni} other {Ostatnie {5} wpisów w ciągu minionych {6} dni}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:949
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {Ostatni wpis w ciągu minionego roku} few {Ostatnie {1} wpisy w ciągu minionego roku} many {Ostatnie {1} wpisów w ciągu minionego roku} other {Ostatnie {1} wpisów w ciągu minionego roku}}"

#: src/components/account-info.jsx:974
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "Oryginalne"

#: src/components/account-info.jsx:978
#: src/components/status.jsx:2553
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1025
#: src/pages/status.jsx:1652
msgid "Replies"
msgstr "Odpowiedzi"

#: src/components/account-info.jsx:982
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "Podbicia"

#: src/components/account-info.jsx:988
msgid "Post stats unavailable."
msgstr "Statystyki wpisów nie są dostępne."

#: src/components/account-info.jsx:1019
msgid "View post stats"
msgstr "Pokaż statystyki wpisów"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1216
msgid "Last post: <0>{0}</0>"
msgstr "Ostatni wpis: <0>{0}</0>"

#: src/components/account-info.jsx:1230
msgid "Muted"
msgstr "Wyciszono"

#: src/components/account-info.jsx:1235
msgid "Blocked"
msgstr "Zablokowano"

#: src/components/account-info.jsx:1244
msgid "Private note"
msgstr "Prywatna notatka"

#: src/components/account-info.jsx:1301
msgid "Mention <0>@{username}</0>"
msgstr "Wspomnij o <0>@{username}</0>"

#: src/components/account-info.jsx:1313
msgid "Translate bio"
msgstr "Przetłumacz biogram"

#: src/components/account-info.jsx:1324
msgid "Edit private note"
msgstr "Edytuj prywatną notatkę"

#: src/components/account-info.jsx:1324
msgid "Add private note"
msgstr "Dodaj prywatną notatkę"

#: src/components/account-info.jsx:1344
msgid "Notifications enabled for @{username}'s posts."
msgstr "Włączono powiadomienia dla wpisów od @{username}."

#: src/components/account-info.jsx:1345
msgid " Notifications disabled for @{username}'s posts."
msgstr " Wyłączono powiadomienia dla wpisów od @{username}."

#: src/components/account-info.jsx:1357
msgid "Disable notifications"
msgstr "Wyłącz powiadomienia"

#: src/components/account-info.jsx:1358
msgid "Enable notifications"
msgstr "Włącz powiadomienia"

#: src/components/account-info.jsx:1375
msgid "Boosts from @{username} enabled."
msgstr "Włączono podbicia od @{username}."

#: src/components/account-info.jsx:1376
msgid "Boosts from @{username} disabled."
msgstr "Wyłączono podbicia od @{username}."

#: src/components/account-info.jsx:1387
msgid "Disable boosts"
msgstr "Wyłącz podbicia"

#: src/components/account-info.jsx:1387
msgid "Enable boosts"
msgstr "Włącz podbicia"

#: src/components/account-info.jsx:1406
msgid "@{username} is no longer featured on your profile."
msgstr "Cofnięto wyróżnienie @{username} na Twoim profilu."

#: src/components/account-info.jsx:1416
msgid "@{username} is now featured on your profile."
msgstr "Wyróżniono @{username} na Twoim profilu."

#: src/components/account-info.jsx:1424
msgid "Unable to unfeature @{username} on your profile."
msgstr "Nie udało się cofnąć wyróżnienia @{username} na Twoim profilu."

#: src/components/account-info.jsx:1428
msgid "Unable to feature @{username} on your profile."
msgstr "Nie udało się wyróżnić @{username} na Twoim profilu."

#: src/components/account-info.jsx:1437
msgid "Don't feature on profile"
msgstr "Nie wyróżniaj na profilu"

#: src/components/account-info.jsx:1438
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "Wyróżnij na profilu"

#: src/components/account-info.jsx:1447
msgid "Show featured profiles"
msgstr "Pokaż wyróżnione profile"

#: src/components/account-info.jsx:1462
#: src/components/account-info.jsx:1472
#: src/components/account-info.jsx:2078
msgid "Add/Remove from Lists"
msgstr "Dodaj/usuń z list"

#: src/components/account-info.jsx:1522
#: src/components/status.jsx:1420
msgid "Link copied"
msgstr "Skopiowano odnośnik"

#: src/components/account-info.jsx:1525
#: src/components/status.jsx:1423
msgid "Unable to copy link"
msgstr "Nie udało się skopiować odnośnika"

#: src/components/account-info.jsx:1531
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1429
#: src/components/status.jsx:3547
msgid "Copy"
msgstr "Kopiuj"

#: src/components/account-info.jsx:1546
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1445
msgid "Sharing doesn't seem to work."
msgstr "Udostępnianie zdaje się nie działać."

#: src/components/account-info.jsx:1552
#: src/components/status.jsx:1451
msgid "Share…"
msgstr "Udostępnij…"

#: src/components/account-info.jsx:1572
msgid "Unmuted @{username}"
msgstr "Wyłączono wyciszenie @{username}"

#: src/components/account-info.jsx:1584
msgid "Unmute <0>@{username}</0>"
msgstr "Nie wyciszaj <0>@{username}</0>"

#: src/components/account-info.jsx:1600
msgid "Mute <0>@{username}</0>…"
msgstr "Wycisz <0>@{username}</0>…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1632
msgid "Muted @{username} for {0}"
msgstr "Wyciszono @{username} na {0}"

#: src/components/account-info.jsx:1644
msgid "Unable to mute @{username}"
msgstr "Nie udało się wyciszyć @{username}"

#: src/components/account-info.jsx:1665
msgid "Remove <0>@{username}</0> from followers?"
msgstr "Usunąć <0>@{username}</0> z obserwujących?"

#: src/components/account-info.jsx:1685
msgid "@{username} removed from followers"
msgstr "Usunięto @{username} z obserwujących"

#: src/components/account-info.jsx:1697
msgid "Remove follower…"
msgstr "Usuń z obserwujących…"

#: src/components/account-info.jsx:1708
msgid "Block <0>@{username}</0>?"
msgstr "Zablokować <0>@{username}</0>?"

#: src/components/account-info.jsx:1732
msgid "Unblocked @{username}"
msgstr "Odblokowano @{username}"

#: src/components/account-info.jsx:1740
msgid "Blocked @{username}"
msgstr "Zablokowano @{username}"

#: src/components/account-info.jsx:1748
msgid "Unable to unblock @{username}"
msgstr "Nie udało się odblokować @{username}"

#: src/components/account-info.jsx:1750
msgid "Unable to block @{username}"
msgstr "Nie udało się zablokować @{username}"

#: src/components/account-info.jsx:1760
msgid "Unblock <0>@{username}</0>"
msgstr "Odblokuj <0>@{username}</0>"

#: src/components/account-info.jsx:1769
msgid "Block <0>@{username}</0>…"
msgstr "Zablokuj <0>@{username}</0>…"

#: src/components/account-info.jsx:1786
msgid "Report <0>@{username}</0>…"
msgstr "Zgłoś <0>@{username}</0>…"

#: src/components/account-info.jsx:1842
msgid "Withdraw follow request?"
msgstr "Wycofać prośbę o obserwację?"

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1843
msgid "Unfollow @{0}?"
msgstr "Przestać obserwować @{0}?"

#: src/components/account-info.jsx:1901
msgid "Unfollow…"
msgstr "Nie obserwuj…"

#: src/components/account-info.jsx:1910
msgid "Withdraw…"
msgstr "Wycofaj…"

#: src/components/account-info.jsx:1917
#: src/components/account-info.jsx:1921
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "Obserwuj"

#: src/components/account-info.jsx:2018
#: src/components/account-info.jsx:2073
#: src/components/account-info.jsx:2207
#: src/components/account-info.jsx:2327
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:892
#: src/components/compose.jsx:2775
#: src/components/compose.jsx:3255
#: src/components/compose.jsx:3464
#: src/components/compose.jsx:3694
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:118
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3271
#: src/components/status.jsx:3511
#: src/components/status.jsx:4020
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1393
msgid "Close"
msgstr "Zamknij"

#: src/components/account-info.jsx:2023
msgid "Translated Bio"
msgstr "Przetłumaczony biogram"

#: src/components/account-info.jsx:2118
msgid "Unable to remove from list."
msgstr "Nie udało się usunąć z listy."

#: src/components/account-info.jsx:2119
msgid "Unable to add to list."
msgstr "Nie udało się dodać do listy."

#: src/components/account-info.jsx:2138
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "Nie udało się wczytać list."

#: src/components/account-info.jsx:2142
msgid "No lists."
msgstr "Brak list."

#: src/components/account-info.jsx:2153
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "Nowa lista"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2212
msgid "Private note about <0>@{0}</0>"
msgstr "Prywatna notatka o <0>@{0}</0>"

#: src/components/account-info.jsx:2242
msgid "Unable to update private note."
msgstr "Nie udało się zaktualizować notatki."

#: src/components/account-info.jsx:2265
#: src/components/account-info.jsx:2563
msgid "Cancel"
msgstr "Anuluj"

#: src/components/account-info.jsx:2270
msgid "Save & close"
msgstr "Zapisz i zamknij"

#: src/components/account-info.jsx:2387
msgid "Unable to update profile."
msgstr "Nie udało się zaktualizować profilu."

#: src/components/account-info.jsx:2394
msgid "Header picture"
msgstr "Obrazek w nagłówku"

#: src/components/account-info.jsx:2446
msgid "Profile picture"
msgstr "Zdjęcie profilowe"

#: src/components/account-info.jsx:2498
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "Nazwa"

#: src/components/account-info.jsx:2511
msgid "Bio"
msgstr "Biogram"

#: src/components/account-info.jsx:2524
msgid "Extra fields"
msgstr "Dodatkowe pola"

#: src/components/account-info.jsx:2530
msgid "Label"
msgstr "Etykieta"

#: src/components/account-info.jsx:2533
msgid "Content"
msgstr "Zawartość"

#: src/components/account-info.jsx:2566
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "Zapisz"

#: src/components/account-info.jsx:2620
msgid "username"
msgstr "nazwa użytkownika"

#: src/components/account-info.jsx:2624
msgid "server domain name"
msgstr "nazwa domeny serwera"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2690
msgid "Profiles featured by @{0}"
msgstr "Profile wyróżnione przez @{0}"

#: src/components/account-info.jsx:2716
msgid "No featured profiles."
msgstr "Brak wyróżnionych profili."

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "Tryb \"maskowanie\" jest wyłączony"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "Tryb \"maskowanie\" jest włączony"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "Strona główna"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "Utwórz wpis"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr "Zaplanowane wpisy"

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr "Dodaj do wątku"

#: src/components/compose.jsx:212
msgid "Take photo or video"
msgstr "Zrób zdjęcie lub wideo"

#: src/components/compose.jsx:213
msgid "Add media"
msgstr "Dodaj media"

#: src/components/compose.jsx:214
msgid "Add custom emoji"
msgstr "Dodaj własne emoji"

#: src/components/compose.jsx:215
msgid "Add GIF"
msgstr "Dodaj GIF"

#: src/components/compose.jsx:216
msgid "Add poll"
msgstr "Dodaj ankietę"

#: src/components/compose.jsx:217
msgid "Schedule post"
msgstr "Zaplanuj wpis"

#: src/components/compose.jsx:417
msgid "You have unsaved changes. Discard this post?"
msgstr "Masz niezapisane zmiany. Czy chcesz porzucić ten wpis?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:655
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr "{0, plural, one {Plik {1} nie jest obsługiwany.} few {Pliki {2} nie są obsługiwane.} many {Pliki {2} nie są obsługiwane.} other {Pliki {2} nie są obsługiwane.}}"

#: src/components/compose.jsx:665
#: src/components/compose.jsx:683
#: src/components/compose.jsx:1795
#: src/components/compose.jsx:1930
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, one {Możesz załączyć co najwyżej # plik.} few {Możesz załączyć co najwyżej # pliki.} many {Możesz załączyć co najwyżej # plików.} other {Możesz załączyć co najwyżej # pliku.}}"

#: src/components/compose.jsx:873
msgid "Pop out"
msgstr "Przenieś do wyskakującego okienka"

#: src/components/compose.jsx:880
msgid "Minimize"
msgstr "Zminimalizuj"

#: src/components/compose.jsx:916
msgid "Looks like you closed the parent window."
msgstr "Wygląda na to, że zamknięto okno nadrzędne."

#: src/components/compose.jsx:923
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Wygląda na to, że masz też otwarte pole tworzenia wpisu w oknie głównym, gdzie aktualnie trwa publikowanie. Poczekaj, aż skończy i spróbuj ponownie później."

#: src/components/compose.jsx:928
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Wygląda na to, że już masz otwarte pole edycji w oknie nadrzędnym. Aktywowanie tego okna spowoduje skasowanie zmian dokonanych przez Ciebie w oknie nadrzędnym. Kontynuować?"

#: src/components/compose.jsx:971
msgid "Pop in"
msgstr "Przenieś z powrotem do okna głównego"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:981
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "Odpowiedz na wpis @{0}(<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:991
msgid "Replying to @{0}’s post"
msgstr "Odpowiedz na wpis @{0}"

#: src/components/compose.jsx:1004
msgid "Editing source post"
msgstr "Edycja wpisu źródłowego"

#: src/components/compose.jsx:1057
msgid "Poll must have at least 2 options"
msgstr "Ankieta musi mieć co najmniej 2 opcje"

#: src/components/compose.jsx:1061
msgid "Some poll choices are empty"
msgstr "Niektóre opcje ankiety są nieuzupełnione"

#: src/components/compose.jsx:1074
msgid "Some media have no descriptions. Continue?"
msgstr "Niektóre media nie posiadają opisów. Kontynuować?"

#: src/components/compose.jsx:1126
msgid "Attachment #{i} failed"
msgstr "Załączenie #{i} nie powiodło się"

#: src/components/compose.jsx:1222
#: src/components/status.jsx:2328
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "Ostrzeżenie o zawartości"

#: src/components/compose.jsx:1238
msgid "Content warning or sensitive media"
msgstr "Ostrzeżenie o treściach lub wrażliwych plikach"

#: src/components/compose.jsx:1274
#: src/components/status.jsx:99
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "Publiczny"

#: src/components/compose.jsx:1279
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:100
msgid "Local"
msgstr "Lokalne"

#: src/components/compose.jsx:1283
#: src/components/status.jsx:101
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "Niepubliczny"

#: src/components/compose.jsx:1286
#: src/components/status.jsx:102
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "Tylko obserwujący"

#: src/components/compose.jsx:1289
#: src/components/status.jsx:103
#: src/components/status.jsx:2204
msgid "Private mention"
msgstr "Prywatna wzmianka"

#: src/components/compose.jsx:1298
msgid "Post your reply"
msgstr "Opublikuj odpowiedź"

#: src/components/compose.jsx:1300
msgid "Edit your post"
msgstr "Edytuj swój wpis"

#: src/components/compose.jsx:1301
msgid "What are you doing?"
msgstr "Co robisz?"

#: src/components/compose.jsx:1380
msgid "Mark media as sensitive"
msgstr "Oznacz media jako \"wrażliwe\""

#: src/components/compose.jsx:1417
msgid "Posting on <0/>"
msgstr "Opublikowano <0/>"

#: src/components/compose.jsx:1448
#: src/components/compose.jsx:3313
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "Dodaj"

#: src/components/compose.jsx:1676
msgid "Schedule"
msgstr "Zaplanuj"

#: src/components/compose.jsx:1678
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1192
#: src/components/status.jsx:1978
#: src/components/status.jsx:1979
#: src/components/status.jsx:2673
msgid "Reply"
msgstr "Odpowiedz"

#: src/components/compose.jsx:1680
msgid "Update"
msgstr "Zaktualizuj"

#: src/components/compose.jsx:1681
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Opublikuj"

#: src/components/compose.jsx:1807
msgid "Downloading GIF…"
msgstr "Pobieram GIF…"

#: src/components/compose.jsx:1835
msgid "Failed to download GIF"
msgstr "Pobieranie GIF'a nie powiodło się"

#: src/components/compose.jsx:2060
#: src/components/compose.jsx:2153
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "Więcej…"

#: src/components/compose.jsx:2589
msgid "Uploaded"
msgstr "Załadowano"

#: src/components/compose.jsx:2602
msgid "Image description"
msgstr "Opis obrazka"

#: src/components/compose.jsx:2603
msgid "Video description"
msgstr "Opis wideo"

#: src/components/compose.jsx:2604
msgid "Audio description"
msgstr "Opis audio"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2639
#: src/components/compose.jsx:2659
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "Rozmiar pliku jest za duży. Upload może spowodować błędy. Spróbuj zredukować rozmiar pliku z {0} do maksymalnie {1}."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2651
#: src/components/compose.jsx:2671
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr "Wymiary są za duże. Upload może spowodować błędy. Spróbuj zredukować wymiary z {0}×{1} do {2}×{3}px."

#: src/components/compose.jsx:2679
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "Liczba klatek na sekundę jest za wysoka. Upload może spowodować błędy."

#: src/components/compose.jsx:2739
#: src/components/compose.jsx:2989
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "Usuń"

#: src/components/compose.jsx:2756
#: src/compose.jsx:84
msgid "Error"
msgstr "Błąd"

#: src/components/compose.jsx:2781
msgid "Edit image description"
msgstr "Edytuj opis obrazka"

#: src/components/compose.jsx:2782
msgid "Edit video description"
msgstr "Edytuj opis wideo"

#: src/components/compose.jsx:2783
msgid "Edit audio description"
msgstr "Edytuj opis audio"

#: src/components/compose.jsx:2828
#: src/components/compose.jsx:2877
msgid "Generating description. Please wait…"
msgstr "Generowanie opisu. Proszę czekać…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2848
msgid "Failed to generate description: {0}"
msgstr "Błąd podczas generowania opisu: {0}"

#: src/components/compose.jsx:2849
msgid "Failed to generate description"
msgstr "Błąd przy generowaniu opisu."

#: src/components/compose.jsx:2861
#: src/components/compose.jsx:2867
#: src/components/compose.jsx:2913
msgid "Generate description…"
msgstr "Wygeneruj opis…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2900
msgid "Failed to generate description{0}"
msgstr "Błąd podczas generowania opisu{0}"

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2915
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>— eksperymentalne</0>"

#: src/components/compose.jsx:2934
msgid "Done"
msgstr "Zrobione"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2970
msgid "Choice {0}"
msgstr "Wybór {0}"

#: src/components/compose.jsx:3017
msgid "Multiple choices"
msgstr "Wielokrotny wybór"

#: src/components/compose.jsx:3020
msgid "Duration"
msgstr "Czas trwania"

#: src/components/compose.jsx:3051
msgid "Remove poll"
msgstr "Usuń ankietę"

#: src/components/compose.jsx:3272
msgid "Search accounts"
msgstr "Szukaj kont"

#: src/components/compose.jsx:3326
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "Błąd ładowania kont"

#: src/components/compose.jsx:3470
msgid "Custom emojis"
msgstr "Własne emoji"

#: src/components/compose.jsx:3490
msgid "Search emoji"
msgstr "Szkaj emoji"

#: src/components/compose.jsx:3521
msgid "Error loading custom emojis"
msgstr "Błąd podczas ładowania własnych emoji."

#: src/components/compose.jsx:3532
msgid "Recently used"
msgstr "Niedawno użyte"

#: src/components/compose.jsx:3533
msgid "Others"
msgstr "Inne"

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3571
msgid "{0} more…"
msgstr "{0} więcej…"

#: src/components/compose.jsx:3709
msgid "Search GIFs"
msgstr "Znajdź GIF"

#: src/components/compose.jsx:3724
msgid "Powered by GIPHY"
msgstr "Obsługiwane przez GIPHY"

#: src/components/compose.jsx:3732
msgid "Type to search GIFs"
msgstr "Zacznij pisać, żeby wyszukać GIF'a"

#: src/components/compose.jsx:3830
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "Poprzednie"

#: src/components/compose.jsx:3848
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "Następne"

#: src/components/compose.jsx:3865
msgid "Error loading GIFs"
msgstr "Wystąpił błąd podczas wczytywania GIFów"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "Nieopublikowane szkice"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "Wygląda na to, że masz nieopublikowane szkice. Kontynuujmy od tego samego miejsca."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "Usunąć ten szkic?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "Wystąpił błąd podczas usuwania szkicu! Spróbuj ponownie."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1595
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "Usuń…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "Wystąpił błąd podczas wczytywania statusu odpowiedzi!"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "Usunąć wszystkie szkice?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "Wystąpił błąd podczas usuwania szkiców! Spróbuj ponownie."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "Usuń wszystko…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "Nie znaleziono żadnych szkiców."

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "Ankieta"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:375
msgid "Media"
msgstr "Multimedia"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "Otwórz w nowym oknie"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "Akceptuj"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "Odrzuć"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "Zaakceptowano"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "Odrzucono"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:335
#: src/pages/search.jsx:368
msgid "Accounts"
msgstr "Konta"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:562
#: src/pages/status.jsx:1426
msgid "Show more…"
msgstr "Pokaż więcej…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:567
msgid "The end."
msgstr "Koniec."

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "Nie ma nic do wyświetlenia"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "Skróty klawiszowe"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "Pomoc dotycząca skrótów klawiszowych"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "Następny wpis"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "Poprzedni wpis"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "Przejdź w \"karuzeli\" do nowego wpisu"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Shift</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "Przejdź w \"karuzeli\" do poprzedniego wpisu"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Shift</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "Wczytaj nowe wpisy"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "Otwórz szczegóły wpisu"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Enter</0> lub <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "Rozwiń ostrzeżenie o zawartości<0/>Rozwiń lub zwiń wątek"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "Zamknij wpis lub okna dialogowe"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc</0> lub <1>Backspace</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "Aktywuj kolumnę w trybie rozszerzonym"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> do <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr "Przejdź do następnej kolumny w trybie rozszerzonym"

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr "Przejdź do poprzedniej kolumny w trybie rozszerzonym"

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "Utwórz nowy wpis"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "Utwórz nowy wpis (w nowym oknie)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Shift</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "Opublikuj wpis"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Ctrl</0> + <1>Enter</1> lub <2>⌘</2> + <3>Enter</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:204
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:47
#: src/pages/search.jsx:317
msgid "Search"
msgstr "Szukaj"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "Odpowiedz (nowe okno)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Shift</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "Polub (ulubione)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> or <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1200
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
#: src/components/status.jsx:2724
msgid "Boost"
msgstr "Podbij"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Shift</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
#: src/components/status.jsx:2749
msgid "Bookmark"
msgstr "Zakładka"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "Przełącz tryb \"maskowania\""

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Shift</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "Edytuj listę"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "Nie da się edytować listy."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "Nie da się stworzyć listy."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "Pokaż odpowiedzi członkom listy"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "Pokaż odpowiedzi ludziom, których śledzę"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "Nie pokazuj odpowiedzi"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "Ukryj posty z tej listy na Stronie głównej/Obserwowanych"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "Utwórz"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "Skasować tą listę?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "Nie można skasować listy."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr "Wpisy z tej listy są ukryte na osi głównej/obserwowanych"

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "Opis mediów"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1306
#: src/components/status.jsx:1315
#: src/components/translation-block.jsx:239
msgid "Translate"
msgstr "Przetłumacz"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1334
msgid "Speak"
msgstr "Powiedz"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "Otwórz oryginalny plik w nowym oknie"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "Otwórz oryginalny plik"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "Próba opisania obrazka. Proszę czekać..."

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "Błąd podczas opisywania obrazka"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "Opisz obrazek"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "Zobacz wpis"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "Media wrażliwe"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "Odfiltrowano: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3850
#: src/components/status.jsx:3946
#: src/components/status.jsx:4024
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "Odfiltrowane"

#: src/components/media.jsx:479
msgid "Open file"
msgstr "Otwórz plik"

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr "Zaplanowany wpis"

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "Wpis opublikowany. Kliknij tutaj, żeby go sprawdzić."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr "Odpowiedź zaplanowana"

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "Odpowiedź wysłana. Kliknij tutaj, żeby ją sprawdzić."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "Wpis został zaktualizowany. Kliknij tutaj, żeby go sprawdzić."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "Menu"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "Odświeżyć stronę teraz, żeby zaktualizować?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "Nowa aktualizacja dostępna…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr "following.title"

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "Nadrabianie zaległości"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "Wzmianki"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "Powiadomienia"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "Nowe"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "Profil"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "Zakładki"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "Polubienia"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "Obserwowane hashtagi"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:341
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "Filtry"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "Wyciszeni użytkownicy"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "Wyciszeni użytkownicy…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "Zablokowani"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "Zablokowani…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "Konta…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:199
#: src/pages/status.jsx:925
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "Zaloguj się"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "Popularne"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "Globalne (sfederowane)"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "Skróty / Kolumny…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "Ustawienia…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "Listy"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "Wszystkie listy"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "Powiadomienie"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "To powiadomienie pochodzi z twojego innego konta."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "Wyświetl wszystkie powiadomienia"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} reaguje na twój wpis {emojiObject}"

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} publikuje wpis."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} podbija twoją odpowiedź.} other {{account} podbija twój wpis.}}} few {{postType, select, reply {{account} podbija {postsCount} twoje odpowiedzi.} other {{account} podbija {postsCount} twoje wpisy.}}} other {{postType, select, reply {{account} podbija {postsCount} twoich odpowiedzi.} other {{account} podbija {postsCount} twoich wpisów.}}}}} few {{postsCount, plural, =1 {{postType, select, reply {<0><1>{0}</1> osoby</0> podbijają twoją odpowiedź.} other {<0><1>{0}</1> osoby</0> podbijają twój wpis.}}} few {{postType, select, reply {<0><1>{0}</1> osoby</0> podbijają {postsCount} twoje odpowiedzi.} other {<0><1>{0}</1> osoby</0> podbijają {postsCount} twoje wpisy.}}} other {{postType, select, reply {<0><1>{0}</1> osoby</0> podbijają {postsCount} twoich odpowiedzi.} other {<0><1>{0}</1> osoby</0> podbijają {postsCount} twoich wpisów.}}}}} other {{postsCount, plural, =1 {{postType, select, reply {<0><1>{0}</1> osób</0> podbija twoją odpowiedź.} other {<0><1>{0}</1> osób</0> podbija twój wpis.}}} few {{postType, select, reply {<0><1>{0}</1> osób</0> podbija {postsCount} twoje odpowiedzi.} other {<0><1>{0}</1> osób</0> podbija {postsCount} twoje wpisy.}}} other {{postType, select, reply {<0><1>{0}</1> osób</0> podbija {postsCount} twoich odpowiedzi.} other {<0><1>{0}</1> osób</0> podbija {postsCount} twoich wpisów.}}}}}}"

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, =1 {{account} obserwuje cię.} few {<0><1>{0}</1> osoby</0> obserwują cię.} other {<0><1>{0}</1> osób</0> obserwuje cię.}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} chce cię obserwować."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} lubi twoją odpowiedź.} other {{account} lubi twój wpis.}}} few {{postType, select, reply {{account} lubi {postsCount} twoje odpowiedzi.} other {{account} lubi {postsCount} twoje wpisy.}}} other {{postType, select, reply {{account} lubi {postsCount} twoich odpowiedzi.} other {{account} lubi {postsCount} twoich wpisów.}}}}} few {{postsCount, plural, =1 {{postType, select, reply {<0><1>{0}</1> osoby</0> lubią twoją odpowiedź.} other {<0><1>{0}</1> osoby</0> lubi twój wpis.}}} few {{postType, select, reply {<0><1>{0}</1> osoby</0> lubią {postsCount} twoje odpowiedzi.} other {<0><1>{0}</1> osoby</0> lubią {postsCount} twoje wpisy.}}} other {{postType, select, reply {<0><1>{0}</1> osoby</0> lubią {postsCount} twoich odpowiedzi.} other {<0><1>{0}</1> osoby</0> lubią {postsCount} twoich wpisów.}}}}} other {{postsCount, plural, =1 {{postType, select, reply {<0><1>{0}</1> osób</0> lubi twoją odpowiedź.} other {<0><1>{0}</1> osób</0> lubi twój wpis.}}} few {{postType, select, reply {<0><1>{0}</1> osób</0> lubi {postsCount} twoje odpowiedzi.} other {<0><1>{0}</1> osób</0> lubi {postsCount} twoje wpisy.}}} other {{postType, select, reply {<0><1>{0}</1> osób</0> lubi {postsCount} twoich odpowiedzi.} other {<0><1>{0}</1> osób</0> lubi {postsCount} twoich wpisów.}}}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "Zakończyła się ankieta utworzona przez ciebie lub w której został oddany twój głos."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "Utworzona przez ciebie ankieta została zakończona."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "Zakończyła się ankieta, w której został oddany twój głos."

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "Wpis został wyedytowany już po twojej interakcji z nim."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} podbija i lubi twoją odpowiedź.} other {{account} podbija i lubi twój wpis.}}} few {{postType, select, reply {{account} podbija i lubi twoje {postsCount} odpowiedzi.} other {{account} podbija i lubi twoje {postsCount} wpisy.}}} other {{postType, select, reply {{account} podbija i lubi {postsCount} twoich odpowiedzi.} other {{account} podbija i lubi twoje {postsCount} wpisów.}}}}} few {{postsCount, plural, =1 {{postType, select, reply {<0><1>{0}</1> osoby</0> podbijają i lubią twoją odpowiedź.} other {<0><1>{0}</1> osoby</0> podbijają i lubią twój wpis.}}} few {{postType, select, reply {<0><1>{0}</1> osoby</0> podbijają i lubią twoje {postsCount} odpowiedzi.} other {<0><1>{0}</1> osoby</0> podbijają i lubią twoje {postsCount} wpisy.}}} other {{postType, select, reply {<0><1>{0}</1> osoby</0> podbijają i lubią twoje {postsCount} odpowiedzi.} other {<0><1>{0}</1> osoby</0> podbijają i lubią twoje {postsCount} wpisów.}}}}} other {{postsCount, plural, =1 {{postType, select, reply {<0><1>{0}</1> osób</0> podbija i lubi twoją odpowiedź.} other {<0><1>{0}</1> osób</0> podbija i lubi twój wpis.}}} few {{postType, select, reply {<0><1>{0}</1> osób</0> podbija i lubi twoje {postsCount} odpowiedzi.} other {<0><1>{0}</1> osób</0> podbija i lubi twoje {postsCount} wpisy.}}} other {{postType, select, reply {<0><1>{0}</1> osób</0> podbija i lubi twoje {postsCount} odpowiedzi.} other {<0><1>{0}</1> osób</0> podbija i lubi twoje {postsCount} wpisów.}}}}}}"

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} rejestruje się."

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} zgłasza {targetAccount}"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "Utracono połączenie z <0>{name}</0>."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "Ostrzeżenie"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr "Twój #Wrapstodon {year} jest już dostępny!"

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "Administrator <0>{from}</0> zawiesza konto <1>{targetName}</1>, co oznacza, że nie możesz już otrzymywać aktualności ani wchodzić w interakcje z tą osobą."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "Administrator <0>{from}</0> blokuje konto <1>{targetName}</1>. Obserwujący: {followersCount}, i obserwowani: {followingCount}, których to dotyczy."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "Zablokowano <0>{targetName}</0>. Obserwujący: {followersCount}, i obserwowani: {followingCount}, których usunięto."

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "Twoje konto otrzymało ostrzeżenie."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "Twoje konto zostało wyłączone."

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "Niektóre twoje wpisy zostały oznaczone jako wrażliwe."

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "Niektóre twoje wpisy zostały usunięte."

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "Twoje wpisy będą od teraz oznaczane jako wrażliwe."

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "Twoje konto zostało ograniczone."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "Twoje konto zostało zawieszone."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[Nieznany typ powiadomienia: {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1277
#: src/components/status.jsx:1287
msgid "Boosted/Liked by…"
msgstr "Podbite/polubione przez…"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "Polubione przez…"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "Podbite przez…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "Obserwujący…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "Dowiedz się więcej <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr "Wyświetl #Wrapstodon"

#: src/components/notification.jsx:801
#: src/components/status.jsx:486
msgid "Read more →"
msgstr "Więcej →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "Twój głos został oddany"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr "{optionVotesCount, plural, one {# głos} few { # głosy} other {# głosów}}"

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "Ukryj wyniki"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "Głosuj"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1295
#: src/pages/status.jsx:1318
msgid "Refresh"
msgstr "Odśwież"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "Pokaż wyniki"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> głos} other {<1>{1}</1> głosy}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, one {<0>{0}</0> głosujący} other {<1>{1}</1> głosujących}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "Zakończone <0/>"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "Zakończone"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "Koniec <0/>"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "Koniec"

#: src/components/recent-searches.jsx:27
msgid "Cleared recent searches"
msgstr "Historia ostatnich wyszukiwań została wyczyszczona"

#: src/components/recent-searches.jsx:49
#: src/components/search-form.jsx:328
msgid "Recent searches"
msgstr "Ostatnie wyszukiwania"

#: src/components/recent-searches.jsx:59
msgid "Clear all"
msgstr "Wyczyść wszystkie"

#: src/components/recent-searches.jsx:94
#: src/pages/account-statuses.jsx:334
msgid "Clear"
msgstr "Wyczyść"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0}s"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0}m"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0}g"

#: src/components/report-modal.jsx:30
msgid "Spam"
msgstr "Spam"

#: src/components/report-modal.jsx:31
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "Niebezpieczne odnośniki, fałszywe zaangażowanie lub powtarzające się odpowiedzi"

#: src/components/report-modal.jsx:34
msgid "Illegal"
msgstr "Nielegalne"

#: src/components/report-modal.jsx:35
msgid "Violates the law of your or the server's country"
msgstr "Naruszenie prawa w kraju użytkownika lub serwera instancji"

#: src/components/report-modal.jsx:38
msgid "Server rule violation"
msgstr "Naruszenie reguł serwera"

#: src/components/report-modal.jsx:39
msgid "Breaks specific server rules"
msgstr "Narusza określone reguły serwera"

#: src/components/report-modal.jsx:40
msgid "Violation"
msgstr "Naruszenie"

#: src/components/report-modal.jsx:43
msgid "Other"
msgstr "Inne"

#: src/components/report-modal.jsx:44
msgid "Issue doesn't fit other categories"
msgstr "Problem nie pasuje do innych kategorii"

#: src/components/report-modal.jsx:111
msgid "Report Post"
msgstr "Zgłoś wpis"

#: src/components/report-modal.jsx:111
msgid "Report @{username}"
msgstr "Zgłoś @{username}"

#: src/components/report-modal.jsx:147
msgid "Pending review"
msgstr "Oczekuje na przejrzenie"

#: src/components/report-modal.jsx:189
msgid "Post reported"
msgstr "Wpis zgłoszony"

#: src/components/report-modal.jsx:189
msgid "Profile reported"
msgstr "Profil został zgłoszony"

#: src/components/report-modal.jsx:197
msgid "Unable to report post"
msgstr "Nie można zgłosić wpisu"

#: src/components/report-modal.jsx:198
msgid "Unable to report profile"
msgstr "Nie udało się zgłosić profilu"

#: src/components/report-modal.jsx:206
msgid "What's the issue with this post?"
msgstr "Jaki jest problem z tym wpisem?"

#: src/components/report-modal.jsx:207
msgid "What's the issue with this profile?"
msgstr "Jaki jest problem z tym profilem?"

#: src/components/report-modal.jsx:276
msgid "Additional info"
msgstr "Dodatkowe informacje"

#: src/components/report-modal.jsx:299
msgid "Forward to <0>{domain}</0>"
msgstr "Przekieruj do <0>{domain}</0>"

#: src/components/report-modal.jsx:309
msgid "Send Report"
msgstr "Wyślij zgłoszenie"

#: src/components/report-modal.jsx:318
msgid "Muted {username}"
msgstr "Wyciszony: {username}"

#: src/components/report-modal.jsx:321
msgid "Unable to mute {username}"
msgstr "Nie udało się wyciszyć: {username}"

#: src/components/report-modal.jsx:326
msgid "Send Report <0>+ Mute profile</0>"
msgstr "Wyślij raport <0>+ Wycisz profil</0>"

#: src/components/report-modal.jsx:337
msgid "Blocked {username}"
msgstr "Zablokowano @{username}"

#: src/components/report-modal.jsx:340
msgid "Unable to block {username}"
msgstr "Nie udało się zablokować @{username}"

#: src/components/report-modal.jsx:345
msgid "Send Report <0>+ Block profile</0>"
msgstr "Wyślij raport <0>+ zablokuj profil</0>"

#: src/components/search-form.jsx:18
msgid "Posts with <0>{query}</0>"
msgstr "Wpisy z <0>{query}</0>"

#: src/components/search-form.jsx:26
msgid "Accounts with <0>{query}</0>"
msgstr "Konta z <0>{query}</0>"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:34
msgid "Posts tagged with <0>#{0}</0>"
msgstr "Wpisy oznaczone <0>#{0}</0>"

#: src/components/search-form.jsx:43
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>- konta, hasztagi i wpisy</0>"

#: src/components/search-form.jsx:135
msgid "Look up <0>{query}</0>"
msgstr "Wyszukaj <0>{query}</0>"

#: src/components/search-form.jsx:361
#: src/pages/home.jsx:251
msgid "See all"
msgstr "Pokaż wszystko"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "Strona główna / Obserwowane"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "Publiczne (Lokalne / Federowane)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "Konto"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "Hashtag"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "ID listy"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "Tylko Lokalne"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:203
msgid "Instance"
msgstr "Serwer"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "Opcjonalnie, np. mastodon.social"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "Wyszukiwane hasło"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "Opcjonalnie, z wyjątkiem trybu rozszerzonego"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "np. PixelArt (maksymalnie 5, oddzielone spacją)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "Tylko multimedia"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "Skróty"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "beta"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "Określ skróty, które będą wyświetlane jako:"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "Pływający przycisk"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "Pasek menu lub zakładek"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "Tryb rozszerzony"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "Niedostępne w aktywnym trybie widoku"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "Przesuń w górę"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "Przesuń w dół"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1557
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "Edytuj"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "Dodaj więcej skrótów/kolumn, aby to działało."

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "Nie ma jeszcze żadnych kolumn. Kliknij przycisk Dodaj kolumnę."

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "Nie ma jeszcze żadnych skrótów. Kliknij przycisk Dodaj skrót."

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "Nie wiesz, co dodać?<0/>Spróbuj najpierw dodać <1>Strona główna / Obserwowane i Powiadomienia</1>."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "Maksymalna liczba kolumn {SHORTCUTS_LIMIT}"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "Maksymalna liczba skrótów {SHORTCUTS_LIMIT}"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "Importuj/eksportuj"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "Dodaj kolumnę…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "Dodaj skrót…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "Wybranie konkretnej listy jest opcjonalne. Lista jest wymagana w trybie rozszerzonym, w przeciwnym razie kolumna nie zostanie wyświetlona."

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "Wyszukiwanie jest wymagane w trybie rozszerzonym, w przeciwnym razie kolumna nie zostanie wyświetlona."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "Można wpisać kilka hashtagów oddzielonych spacjami."

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "Edytuj skrót"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "Dodaj skrót"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "Oś czasu"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "Lista"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "Importuj/eksportuj <0>Skróty</0>"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "Importuj"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "Wklej skróty tutaj"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "Pobieranie zapisanych skrótów z serwera…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "Nie udało się pobrać skrótów"

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "Pobierz skróty z serwera"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "* Ten skrót już istnieje"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "Lista z innego konta może nie zadziałać."

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "Nieprawidłowy format ustawień"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "Dodać do skrótów?"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr "Tylko brakujące skróty zostaną dodane."

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "Nie ma nowych skrótów do zaimportowania"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "Skróty zostały zaimportowane. Przekroczono maksymalną liczbę skrótów {SHORTCUTS_LIMIT}, więc reszta nie zostanie zaimportowana."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "Skróty zostały zaimportowane"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "Importuj i dodaj…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "Zastąpić skróty?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "Zaimportować skróty?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "lub zastąp…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "Import…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "Eksport"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "Skrót skopiowany"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "Nie udało się skopiować skrótu"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "Ustawienia skrótu skopiowane"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "Nie udało się skopiować ustawień skrótu"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "Udostępnij"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "Zapisywanie skrótów do serwera instancji…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "Skróty zapisane"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "Nie udało się zapisać skrótów"

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "Synchronizuj do serwera instancji"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0, plural, one {# znak} other {# znaki}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "JSON ze skrótami"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "Import/eksport ustawień z/do serwera instancji (Bardzo eksperymentalne)"

#: src/components/status.jsx:351
msgid "Unable to format math"
msgstr "Nie udało się sformatować wyrażenia matematycznego"

#: src/components/status.jsx:365
msgid "Math expressions found."
msgstr "Znaleziono wyrażenie matematyczne."

#. Action to switch from rendered math back to raw (LaTeX) markup
#: src/components/status.jsx:368
msgid "Show markup"
msgstr "Pokaż znaczniki formatowania"

#. Action to render math expressions from raw (LaTeX) markup
#: src/components/status.jsx:373
msgid "Format math"
msgstr "Formatowanie wyrażeń matematycznych"

#: src/components/status.jsx:772
msgid "<0/> <1>boosted</1>"
msgstr "<0/> <1>podbija</1>"

#: src/components/status.jsx:875
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "Przepraszamy, obecnie używana instancja nie może wchodzić w interakcje z tym wpisem z innej instancji."

#. placeholder {0}: username || acct
#: src/components/status.jsx:1029
msgid "Unliked @{0}'s post"
msgstr "Cofnięto polubienie wpisu @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1030
msgid "Liked @{0}'s post"
msgstr "Polubiono wpis @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1069
msgid "Unbookmarked @{0}'s post"
msgstr "Usunięto z zakładek wpis od @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1070
msgid "Bookmarked @{0}'s post"
msgstr "Zapisano wpis @{0}"

#: src/components/status.jsx:1169
msgid "Some media have no descriptions."
msgstr "Niektóre media nie mają opisu."

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1176
msgid "Old post (<0>{0}</0>)"
msgstr "Stary wpis (<0>{0}</0>)"

#: src/components/status.jsx:1200
#: src/components/status.jsx:1240
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
msgid "Unboost"
msgstr "Cofnij podbicie"

#: src/components/status.jsx:1216
#: src/components/status.jsx:2715
msgid "Quote"
msgstr "Cytuj"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1228
#: src/components/status.jsx:1694
msgid "Unboosted @{0}'s post"
msgstr "Cofnięto podbicie wpisu od @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1229
#: src/components/status.jsx:1695
msgid "Boosted @{0}'s post"
msgstr "Podbito wpis od @{0}"

#: src/components/status.jsx:1241
msgid "Boost…"
msgstr "Podbij…"

#: src/components/status.jsx:1253
#: src/components/status.jsx:1988
#: src/components/status.jsx:2736
msgid "Unlike"
msgstr "Cofnij polubienie"

#: src/components/status.jsx:1254
#: src/components/status.jsx:1988
#: src/components/status.jsx:1989
#: src/components/status.jsx:2736
#: src/components/status.jsx:2737
msgid "Like"
msgstr "Polub"

#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
msgid "Unbookmark"
msgstr "Usuń z zakładek"

#: src/components/status.jsx:1346
msgid "Post text copied"
msgstr "Tekst wpisu został skopiowany"

#: src/components/status.jsx:1349
msgid "Unable to copy post text"
msgstr "Nie udało się skopiować tekstu wpisu"

#: src/components/status.jsx:1355
msgid "Copy post text"
msgstr "Skopiuj tekst wpisu"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1373
msgid "View post by <0>@{0}</0>"
msgstr "Otwórz wpis <0>@{0}</0>"

#: src/components/status.jsx:1394
msgid "Show Edit History"
msgstr "Pokaż historię edytowania"

#: src/components/status.jsx:1397
msgid "Edited: {editedDateText}"
msgstr "Edytowano: {editedDateText}"

#: src/components/status.jsx:1464
#: src/components/status.jsx:3516
msgid "Embed post"
msgstr "Zagnieźdź wpis"

#: src/components/status.jsx:1478
msgid "Conversation unmuted"
msgstr "Anulowano wyciszenie konwersacji"

#: src/components/status.jsx:1478
msgid "Conversation muted"
msgstr "Konwersacja wyciszona"

#: src/components/status.jsx:1484
msgid "Unable to unmute conversation"
msgstr "Nie udało się anulować wyciszenia konwersacji"

#: src/components/status.jsx:1485
msgid "Unable to mute conversation"
msgstr "Nie udało się wyciszyć konwersacji"

#: src/components/status.jsx:1494
msgid "Unmute conversation"
msgstr "Anuluj wyciszenie konwersacji"

#: src/components/status.jsx:1501
msgid "Mute conversation"
msgstr "Wycisz konwersację"

#: src/components/status.jsx:1517
msgid "Post unpinned from profile"
msgstr "Wpis został odpięty z profilu"

#: src/components/status.jsx:1518
msgid "Post pinned to profile"
msgstr "Wpis przypięty do profilu"

#: src/components/status.jsx:1523
msgid "Unable to unpin post"
msgstr "Nie udało się odpiąć wpisu"

#: src/components/status.jsx:1523
msgid "Unable to pin post"
msgstr "Nie udało się przypiąć wpisu"

#: src/components/status.jsx:1532
msgid "Unpin from profile"
msgstr "Odepnij z profilu"

#: src/components/status.jsx:1539
msgid "Pin to profile"
msgstr "Przypnij do profilu"

#: src/components/status.jsx:1568
msgid "Delete this post?"
msgstr "Skasować ten wpis?"

#: src/components/status.jsx:1584
msgid "Post deleted"
msgstr "Wpis został skasowany"

#: src/components/status.jsx:1587
msgid "Unable to delete post"
msgstr "Nie udało się usunąć wpisu"

#: src/components/status.jsx:1615
msgid "Report post…"
msgstr "Zgłoś wpis…"

#: src/components/status.jsx:1989
#: src/components/status.jsx:2025
#: src/components/status.jsx:2737
msgid "Liked"
msgstr "Polubiono"

#: src/components/status.jsx:2022
#: src/components/status.jsx:2724
msgid "Boosted"
msgstr "Podbito"

#: src/components/status.jsx:2032
#: src/components/status.jsx:2749
msgid "Bookmarked"
msgstr "Zapisano"

#: src/components/status.jsx:2036
msgid "Pinned"
msgstr "Przypięto"

#: src/components/status.jsx:2082
#: src/components/status.jsx:2561
msgid "Deleted"
msgstr "Usunięto"

#: src/components/status.jsx:2123
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {# odpowiedź} few {# odpowiedzi} other {# odpowiedzi}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2213
msgid "Thread{0}"
msgstr "Wątek{0}"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
#: src/components/status.jsx:2457
msgid "Show less"
msgstr "Pokaż mniej"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
msgid "Show content"
msgstr "Pokaż zawartość"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2453
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Odfiltrowano: {0}"

#: src/components/status.jsx:2457
msgid "Show media"
msgstr "Pokaż multimedia"

#: src/components/status.jsx:2597
msgid "Edited"
msgstr "Edytowano"

#: src/components/status.jsx:2674
msgid "Comments"
msgstr "Odpowiedzi"

#. More from [Author]
#: src/components/status.jsx:2974
msgid "More from <0/>"
msgstr "Więcej od <0/>"

#: src/components/status.jsx:3276
msgid "Edit History"
msgstr "Historia zmian"

#: src/components/status.jsx:3280
msgid "Failed to load history"
msgstr "Nie udało się wczytać historii"

#: src/components/status.jsx:3285
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "Wczytywanie…"

#: src/components/status.jsx:3521
msgid "HTML Code"
msgstr "Kod HTML"

#: src/components/status.jsx:3538
msgid "HTML code copied"
msgstr "Kod HTML został skopiowany"

#: src/components/status.jsx:3541
msgid "Unable to copy HTML code"
msgstr "Nie udało się skopiować kodu HTML"

#: src/components/status.jsx:3553
msgid "Media attachments:"
msgstr "Załączniki:"

#: src/components/status.jsx:3575
msgid "Account Emojis:"
msgstr "Emoji:"

#: src/components/status.jsx:3606
#: src/components/status.jsx:3651
msgid "static URL"
msgstr "statyczny adres URL"

#: src/components/status.jsx:3620
msgid "Emojis:"
msgstr "Emoji:"

#: src/components/status.jsx:3665
msgid "Notes:"
msgstr "Notatki:"

#: src/components/status.jsx:3669
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "Jest to strona statyczna, bez stylów i skryptów. Konieczne może być zastosowanie własnych stylów i edytowanie w razie potrzeby."

#: src/components/status.jsx:3675
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "Ankiety nie są interaktywne, stają się listą z liczbą głosów."

#: src/components/status.jsx:3680
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "Załącznikami mogą być zdjęcia, filmy, pliki audio lub dowolne inne typy plików."

#: src/components/status.jsx:3686
msgid "Post could be edited or deleted later."
msgstr "Wpis może być edytowany lub usunięty później."

#: src/components/status.jsx:3692
msgid "Preview"
msgstr "Podgląd"

#: src/components/status.jsx:3701
msgid "Note: This preview is lightly styled."
msgstr "Uwaga: podgląd jest lekko stylizowany."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3954
msgid "<0/> <1/> boosted"
msgstr "<0/> <1/> podbija"

#: src/components/status.jsx:4056
msgid "Post hidden by your filters"
msgstr "Post ukryty przez Twoje filtry"

#: src/components/status.jsx:4057
msgid "Post pending"
msgstr "Wpis oczekuje na publikację"

#: src/components/status.jsx:4058
#: src/components/status.jsx:4059
#: src/components/status.jsx:4060
#: src/components/status.jsx:4061
msgid "Post unavailable"
msgstr "Wpis jest niedostępny"

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "Nowe wpisy"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1078
#: src/pages/status.jsx:1455
msgid "Try again"
msgstr "Spróbuj ponownie"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, one {# podbicie} few {# podbicia} other{# podbić}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr "Przypięte wpisy"

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "Wątek"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>Odfiltrowane</0>: <1>{0}</1>"

#: src/components/translation-block.jsx:196
msgid "Auto-translated from {sourceLangText}"
msgstr "Automatycznie przetłumaczone z {sourceLangText}"

#: src/components/translation-block.jsx:234
msgid "Translating…"
msgstr "Tłumaczenie…"

#: src/components/translation-block.jsx:237
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "Przetłumacz z {sourceLangText} (wykryto automatycznie)"

#: src/components/translation-block.jsx:238
msgid "Translate from {sourceLangText}"
msgstr "Przetłumacz z {sourceLangText}"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:266
msgid "Auto ({0})"
msgstr "Automatycznie ({0})"

#: src/components/translation-block.jsx:279
msgid "Failed to translate"
msgstr "Nie udało się przetłumaczyć"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "Edytowanie źródłowego statusu"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "Odpowiedź do @{0}"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "Możesz już zamknąć tę stronę."

#: src/compose.jsx:71
msgid "Close window"
msgstr "Zamknij okno"

#: src/compose.jsx:87
msgid "Login required."
msgstr "Wymagane logowanie."

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:280
msgid "Go home"
msgstr "Idź na stronę główną"

#: src/pages/account-statuses.jsx:241
msgid "Account posts"
msgstr "Wpisy konta"

#: src/pages/account-statuses.jsx:248
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ Odpowiedzi)"

#: src/pages/account-statuses.jsx:250
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- Podbicia)"

#: src/pages/account-statuses.jsx:252
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:254
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (Media)"

#: src/pages/account-statuses.jsx:260
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:331
msgid "Clear filters"
msgstr "Wyczyść filtry"

#: src/pages/account-statuses.jsx:348
msgid "Showing post with replies"
msgstr "Wpisy z odpowiedziami"

#: src/pages/account-statuses.jsx:353
msgid "+ Replies"
msgstr "+ Odpowiedzi"

#: src/pages/account-statuses.jsx:359
msgid "Showing posts without boosts"
msgstr "Wpisy z wyłączeniem podbić"

#: src/pages/account-statuses.jsx:364
msgid "- Boosts"
msgstr "- Podbicia"

#: src/pages/account-statuses.jsx:370
msgid "Showing posts with media"
msgstr "Wpisy z mediami"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:387
msgid "Showing posts tagged with #{0}"
msgstr "Wyświetlanie wpisów oznaczonych #{0}"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:426
msgid "Showing posts in {0}"
msgstr "Wyświetlanie wpisów w {0}"

#: src/pages/account-statuses.jsx:516
msgid "Nothing to see here yet."
msgstr "Nie ma tu nic do widzenia!"

#: src/pages/account-statuses.jsx:517
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "Nie można załadować wpisów"

#: src/pages/account-statuses.jsx:558
#: src/pages/account-statuses.jsx:588
msgid "Unable to fetch account info"
msgstr "Nie udało się pobrać informacji o koncie"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:565
msgid "Switch to account's instance {0}"
msgstr "Przełącz na instancję danego konta {0}"

#: src/pages/account-statuses.jsx:595
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "Przełącz na moją instancję (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:668
msgid "Month"
msgstr "Miesiąc"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "Aktualny"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "Domyślny"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "Przełącz na to konto"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "Przełącz na nowej zakładce/oknie"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "Zobacz profil…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "Ustaw jako domyślne"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr "Wylogować <0>@{0}</0>?"

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "Wyloguj…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr "Data połączenia: {0} (<0/>)"

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "Dodaj istniejące konto"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "Uwaga: <0>Domyślne</0> konto będzie zawsze używane przy pierwszym załadowaniu. Przełączone konta będą utrzymywane podczas sesji."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "Nie ma jeszcze zakładek. Idź na coś zapisać!"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "Nie udało się załadować zakładek."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "1 godzinę temu"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "2 godziny temu"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "3 godziny temu"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "4 godziny temu"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "5 godzin temu"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "6 godzin temu"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "7 godzin temu"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "8 godzin temu"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "9 godzin temu"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "10 godzin temu"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "11 godzin temu"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "12 godzin temu"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "ponad 12 godzin temu"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "Obserwowane tagi"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "Grupy"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "Wyświetlanie {selectedFilterCategory, select, all {wszystkich wpisów} original {oryginalnych wpisów} replies {odpowiedzi} boosts {podbić} followedTags {obserwowanych hashtagów} groups {grup} filtered {odfiltrowanych wpisów}}, {sortBy, select, createdAt {{sortOrder, select, asc {najstarsze} desc {najnowsze}}} reblogsCount {{sortOrder, select, asc {najmniej podbić} desc {najwięcej podbić}}} favouritesCount {{sortOrder, select, asc {najmniej polubień} desc {najwięcej polubień}}} repliesCount {{sortOrder, select, asc {najmniej odpowiedzi} desc {najwięcej odpowiedzi}}} density {{sortOrder, select, asc {najkrótsze} desc {najdłuższe}}}} najpierw{groupBy, select, account {, pogrupowane według autorstwa} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "Nadrabianie zaległości <0>beta</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "Pomoc"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "Na czym to polega?"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "Nadrabianie zaległości to osobna oś czasu, zapewniająca przejrzysty podgląd obserwowanych treści, z prostym, inspirowanym pocztą e-mail interfejsem do łatwego sortowania i filtrowania wpisów."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Podgląd interfejsu nadrabiania zaległości"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "Nadrób zaległości"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "Nadrób zaległości z wpisów osób, które obserwujesz."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "Pokaż mi wszystkie wpisy opublikowane nie wcześniej niż…"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "na maksa"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "Nadrób zaległości"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "Pokrywa się z ostatnim nadrabianiem zaległości"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "Do ostatniego nadrabiania zaległości ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "Uwaga: twój serwer może wyświetlić maksymalnie około 800 wpisów na głównej osi czasu niezależnie od zakresu czasu."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "Poprzednio…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, one {# wpis} few {# wpisy} other {# wpisów}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "Usunąć to nadrabianie zaległości?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr "Usuwanie nadrabiania zaległości {0}"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr "Usunięto nadrabianie zaległości {0}"

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "Uwaga: tylko maksymalnie 3 zostaną zapisane. Pozostałe będą usunięte automatycznie."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "Wczytywanie wpisów…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "To może zająć chwilę."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "Resetuj filtry"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "Najpopularniejsze"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "Od {0}"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:330
msgid "All"
msgstr "Wszystkie"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, one {# autor} few {# autorów} other {# autorów}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "Sortuj"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Data"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Długość"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr "Filtry"

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "Autorzy"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "Brak"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "Pokaż wszystkich autorów"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "Nie musisz czytać wszystkiego."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "To wszystko."

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "Wróć na górę"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "Linki udostępniane przez obserwowanych, posortowane według liczby udostępnień, podbić i polubień."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "Sortuj: Długość"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "Wpisy są sortowane według wagi informacji. Krótsze wpisy są \"lżejsze\", a dłuższe \"cięższe\". Wpisy ze zdjęciami są \"cięższe\" niż wpisy bez zdjęć."

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "Grupuj: Autorzy"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "Wpisy są pogrupowane według autorstwa, posortowane według liczby wpisów na autora."

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "Następny autor"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "Poprzedni autor"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "Przewiń na górę"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "Nie ma jeszcze polubień. Polub coś!"

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "Nie udało się wczytać polubień."

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "Strona główna i listy"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "Publiczna oś czasu"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "Dyskusje"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "Profil"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "Nigdy"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "Nowy filtr"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0, plural, one {# obserwujący} few {# obserwujących} many {# obserwujących} other {# obserwujących}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "Nie udało się załadować filtrów"

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr " Nie ma jeszcze filtrów."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "Dodaj filtr"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "Edytuj filtr"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "Nie udało się edytować filtr"

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "Nie udało się utworzyć filtru"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "Tytuł"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "Wszystkie słowa"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "Brak słów kluczowych. Dodaj jakieś."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "Dodaj słowa kluczowe"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, one {# słowo kluczowe} few {# słowa kluczowe} many {# słowa kluczowe} other {# słowa kluczowe}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "Filtruj od…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* Jeszcze niezaimplementowane"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "Status: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "Zmień termin wygaśnięcia"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "Wygaśnięcie"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "Przefiltrowany wpis będzie…"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr "ukryte (tylko media)"

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "zminimalizowane"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "ukryte"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "Skasować filtr?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "Nie udało się skasować filtru."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "Wygasły"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "Wygasa <0/>"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "Nigdy nie wygasa"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural, one {# hashtag} few {# hashtagi} other {# hashtagów}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "Nie udało się załadować obserwowanych hashtagów."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "Nie ma jeszcze obserwowanych hashtagów."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "Nie ma tu nic do widzenia!"

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "Nie udało się załadować wpisu."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{hashtagTitle} (tylko media) na {instance}"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{hashtagTitle} na {instance}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (tylko media)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "Nikt nie napisał jeszcze nic z tym tagiem."

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "Nie udało się załadować wpisów z tym tagiem"

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "Przestać obserwować #{hashtag}?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "Przestano obserwować #{hashtag}"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "Zaobserwowano #{hashtag}"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "Obserwowane…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "Cofnięto wyróżnienie na profilu"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "Nie udało się cofnąć wyróżnienia na profilu"

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "Wyróżnione na profilu"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr "{TOTAL_TAGS_LIMIT, plural, few {maks # tagi} other {maks # tagów}}"

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "Dodaj hashtag"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "Usuń hashtag"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr "Osiągnięto limit {SHORTCUTS_LIMIT, plural, one {# taga.} few {# tagów.} other {# tagów.}} Nie można dodać więcej."

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "Ten skrót już istnieje"

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr "Dodano skrót taga"

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "Dodaj do skrótów"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "Wprowadź nowy serwer, np. \"mastodon.social\""

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "Nieprawidłowy serwer"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "Przejdź do innego serwera…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "Przejdź do mojego serwera (<0>{currentInstance}</0>)"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "Nie można wczytać powiadomień."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<0>Nowe</0> <1>Prośby o obserwację</1>"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "Wczytywanie…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "Nie można wczytać adresu URL"

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "Nie ma tu jeszcze nic."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "Zarządzaj użytkownikami"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "Usunąć <0>@{0}</0> z listy?"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "Usuń…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, one {# lista} few {# listy} other {# list}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "Brak list."

#: src/pages/login.jsx:122
#: src/pages/login.jsx:132
msgid "Failed to register application"
msgstr "Nie udało się zarejestrować aplikacji"

#: src/pages/login.jsx:218
msgid "instance domain"
msgstr "domena serwera"

#: src/pages/login.jsx:242
msgid "e.g. “mastodon.social”"
msgstr "np. “mastodon.social”"

#: src/pages/login.jsx:253
msgid "Failed to log in. Please try again or try another instance."
msgstr "Nie udało się zalogować. Spróbuj ponownie lub spróbuj inny serwer."

#: src/pages/login.jsx:265
msgid "Continue with {selectedInstanceText}"
msgstr "Kontynuuj z {selectedInstanceText}"

#: src/pages/login.jsx:266
msgid "Continue"
msgstr "Kontynuuj"

#: src/pages/login.jsx:274
msgid "Don't have an account? Create one!"
msgstr "Nie masz konta? Utwórz je!"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "Prywatne wzmianki"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "Prywatne"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "Brak wzmianek o tobie :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "Nie udało się wczytać wzmianek."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "których nie obserwujesz"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "które nie obserwują ciebie"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "z nowym kontem"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr "które wysłały do ciebie niechcianą prywatną wzmiankę"

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr "które mają ograniczenia od moderatorów serwera"

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "Ustawienia powiadomień"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "Nowe powiadomienia"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, one {Ogłoszenie} few {Ogłoszenia} other {Ogłoszeń}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "Prośby o obserwację"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, one {# prośba} few {# prośby} other {# próśb}} o obserwację"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr "Odfiltrowane powiadomienia od {0, plural, one {# osoby} other {# osób}}"

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "Tylko wzmianki"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "Dziś"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "Jesteś na bieżąco."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "Wczoraj"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "Nie udało się wczytać powiadomień"

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "Ustawienia powiadomień zostały zaktualizowane"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "Filtruj powiadomienia od osób:"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "Filtr"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "Ignoruj"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "Zaktualizowano <0>{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr "Zobacz powiadomienia od <0>@{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr "Powiadomienia od <0>@{0}</0>"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr "Powiadomienia od @{0} nie będą teraz filtrowane."

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr "Nie udało się zaakceptować prośby o powiadomienia"

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "Zezwól"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr "Powiadomienia od @{0} nie będą teraz wyświetlane w przefiltrowanych powiadomieniach."

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr "Nie udało się odrzucić prośby o powiadomienia"

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "Anuluj"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "Anulowane"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "Lokalna oś czasu ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "Globalna (sfederowana) oś czasu ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "Lokalna oś czasu"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "Globalna (sfederowana) oś czasu"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "Nikt tu jeszcze niczego nie opublikował."

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "Przełącz na Globalną (sfederowaną)"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "Przełącz na Lokalną"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr "Brak zaplanowanych wpisów."

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr "Zaplanowano <0><1/></0> <2>({0})</2>"

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr "Zaplanowano <0><1/></0>"

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr "Przełożono zaplanowany wpis"

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr "Nie udało się zmienić harmonogramu wpisu"

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr "Zaplanuj ponownie"

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr "Skasować zaplanowany wpis?"

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr "Zaplanowany wpis został skasowany"

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr "Nie udało się skasować zaplanowanego wpisu"

#: src/pages/search.jsx:51
msgid "Search: {q} (Posts)"
msgstr "Wyszukaj: {q} (wpisy)"

#: src/pages/search.jsx:54
msgid "Search: {q} (Accounts)"
msgstr "Wyszukaj: {q} (konta)"

#: src/pages/search.jsx:57
msgid "Search: {q} (Hashtags)"
msgstr "Wyszukaj: {q} (hashtagi)"

#: src/pages/search.jsx:60
msgid "Search: {q}"
msgstr "Wyszukaj: {q}"

#: src/pages/search.jsx:340
#: src/pages/search.jsx:422
msgid "Hashtags"
msgstr "Hashtagi"

#: src/pages/search.jsx:372
#: src/pages/search.jsx:426
#: src/pages/search.jsx:496
msgid "See more"
msgstr "Zobacz więcej"

#: src/pages/search.jsx:398
msgid "See more accounts"
msgstr "Zobacz więcej kont"

#: src/pages/search.jsx:412
msgid "No accounts found."
msgstr "Nie znaleziono żadnych kont."

#: src/pages/search.jsx:468
msgid "See more hashtags"
msgstr "Zobacz więcej hashtagów"

#: src/pages/search.jsx:482
msgid "No hashtags found."
msgstr "Nie znaleziono hashtagów"

#: src/pages/search.jsx:526
msgid "See more posts"
msgstr "Zobacz więcej wpisów"

#: src/pages/search.jsx:540
msgid "No posts found."
msgstr "Nie znaleziono wpisów."

#: src/pages/search.jsx:585
msgid "Enter your search term or paste a URL above to get started."
msgstr "Wprowadź wyszukiwane hasło lub wklej adres URL powyżej, aby rozpocząć."

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "Ustawienia"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "Wygląd"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "Jasny"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "Ciemny"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "Automatyczny"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "Rozmiar tekstu"

#. Preview of one character, in largest size
#. Preview of one character, in smallest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "A"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "Język interfejsu"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "Pomóż w tłumaczeniu"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "Publikowanie wpisów"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "Domyślna widoczność wpisów"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "Zsynchronizowano"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "Nie udało się zaktualizować ustawień prywatności wpisów"

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "Synchronizowano z ustawieniami serwera twojej instancji. <0>Przejdź do swojej instancji ({instance}), aby uzyskać więcej ustawień.</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "Eksperymenty"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "Automatycznie odświeżaj wpisy na osi czasu"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "Karuzela podbić"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "Tłumaczenie wpisu"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr "Przetłumacz na "

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "Język systemu ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr "{0, plural, =0 {Ukryj przycisk tłumaczenia dla:} other {Ukryj przycisk tłumaczenia dla (#):}}"

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr "Uwaga: Ta funkcja używa zewnętrznych tłumaczeń, wykorzystując <0>{TRANSLATION_API_NAME}</0>."

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "Automatyczne tłumaczenie"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr "Pokazuj automatycznie przetłumaczone wpisy na osi czasu. Działa tylko w przypadku <0>krótkich</0> wpisów bez ostrzeżeń, multimediów i ankiet."

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "Selektor GIFów"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "Uwaga: ta funkcja korzysta z zewnętrznej usługi wyszukiwania GIFów, obsługiwanej przez <0>GIPHY</0>. Klasyfikacja G (odpowiednie dla wszystkich grup wiekowych), parametry śledzące są usuwane, informacje o źródle są pomijane w zapytaniach, ale wyszukiwania i informacje o adresie IP nadal będą trafiać na ich serwery."

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "Generator opisu zdjęć"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr "Tylko dla nowych zdjęć podczas tworzenia nowych wpisów."

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "Uwaga: ta funkcja korzysta z zewnętrznej usługi AI, obsługiwanej przez <0>img-alt-api</0>. Może nie działać prawidłowo. Tylko dla zdjęć i tylko po angielsku."

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr "Powiadomienia pogrupowane po stronie serwera"

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr "Funkcja w fazie alfa. Potencjalnie ulepszone okno grupowania, ale podstawowa logika grupowania."

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "Import/eksport ustawień skrótów z \"chmury\""

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr "⚠️⚠️⚠️ Bardzo eksperymentalne.<0/>Przechowywane w notatkach Twojego własnego profilu. Notatki profilu (prywatne) są używane głównie dla innych profili i ukryte dla własnego profilu."

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "Uwaga: ta funkcja korzysta z API aktywnego serwera."

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr "Tryb maskowania <0>(<1>Tekst</1> → <2>████</2>)</0>"

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr "Zastępuje tekst blokami, co jest przydatne podczas robienia zrzutów ekranu w celu zachowania prywatności."

#: src/pages/settings.jsx:710
msgid "About"
msgstr "Informacje"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr "<0>Stworzone</0> przez <1>@cheeaun</1>"

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "Sponsoruj"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Wesprzyj"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr "Co nowego?"

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "Polityka prywatności"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>Strona:</0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>Wersja:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "Numer wersji został skopiowany"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr "Nie udało się skopiować numeru wersji"

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr "Nie udało się zaktualizować subskrypcji. Spróbuj ponownie."

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr "Nie udało się usunąć subskrypcji. Spróbuj ponownie."

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "Powiadomienia push (beta)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr "Powiadomienia push są blokowane. Włącz je w ustawieniach przeglądarki."

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "Zezwalaj od <0>{0}</0>"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "każdego"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "obserwowanych"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "obserwujących"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "Obserwowani"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "Ankiety"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "Edycje wpisu"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr "Zgoda na powiadomienia push nie została udzielona od ostatniego logowania. Musisz <0><1>zalogować się</1> ponownie, aby zezwolić na powiadomienia push</0>."

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "UWAGA: powiadomienia push działają tylko dla <0>jednego konta</0>."

#. js-lingui-explicit-id
#: src/pages/status.jsx:658
#: src/pages/status.jsx:1221
msgid "post.title"
msgstr "Wpis"

#: src/pages/status.jsx:912
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "Nie zalogowano. Interakcje (odpowiedzi, podbicia, itp.) nie są możliwe."

#: src/pages/status.jsx:932
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "Ten wpis jest z innego serwera (<0>{instance}</0>). Interakcje (odpowiedzi, podbicia, itp.) nie są możliwe."

#: src/pages/status.jsx:960
msgid "Error: {e}"
msgstr "Błąd: {e}"

#: src/pages/status.jsx:967
msgid "Switch to my instance to enable interactions"
msgstr "Przełącz na mój serwer, aby włączyć interakcje"

#: src/pages/status.jsx:1069
msgid "Unable to load replies."
msgstr "Nie udało się wczytać odpowiedzi."

#: src/pages/status.jsx:1181
msgid "Back"
msgstr "Wróć"

#: src/pages/status.jsx:1212
msgid "Go to main post"
msgstr "Przejdź do głównego wpisu"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1235
msgid "{0} posts above ‒ Go to top"
msgstr "{0} wpisów powyżej - Przejdź do góry"

#: src/pages/status.jsx:1282
#: src/pages/status.jsx:1345
msgid "Switch to Side Peek view"
msgstr "Przełącz na widok Podglądu Bocznego"

#: src/pages/status.jsx:1346
msgid "Switch to Full view"
msgstr "Przełącz na widok pełnoekranowy"

#: src/pages/status.jsx:1364
msgid "Show all sensitive content"
msgstr "Pokaż wszystkie wrażliwe treści"

#: src/pages/status.jsx:1369
msgid "Experimental"
msgstr "Eksperymentalne"

#: src/pages/status.jsx:1378
msgid "Unable to switch"
msgstr "Nie udało się przełączyć"

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1385
msgid "Switch to post's instance ({0})"
msgstr "Przełącz na instancję wpisu ({0})"

#: src/pages/status.jsx:1388
msgid "Switch to post's instance"
msgstr "Przełącz na instancję wpisu"

#: src/pages/status.jsx:1446
msgid "Unable to load post"
msgstr "Nie udało się załadować wpisu"

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1582
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, one {# odpowiedź} other {<0>{1}</0> odpowiedzi}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1600
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, one {# odpowiedź} other {<0>{0}</0> odpowiedzi}}"

#: src/pages/status.jsx:1622
msgid "View post with its replies"
msgstr "Zobacz wpis z odpowiedziami"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "Najpopularniejsze na ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "Popularne wiadomości"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr "Od {0}"

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "Wróć do popularnych wpisów"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "Wyświetlanie wpisów wspominających <0>{0}</0>"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "Popularne wpisy"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "Brak popularnych wpisów."

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "Minimalistyczny, oryginalny klient webowy dla Mastodon."

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Zaloguj się z Mastodon"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "Zarejestruj się"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "Połącz swoje istniejące konto Mastodon/Fediwersum.<0/>Twoje dane uwierzytelniające nie są przechowywane na tym serwerze."

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr "<0>Stworzono</0> przez <1>@cheeaun</1>. <2>Polityka prywatności</2>."

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "Zrzut ekranu karuzeli podbić"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "Karuzela podbić"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "Oddziel wizualnie oryginalne wpisy i podbicia."

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr "Zrzut ekranu zagnieżdżonych odpowiedzi"

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr "Zagnieżdżone odpowiedzi"

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "Bezproblemowo śledź rozmowy. Częściowo ukrywalne odpowiedzi."

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr "Zrzut ekranu zgrupowanych powiadomień"

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "Grupowanie powiadomień"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr "Podobne powiadomienia są pogrupowane i zwijane, aby zmniejszyć harmider."

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "Zrzut ekranu wielokolumnowego interfejsu użytkownika"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "Jedna lub wiele kolumn"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "Domyślne ustawienie to pojedyncza kolumna dla poszukiwaczy zen. Możliwość konfiguracji wielu kolumn dla zaawansowanych użytkowników."

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "Zrzut ekranu wielohasztagagowej osi czasu z polem dodawania więcej hashtagów"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "Oś czasu wielu hashtagów"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "Do 5 hashtagów połączonych w pojedynczą oś czasu."

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "Wygląda na to, że przeglądarka blokuje wyskakujące okienka."

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr "Wersja robocza wpisu jest obecnie zminimalizowana. Opublikuj lub odrzuć go przed utworzeniem nowego."

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr "Wpis jest obecnie otwarty. Opublikuj lub odrzuć go przed utworzeniem nowego."

