msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: ca\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-08-05 04:12\n"
"Last-Translator: \n"
"Language-Team: Catalan\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: ca\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:141
msgid "Locked"
msgstr "Blocada"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:163
msgid "Posts: {0}"
msgstr "Publicacions: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:168
msgid "Last posted: {0}"
msgstr "Última publicació: {0}"

#: src/components/account-block.jsx:183
#: src/components/account-info.jsx:705
msgid "Automated"
msgstr "Automatitzat"

#: src/components/account-block.jsx:190
#: src/components/account-info.jsx:710
#: src/components/status.jsx:748
msgid "Group"
msgstr "Grup"

#: src/components/account-block.jsx:200
msgid "Mutual"
msgstr "Mutu"

#: src/components/account-block.jsx:204
#: src/components/account-info.jsx:1907
msgid "Requested"
msgstr "Sol·licitat per"

#: src/components/account-block.jsx:208
#: src/components/account-info.jsx:1898
msgid "Following"
msgstr "Seguint-ne"

#: src/components/account-block.jsx:212
#: src/components/account-info.jsx:1212
msgid "Follows you"
msgstr "Et segueix"

#: src/components/account-block.jsx:220
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount , plural, one {# seguidor} other {# seguidors}}"

#: src/components/account-block.jsx:229
#: src/components/account-info.jsx:753
msgid "Verified"
msgstr "Verificat"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:244
#: src/components/account-info.jsx:892
msgid "Joined <0>{0}</0>"
msgstr "S'hi va unir el <0>{0}</0>"

#: src/components/account-info.jsx:64
msgid "Forever"
msgstr "Per sempre"

#: src/components/account-info.jsx:398
msgid "Unable to load account."
msgstr "No es pot carregar el compte."

#: src/components/account-info.jsx:413
msgid "Go to account page"
msgstr "Ves a la pàgina del compte"

#: src/components/account-info.jsx:442
#: src/components/account-info.jsx:775
msgid "Followers"
msgstr "Seguidors"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:446
#: src/components/account-info.jsx:830
msgid "following.stats"
msgstr "Seguint-ne"

#: src/components/account-info.jsx:449
#: src/pages/account-statuses.jsx:495
#: src/pages/search.jsx:345
#: src/pages/search.jsx:492
msgid "Posts"
msgstr "Publicacions"

#: src/components/account-info.jsx:457
#: src/components/account-info.jsx:1268
#: src/components/compose.jsx:2819
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1998
#: src/components/status.jsx:2015
#: src/components/status.jsx:2140
#: src/components/status.jsx:2769
#: src/components/status.jsx:2772
#: src/pages/account-statuses.jsx:539
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1306
#: src/pages/trending.jsx:474
msgid "More"
msgstr "Més"

#: src/components/account-info.jsx:469
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> ha indicat que té un nou compte:"

#: src/components/account-info.jsx:614
#: src/components/account-info.jsx:1496
msgid "Handle copied"
msgstr "Identificador copiat"

#: src/components/account-info.jsx:617
#: src/components/account-info.jsx:1499
msgid "Unable to copy handle"
msgstr "No s'ha pogut copiar l'identificador d'usuari"

#: src/components/account-info.jsx:623
#: src/components/account-info.jsx:1505
msgid "Copy handle"
msgstr "Copia l'identificador d'usuari"

#: src/components/account-info.jsx:629
msgid "Go to original profile page"
msgstr "Ves a la pàgina del perfil original"

#: src/components/account-info.jsx:647
msgid "View profile image"
msgstr "Veure imatge de perfil"

#: src/components/account-info.jsx:665
msgid "View profile header"
msgstr "Veure imatge de la capçalera"

#: src/components/account-info.jsx:681
#: src/components/account-info.jsx:1806
#: src/components/account-info.jsx:2332
msgid "Edit profile"
msgstr "Edita el perfil"

#: src/components/account-info.jsx:700
msgid "In Memoriam"
msgstr "En Memòria"

#: src/components/account-info.jsx:782
#: src/components/account-info.jsx:840
msgid "This user has chosen to not make this information available."
msgstr "Aquest usuari ha decidit no mostrar aquesta informació."

#. placeholder {0}: shortenNumber(followersCount)
#. placeholder {1}: shortenNumber(followersCount)
#: src/components/account-info.jsx:802
msgid "{followersCount, plural, one {<0>{0}</0> Follower} other {<1>{1}</1> Followers}}"
msgstr ""

#. placeholder {0}: shortenNumber(followingCount)
#: src/components/account-info.jsx:846
msgid "{followingCount, plural, other {<0>{0}</0> Following}}"
msgstr ""

#. placeholder {0}: shortenNumber(statusesCount)
#. placeholder {1}: shortenNumber(statusesCount)
#: src/components/account-info.jsx:870
msgid "{statusesCount, plural, one {<0>{0}</0> Post} other {<1>{1}</1> Posts}}"
msgstr ""

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:917
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0} publicacions originals, {1} respostes, {2} impulsos"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:933
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural, one {{1, plural, one {Última publicació en el darrer dia} other {Última publicació en els darrers {2} dies}}} other {{3, plural, one {Últimes {4} publicacions en el darrer dia} other {Últimes {5} publicacions en els darrers {6} dies}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:949
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {Última publicació en el darrer any(s)} other {Últimes {1} publicacions en el darrer any(s)}}"

#: src/components/account-info.jsx:974
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "Original"

#: src/components/account-info.jsx:978
#: src/components/status.jsx:2553
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1025
#: src/pages/status.jsx:1652
msgid "Replies"
msgstr "Respostes"

#: src/components/account-info.jsx:982
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "Impulsos"

#: src/components/account-info.jsx:988
msgid "Post stats unavailable."
msgstr "Les estadístiques de les publicacions no estan disponibles."

#: src/components/account-info.jsx:1019
msgid "View post stats"
msgstr "Veure estadístiques de les publicacions"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1216
msgid "Last post: <0>{0}</0>"
msgstr "Última publicació: <0>{0}</0>"

#: src/components/account-info.jsx:1230
msgid "Muted"
msgstr "Usuaris silenciats"

#: src/components/account-info.jsx:1235
msgid "Blocked"
msgstr "Usuaris blocats"

#: src/components/account-info.jsx:1244
msgid "Private note"
msgstr "Nota privada"

#: src/components/account-info.jsx:1301
msgid "Mention <0>@{username}</0>"
msgstr "Menciona a <0>@{username}</0>"

#: src/components/account-info.jsx:1313
msgid "Translate bio"
msgstr "Tradueix la biografia"

#: src/components/account-info.jsx:1324
msgid "Edit private note"
msgstr "Edita una nota privada"

#: src/components/account-info.jsx:1324
msgid "Add private note"
msgstr "Afegiu una nota privada"

#: src/components/account-info.jsx:1344
msgid "Notifications enabled for @{username}'s posts."
msgstr "S'han activat les notificacions per a les publicacions de @{username}."

#: src/components/account-info.jsx:1345
msgid " Notifications disabled for @{username}'s posts."
msgstr " S'han desactivat les notificacions per a les publicacions de @{username}."

#: src/components/account-info.jsx:1357
msgid "Disable notifications"
msgstr "Desactiva les notificacions"

#: src/components/account-info.jsx:1358
msgid "Enable notifications"
msgstr "Activa les notificacions"

#: src/components/account-info.jsx:1375
msgid "Boosts from @{username} enabled."
msgstr "Impulsos de @{username} permesos."

#: src/components/account-info.jsx:1376
msgid "Boosts from @{username} disabled."
msgstr "Impulsos de @{username} inhabilitats."

#: src/components/account-info.jsx:1387
msgid "Disable boosts"
msgstr "Desactiva els impulsos"

#: src/components/account-info.jsx:1387
msgid "Enable boosts"
msgstr "Activa els impulsos"

#: src/components/account-info.jsx:1406
msgid "@{username} is no longer featured on your profile."
msgstr "@{username} ja no està destacat al teu perfil."

#: src/components/account-info.jsx:1416
msgid "@{username} is now featured on your profile."
msgstr "@{username} està destacat al teu perfil."

#: src/components/account-info.jsx:1424
msgid "Unable to unfeature @{username} on your profile."
msgstr "No es pot eliminar @{username} del teu perfil."

#: src/components/account-info.jsx:1428
msgid "Unable to feature @{username} on your profile."
msgstr "No es pot afegir @{username} al teu perfil."

#: src/components/account-info.jsx:1437
msgid "Don't feature on profile"
msgstr "No incloure al perfil"

#: src/components/account-info.jsx:1438
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "Destaca al perfil"

#: src/components/account-info.jsx:1447
msgid "Show featured profiles"
msgstr "Mostra els perfils destacats"

#: src/components/account-info.jsx:1462
#: src/components/account-info.jsx:1472
#: src/components/account-info.jsx:2078
msgid "Add/Remove from Lists"
msgstr "Afegeix/elimina de les llistes"

#: src/components/account-info.jsx:1522
#: src/components/status.jsx:1420
msgid "Link copied"
msgstr "Enllaç copiat"

#: src/components/account-info.jsx:1525
#: src/components/status.jsx:1423
msgid "Unable to copy link"
msgstr "No s'ha pogut copiar l'enllaç"

#: src/components/account-info.jsx:1531
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1429
#: src/components/status.jsx:3547
msgid "Copy"
msgstr "Copia"

#: src/components/account-info.jsx:1546
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1445
msgid "Sharing doesn't seem to work."
msgstr "Sembla que la compartició no funciona."

#: src/components/account-info.jsx:1552
#: src/components/status.jsx:1451
msgid "Share…"
msgstr "Comparteix…"

#: src/components/account-info.jsx:1572
msgid "Unmuted @{username}"
msgstr "@{username} s'ha deixat de silenciar"

#: src/components/account-info.jsx:1584
msgid "Unmute <0>@{username}</0>"
msgstr "Deixa de silenciar a <0>@{username}</0>"

#: src/components/account-info.jsx:1600
msgid "Mute <0>@{username}</0>…"
msgstr "Silencia a <0>@{username}</0>…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1632
msgid "Muted @{username} for {0}"
msgstr "@{username} silenciat durant {0}"

#: src/components/account-info.jsx:1644
msgid "Unable to mute @{username}"
msgstr "No ha estat possible silenciar @{username}"

#: src/components/account-info.jsx:1665
msgid "Remove <0>@{username}</0> from followers?"
msgstr "Voleu suprimir a <0>@{username}</0> dels seguidors?"

#: src/components/account-info.jsx:1685
msgid "@{username} removed from followers"
msgstr "S'ha eliminat @{username} de la llista de seguidors"

#: src/components/account-info.jsx:1697
msgid "Remove follower…"
msgstr "Suprimeix seguidor…"

#: src/components/account-info.jsx:1708
msgid "Block <0>@{username}</0>?"
msgstr "Voleu blocar a <0>@{username}</0>?"

#: src/components/account-info.jsx:1732
msgid "Unblocked @{username}"
msgstr "S'ha desblocat a @{username}"

#: src/components/account-info.jsx:1740
msgid "Blocked @{username}"
msgstr "S'ha blocat a @{username}"

#: src/components/account-info.jsx:1748
msgid "Unable to unblock @{username}"
msgstr "No ha estat possible desblocar @{username}"

#: src/components/account-info.jsx:1750
msgid "Unable to block @{username}"
msgstr "No ha estat possible blocar @{username}"

#: src/components/account-info.jsx:1760
msgid "Unblock <0>@{username}</0>"
msgstr "Deixa de blocar a <0>@{username}</0>"

#: src/components/account-info.jsx:1769
msgid "Block <0>@{username}</0>…"
msgstr "Bloca a <0>@{username}</0>…"

#: src/components/account-info.jsx:1786
msgid "Report <0>@{username}</0>…"
msgstr "Denúncia a <0>@{username}</0>…"

#: src/components/account-info.jsx:1842
msgid "Withdraw follow request?"
msgstr "Voleu retirar la sol·licitud de seguiment?"

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1843
msgid "Unfollow @{0}?"
msgstr "Voleu deixar de seguir @{0}?"

#: src/components/account-info.jsx:1901
msgid "Unfollow…"
msgstr "Deixa de seguir…"

#: src/components/account-info.jsx:1910
msgid "Withdraw…"
msgstr "Descarta…"

#: src/components/account-info.jsx:1917
#: src/components/account-info.jsx:1921
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "Segueix"

#: src/components/account-info.jsx:2018
#: src/components/account-info.jsx:2073
#: src/components/account-info.jsx:2207
#: src/components/account-info.jsx:2327
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:892
#: src/components/compose.jsx:2775
#: src/components/compose.jsx:3255
#: src/components/compose.jsx:3464
#: src/components/compose.jsx:3694
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:118
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3271
#: src/components/status.jsx:3511
#: src/components/status.jsx:4020
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1393
msgid "Close"
msgstr "Tanca"

#: src/components/account-info.jsx:2023
msgid "Translated Bio"
msgstr "Biografia traduïda"

#: src/components/account-info.jsx:2118
msgid "Unable to remove from list."
msgstr "No s'ha pogut eliminar de la llista."

#: src/components/account-info.jsx:2119
msgid "Unable to add to list."
msgstr "No s'ha pogut afegir a la llista."

#: src/components/account-info.jsx:2138
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "No s'ha pogut carregar les llistes."

#: src/components/account-info.jsx:2142
msgid "No lists."
msgstr "No hi ha cap llista."

#: src/components/account-info.jsx:2153
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "Llista nova"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2212
msgid "Private note about <0>@{0}</0>"
msgstr "Nota privada sobre <0>@{0}</0>"

#: src/components/account-info.jsx:2242
msgid "Unable to update private note."
msgstr "No ha estat possible actualitzar la nota privada."

#: src/components/account-info.jsx:2265
#: src/components/account-info.jsx:2563
msgid "Cancel"
msgstr "Cancel·la"

#: src/components/account-info.jsx:2270
msgid "Save & close"
msgstr "Desa i tanca"

#: src/components/account-info.jsx:2387
msgid "Unable to update profile."
msgstr "No ha estat possible actualitzar el perfil."

#: src/components/account-info.jsx:2394
msgid "Header picture"
msgstr "Imatge de capçalera"

#: src/components/account-info.jsx:2446
msgid "Profile picture"
msgstr "Foto de perfil"

#: src/components/account-info.jsx:2498
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "Nom"

#: src/components/account-info.jsx:2511
msgid "Bio"
msgstr "Biografia"

#: src/components/account-info.jsx:2524
msgid "Extra fields"
msgstr "Camps addicionals"

#: src/components/account-info.jsx:2530
msgid "Label"
msgstr "Etiqueta"

#: src/components/account-info.jsx:2533
msgid "Content"
msgstr "Contingut"

#: src/components/account-info.jsx:2566
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "Desa"

#: src/components/account-info.jsx:2620
msgid "username"
msgstr "nom d’usuari"

#: src/components/account-info.jsx:2624
msgid "server domain name"
msgstr "nom de domini del servidor"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2690
msgid "Profiles featured by @{0}"
msgstr "Perfils destacats per @{0}"

#: src/components/account-info.jsx:2716
msgid "No featured profiles."
msgstr "No hi ha perfils destacats."

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "Mode ocult desactivat"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "Mode ocult activat"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "Inici"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "Redacta"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr "Publicacions programades"

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr "Afegeix al fil"

#: src/components/compose.jsx:212
msgid "Take photo or video"
msgstr "Fes una foto o un vídeo"

#: src/components/compose.jsx:213
msgid "Add media"
msgstr "Afegeix un mitjà"

#: src/components/compose.jsx:214
msgid "Add custom emoji"
msgstr "Afegeix emoji personalitzat"

#: src/components/compose.jsx:215
msgid "Add GIF"
msgstr "Afegeix un GIF"

#: src/components/compose.jsx:216
msgid "Add poll"
msgstr "Afegeix una enquesta"

#: src/components/compose.jsx:217
msgid "Schedule post"
msgstr "Programa una publicació"

#: src/components/compose.jsx:417
msgid "You have unsaved changes. Discard this post?"
msgstr "Hi ha canvis sense desar. Voleu descartar la publicació?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:655
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr "{0, plural, one {{1} fitxer no és compatible.} other {{2} fitxers no són compatibles.}}"

#: src/components/compose.jsx:665
#: src/components/compose.jsx:683
#: src/components/compose.jsx:1795
#: src/components/compose.jsx:1930
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, one {Només podeu adjuntar com a màxim 1 fitxer.} other {Només podeu adjuntar un màxim de # fitxers.}}"

#: src/components/compose.jsx:873
msgid "Pop out"
msgstr "Finestra emergent"

#: src/components/compose.jsx:880
msgid "Minimize"
msgstr "Minimitza"

#: src/components/compose.jsx:916
msgid "Looks like you closed the parent window."
msgstr "Sembla que heu tancat la finestra principal."

#: src/components/compose.jsx:923
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Sembla que ja teniu un camp d'edició obert a la finestra principal i que esteu publicant. Espereu que es publiqui i torneu-ho a provar més tard."

#: src/components/compose.jsx:928
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Sembla que ja teniu un camp d'edició obert a la finestra principal. Si l'utilitzeu en aquesta finestra, es descartaran els canvis que heu fet a la finestra principal. Voleu continuar?"

#: src/components/compose.jsx:971
msgid "Pop in"
msgstr "Mostra"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:981
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "Contestant la publicació de @{0} (<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:991
msgid "Replying to @{0}’s post"
msgstr "En resposta a la publicació de @{0}"

#: src/components/compose.jsx:1004
msgid "Editing source post"
msgstr "Editant la publicació original"

#: src/components/compose.jsx:1057
msgid "Poll must have at least 2 options"
msgstr "L'enquesta ha de tenir si més no 2 opcions"

#: src/components/compose.jsx:1061
msgid "Some poll choices are empty"
msgstr "Alguna opció de l'enquesta és buida"

#: src/components/compose.jsx:1074
msgid "Some media have no descriptions. Continue?"
msgstr "Alguns mitjans no tenen descripcions. Voleu continuar?"

#: src/components/compose.jsx:1126
msgid "Attachment #{i} failed"
msgstr "El fitxer adjunt #{i} ha fallat"

#: src/components/compose.jsx:1222
#: src/components/status.jsx:2328
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "Avís de contingut"

#: src/components/compose.jsx:1238
msgid "Content warning or sensitive media"
msgstr "Advertiment de contingut o mitjans sensibles"

#: src/components/compose.jsx:1274
#: src/components/status.jsx:99
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "Públic"

#: src/components/compose.jsx:1279
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:100
msgid "Local"
msgstr "Local"

#: src/components/compose.jsx:1283
#: src/components/status.jsx:101
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "Sense llistar"

#: src/components/compose.jsx:1286
#: src/components/status.jsx:102
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "Només per als seguidors"

#: src/components/compose.jsx:1289
#: src/components/status.jsx:103
#: src/components/status.jsx:2204
msgid "Private mention"
msgstr "Menció privada"

#: src/components/compose.jsx:1298
msgid "Post your reply"
msgstr "Publica la resposta"

#: src/components/compose.jsx:1300
msgid "Edit your post"
msgstr "Edita la publicació"

#: src/components/compose.jsx:1301
msgid "What are you doing?"
msgstr "Què feu ara?"

#: src/components/compose.jsx:1380
msgid "Mark media as sensitive"
msgstr "Marca els mitjans com a sensibles"

#: src/components/compose.jsx:1417
msgid "Posting on <0/>"
msgstr "Data de la publicació: <0/>"

#: src/components/compose.jsx:1448
#: src/components/compose.jsx:3313
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "Afegeix"

#: src/components/compose.jsx:1676
msgid "Schedule"
msgstr "Programa"

#: src/components/compose.jsx:1678
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1192
#: src/components/status.jsx:1978
#: src/components/status.jsx:1979
#: src/components/status.jsx:2673
msgid "Reply"
msgstr "Respon"

#: src/components/compose.jsx:1680
msgid "Update"
msgstr "Actualitza"

#: src/components/compose.jsx:1681
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Publica"

#: src/components/compose.jsx:1807
msgid "Downloading GIF…"
msgstr "S'està baixant el GIF…"

#: src/components/compose.jsx:1835
msgid "Failed to download GIF"
msgstr "Ha fallat la descàrrega del GIF"

#: src/components/compose.jsx:2060
#: src/components/compose.jsx:2153
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "Més…"

#: src/components/compose.jsx:2589
msgid "Uploaded"
msgstr "Pujat"

#: src/components/compose.jsx:2602
msgid "Image description"
msgstr "Descripció de la imatge"

#: src/components/compose.jsx:2603
msgid "Video description"
msgstr "Descripció del vídeo"

#: src/components/compose.jsx:2604
msgid "Audio description"
msgstr "Descripció de l'àudio"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2639
#: src/components/compose.jsx:2659
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "La mida del fitxer és massa gran. La càrrega pot tenir problemes. Proveu de reduir la mida del fitxer de {0} a {1} o inferior."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2651
#: src/components/compose.jsx:2671
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr "La resolució del fitxer és massa gran. La càrrega pot tenir problemes. Proveu de reduir la resolució de {0}×{1}px a {2}×{3}px."

#: src/components/compose.jsx:2679
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "La velocitat de fotogrames és massa alta. La càrrega pot tenir problemes."

#: src/components/compose.jsx:2739
#: src/components/compose.jsx:2989
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "Suprimeix"

#: src/components/compose.jsx:2756
#: src/compose.jsx:84
msgid "Error"
msgstr "Error"

#: src/components/compose.jsx:2781
msgid "Edit image description"
msgstr "Edita la descripció de la imatge"

#: src/components/compose.jsx:2782
msgid "Edit video description"
msgstr "Edita la descripció del vídeo"

#: src/components/compose.jsx:2783
msgid "Edit audio description"
msgstr "Edita la descripció de l'àudio"

#: src/components/compose.jsx:2828
#: src/components/compose.jsx:2877
msgid "Generating description. Please wait…"
msgstr "Generant descripció. Si us plau, espereu…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2848
msgid "Failed to generate description: {0}"
msgstr "No s'ha pogut generar la descripció: {0}"

#: src/components/compose.jsx:2849
msgid "Failed to generate description"
msgstr "No s'ha pogut generar la descripció"

#: src/components/compose.jsx:2861
#: src/components/compose.jsx:2867
#: src/components/compose.jsx:2913
msgid "Generate description…"
msgstr "Genera una descripció…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2900
msgid "Failed to generate description{0}"
msgstr "No s'ha pogut generar la descripció{0}"

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2915
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>— experimental</0>"

#: src/components/compose.jsx:2934
msgid "Done"
msgstr "Fet"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2970
msgid "Choice {0}"
msgstr "Opció {0}"

#: src/components/compose.jsx:3017
msgid "Multiple choices"
msgstr "Opció múltiple"

#: src/components/compose.jsx:3020
msgid "Duration"
msgstr "Durada"

#: src/components/compose.jsx:3051
msgid "Remove poll"
msgstr "Elimina l'enquesta"

#: src/components/compose.jsx:3272
msgid "Search accounts"
msgstr "Cerca comptes"

#: src/components/compose.jsx:3326
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "S'ha produït un error en carregar els comptes"

#: src/components/compose.jsx:3470
msgid "Custom emojis"
msgstr "Emojis personalitzats"

#: src/components/compose.jsx:3490
msgid "Search emoji"
msgstr "Cerca un emoji"

#: src/components/compose.jsx:3521
msgid "Error loading custom emojis"
msgstr "S'ha produït un error en carregar els emojis personalitzats"

#: src/components/compose.jsx:3532
msgid "Recently used"
msgstr "Usats recentment"

#: src/components/compose.jsx:3533
msgid "Others"
msgstr "Altres"

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3571
msgid "{0} more…"
msgstr "{0} més…"

#: src/components/compose.jsx:3709
msgid "Search GIFs"
msgstr "Cerca GIF"

#: src/components/compose.jsx:3724
msgid "Powered by GIPHY"
msgstr "Desenvolupat per GIPHY"

#: src/components/compose.jsx:3732
msgid "Type to search GIFs"
msgstr "Escriviu per cercar un GIF"

#: src/components/compose.jsx:3830
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "Anterior"

#: src/components/compose.jsx:3848
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "Següent"

#: src/components/compose.jsx:3865
msgid "Error loading GIFs"
msgstr "S'ha produït un error en carregar els GIF"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "Esborranys no enviats"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "Sembla que hi ha esborranys sense enviar. Continuarem on ho vau deixar."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "Voleu suprimir aquesta esborrany?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "Error quan desava l'esborrany. Torneu a intentar-ho."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1595
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "Esborra…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "S'ha produït un error en obtenir l'estat de resposta!"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "Voleu suprimir tots els esborranys?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "S'ha produït un error en suprimir els esborranys! Si us plau, torna-ho a provar."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "Elimina-ho tot…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "No s'han trobat esborranys."

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "Enquesta"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:375
msgid "Media"
msgstr "Multimèdia"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "Obre en una finestra nova"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "Accepta"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "Rebutja"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "Acceptat"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "Rebutjat"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:335
#: src/pages/search.jsx:368
msgid "Accounts"
msgstr "Comptes"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:562
#: src/pages/status.jsx:1426
msgid "Show more…"
msgstr "Mostra'n més…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:567
msgid "The end."
msgstr "Final."

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "Res a mostrar"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "Dreceres de teclat"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "Ajuda sobre dreceres de teclat"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "Publicació següent"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "Publicació anterior"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "Omet el carrusel a la següent publicació"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Shift</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "Omet el carrusel a l'anterior publicació"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Shift</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "Carrega publicacions noves"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "Obre els detalls de la publicació"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Enter</0> o bé <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "Amplieu l'avís de contingut o<0/>canvia al fil ampliat/replegat"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "Tanca la publicació o els diàlegs"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc</0> o <1>Retrocés</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "Centra la columna en mode de múltiples columnes"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> a <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr "Centra la columna següent en mode de múltiples columnes"

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr "Centra la columna anterior en mode de múltiples columnes"

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "Redacta una publicació nova"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "Redacta una publicació nova (en una altra finestra)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Shift</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "Envia la publicació"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Ctrl</0> + <1>Enter</1> o <2>⌘</2> + <3>Enter</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:204
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:47
#: src/pages/search.jsx:317
msgid "Search"
msgstr "Cerca"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "Respon (en una altra finestra)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Shift</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "M'agrada (favorit)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> o <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1200
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
#: src/components/status.jsx:2724
msgid "Boost"
msgstr "Impulsa"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Shift</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
#: src/components/status.jsx:2749
msgid "Bookmark"
msgstr "Afegeix als marcadors"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "Alterna el mode ocult"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Shift</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "Edita la llista"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "No s'ha pogut editar la llista."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "No s'ha pogut crear la llista."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "Mostra les respostes dels membres de la llista"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "Mostra les respostes de gent que segueixo"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "No mostris les respostes"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "Amaga les publicacions d'inici/seguint en aquesta llista"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "Crea"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "Voleu suprimir aquesta llista?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "No s'ha pogut esborrar la llista."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr "Les publicacions d'aquesta llista estan ocultes a Inici/Seguint"

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "Descripció dels mitjans"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1306
#: src/components/status.jsx:1315
#: src/components/translation-block.jsx:239
msgid "Translate"
msgstr "Tradueix"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1334
msgid "Speak"
msgstr "Pronuncia"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "Obre el fitxer original en una finestra nova"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "Obre el fitxer original"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "Intentant descriure la imatge. Si us plau, espereu…"

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "No s'ha pogut descriure la imatge"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "Descriu la imatge…"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "Mostra la publicació"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "Contingut sensible"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "Filtrat: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3850
#: src/components/status.jsx:3946
#: src/components/status.jsx:4024
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "Filtrat"

#: src/components/media.jsx:479
msgid "Open file"
msgstr "Obre el fitxer"

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr "Publicació programada"

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "Publicació enviada. Comproveu-la."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr "Resposta programada"

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "Resposta enviada. Comproveu-la."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "Publicació actualitzada. Comproveu-la."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "Menú"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "Voleu tornar a carregar la pàgina per actualitzar-la?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "Nova actualització disponible…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr "Seguint-ne"

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "Posada al dia"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "Mencions"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "Notificacions"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "Nou"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "Perfil"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "Marcadors"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "\"M'agrada\""

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "Etiquetes seguides"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:341
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "Filtres"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "Usuaris silenciats"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "Usuaris silenciats…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "Usuaris blocats"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "Usuaris blocats…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "Comptes…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:199
#: src/pages/status.jsx:925
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "Inicia sessió"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "Tendències"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "Federada"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "Dreceres / Columnes…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "Configuració…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "Llistes"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "Totes les llistes"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "Notificació"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "Aquesta notificació és d'un altre compte vostre."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "Mostra totes les notificacions"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} ha reaccionat a la vostra publicació \n"
"amb {emojiObject}"

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} ha compartit una publicació."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} ha impulsat la teva resposta.} other {{account} ha impulsat la teva publicació.}}} other {{account} ha impulsat {postsCount} de les teves publicacions.}}} other {{postType, select, reply {<0><1>{0}</1> persones</0> han impulsat la teva resposta.} other {<2><3>{1}</3> persones</2> han impulsat la teva publicació.}}}}"

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, one {}=1 {{account} et segueix.} other {<0><1>{0}</1> persones</0> et segueixen.}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} ha enviat una sol·licitud de seguiment."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} li agrada la teva resposta.} other {{account} li agrada la teva publicació.}}} other {{account} li agrada {postsCount} de les teves publicacions.}}} other {{postType, select, reply {<0><1>{0}</1> persones</0> els agrada la teva resposta.} other {<2><3>{1}</3> persones</2> els agrada la teva publicació.}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "Ha finalitzat una enquesta vostra o en la qual heu participat."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "Ha finalitzat una enquesta vostra."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "Ha finalitzat una enquesta en què heu votat."

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "Una publicació amb què heu interactuat ha estat editada."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} ha impulsat i li agrada la teva resposta.} other {{account} ha impulsat i li agrada la teva publicació.}}} other {{account} ha impulsat i li agrada {postsCount} de les teves publicacions.}}} other {{postType, select, reply {<0><1>{0}</1> persones</0> han impulsat i els agrada la teva resposta.} other {<2><3>{1}</3> persones</2> han impulsat i els agrada la teva publicació.}}}}"

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} s'hi ha unit."

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} ha denunciat a {targetAccount}"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "S'han perdut les connexions amb <0>{name}</0>."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "Avís de moderació"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr "Ja teniu aquí el vostre #Wrapstodon {year}!"

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "Un administrador de <0>{from}</0> ha suspès <1>{targetName}</1>; això vol dir que ja no en podreu rebre actualitzacions o interactuar-hi."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "Un administrador de <0>{from}</0> ha blocat <1>{targetName}</1>. Seguidors afectats: {followersCount}, seguiments: {followingCount}."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "Heu bloquejat a <0>{targetName}</0>. Seguidors eliminats: {followersCount}, seguidors: {followingCount}."

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "El vostre compte ha rebut un avís de moderació."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "S'ha desactivat el vostre compte."

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "S'ha marcat com a sensibles algunes de les vostres publicacions."

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "S'han eliminat algunes de les vostres publicacions."

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "A partir d'ara les vostres publicacions es marcaran com sensibles."

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "S'ha limitat el vostre compte."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "El vostre compte ha estat suspès."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[Tipus de notificació desconeguda: {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1277
#: src/components/status.jsx:1287
msgid "Boosted/Liked by…"
msgstr "Impulsat/Afavorit per…"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "Li agrada a…"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "Impulsat per…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "Seguit per…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "Saber-ne més <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr "Visualitzeu el #Wrapstodon"

#: src/components/notification.jsx:801
#: src/components/status.jsx:486
msgid "Read more →"
msgstr "Llegiu més →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "Votat"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr "{optionVotesCount, plural, one {# vot} other {# vots}}"

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "Amaga els resultats"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "Vota"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1295
#: src/pages/status.jsx:1318
msgid "Refresh"
msgstr "Actualitza"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "Mostra els resultats"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> vot} other {<1>{1}</1> vots}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, one {<0>{0}</0> votant} other {<1>{1}</1> votants}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "Finalitzada<0/>"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "Finalitzada"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "Finalitza <0/>"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "Finalitza"

#: src/components/recent-searches.jsx:27
msgid "Cleared recent searches"
msgstr ""

#: src/components/recent-searches.jsx:49
#: src/components/search-form.jsx:328
msgid "Recent searches"
msgstr ""

#: src/components/recent-searches.jsx:59
msgid "Clear all"
msgstr ""

#: src/components/recent-searches.jsx:94
#: src/pages/account-statuses.jsx:334
msgid "Clear"
msgstr "Neteja"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0}s"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0}m"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0}h"

#: src/components/report-modal.jsx:30
msgid "Spam"
msgstr "Contingut brossa"

#: src/components/report-modal.jsx:31
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "Enllaços maliciosos, interacció falsa o respostes repetitives"

#: src/components/report-modal.jsx:34
msgid "Illegal"
msgstr "Il·legal"

#: src/components/report-modal.jsx:35
msgid "Violates the law of your or the server's country"
msgstr "Viola la llei del vostre país o del país on és el servidor"

#: src/components/report-modal.jsx:38
msgid "Server rule violation"
msgstr "Incompliment d'alguna regla del servidor"

#: src/components/report-modal.jsx:39
msgid "Breaks specific server rules"
msgstr "Viola regles específiques del servidor"

#: src/components/report-modal.jsx:40
msgid "Violation"
msgstr "Violació"

#: src/components/report-modal.jsx:43
msgid "Other"
msgstr "Altres"

#: src/components/report-modal.jsx:44
msgid "Issue doesn't fit other categories"
msgstr "El problema no encaixa en altres categories"

#: src/components/report-modal.jsx:111
msgid "Report Post"
msgstr "Denuncia la publicació"

#: src/components/report-modal.jsx:111
msgid "Report @{username}"
msgstr "Informar sobre @{username}"

#: src/components/report-modal.jsx:147
msgid "Pending review"
msgstr "Pendent de revisió"

#: src/components/report-modal.jsx:189
msgid "Post reported"
msgstr "Publicació denunciada"

#: src/components/report-modal.jsx:189
msgid "Profile reported"
msgstr "Perfil denunciat"

#: src/components/report-modal.jsx:197
msgid "Unable to report post"
msgstr "No ha estat possible denunciar la publicació"

#: src/components/report-modal.jsx:198
msgid "Unable to report profile"
msgstr "No ha estat possible denunciar el perfil"

#: src/components/report-modal.jsx:206
msgid "What's the issue with this post?"
msgstr "Quin és el problema amb aquesta publicació?"

#: src/components/report-modal.jsx:207
msgid "What's the issue with this profile?"
msgstr "Quin és el problema amb aquest perfil?"

#: src/components/report-modal.jsx:276
msgid "Additional info"
msgstr "Informació addicional"

#: src/components/report-modal.jsx:299
msgid "Forward to <0>{domain}</0>"
msgstr "Reenvia a <0>{domain}</0>"

#: src/components/report-modal.jsx:309
msgid "Send Report"
msgstr "Envia informe"

#: src/components/report-modal.jsx:318
msgid "Muted {username}"
msgstr "Silencia a @{username}"

#: src/components/report-modal.jsx:321
msgid "Unable to mute {username}"
msgstr "No ha estat possible silenciar a {username}"

#: src/components/report-modal.jsx:326
msgid "Send Report <0>+ Mute profile</0>"
msgstr "Envia l'informe <0>+ Silencia el perfil</0>"

#: src/components/report-modal.jsx:337
msgid "Blocked {username}"
msgstr "Bloca a {username}"

#: src/components/report-modal.jsx:340
msgid "Unable to block {username}"
msgstr "No ha estat possible blocar a {username}"

#: src/components/report-modal.jsx:345
msgid "Send Report <0>+ Block profile</0>"
msgstr "Envia l'informe <0>+ Bloca el perfil</0>"

#: src/components/search-form.jsx:18
msgid "Posts with <0>{query}</0>"
msgstr "Publicacions amb <0>{query}</0>"

#: src/components/search-form.jsx:26
msgid "Accounts with <0>{query}</0>"
msgstr "Comptes amb <0>{query}</0>"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:34
msgid "Posts tagged with <0>#{0}</0>"
msgstr "Publicacions etiquetades amb <0>#{0}</0>"

#: src/components/search-form.jsx:43
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>‒ comptes, etiquetes i publicacions</0>"

#: src/components/search-form.jsx:135
msgid "Look up <0>{query}</0>"
msgstr "Cerca <0>{query}</0>"

#: src/components/search-form.jsx:361
#: src/pages/home.jsx:251
msgid "See all"
msgstr "Veure-ho tot"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "Inici / Seguint"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "Públic (Local / Federat)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "Compte"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "Etiqueta"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "ID de llista"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "Només local"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:203
msgid "Instance"
msgstr "Instància"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "Opcional, p. ex. mastodon.social"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "Cerca terme"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "Opcional, tret que sigui per al mode de múltiples columnes"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "p. ex. PixelArt (màx. 5, separats per espais)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "Només mèdia"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "Dreceres"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "beta"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "Especifiqueu una llista de dreceres que apareixeran com a:"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "Botó flotant"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "Pestanya/Barra de menú"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "Múltiples columnes"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "No disponible en el mode de visualització actual"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "Mou cap amunt"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "Mou cap avall"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1557
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "Edita"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "Afegiu més d'una drecera/columna perquè funcioni."

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "No hi ha columnes encara. Toqueu el botó Afegeix columna."

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "No hi ha dreceres encara. Toqueu el botó Afegeix drecera."

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "No saps què afegir?<0/>Prova d'afegir primer <1>Pàgina d'Inici / Seguint i notificacions</1>."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "Màxim {SHORTCUTS_LIMIT} columnes"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "Màxim {SHORTCUTS_LIMIT} dreceres"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "Importa/exporta"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "Afegeix una columna…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "Afegeix una drecera…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "La llista específica és opcional. Per al mode de múltiples columnes, la llista és necessària, en cas contrari la columna no es mostrarà."

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "Per al mode de múltiples columnes, cal un terme de cerca, en cas contrari la columna no es mostrarà."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "S'admeten diverses etiquetes. Separades per espais."

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "Edita la drecera"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "Afegeix una drecera"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "Línia de temps"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "Llista"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "Importar/exportar <0>dreceres</0>"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "Importa"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "Enganxeu dreceres aquí"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "S'estan baixant les dreceres desades a la instància del servidor…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "No es poden baixar les dreceres"

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "Baixeu les dreceres des de la instància del servidor"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "*Ja existeix a les dreceres actuals"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "És possible que la llista no funcioni si prové d'un compte diferent."

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "Format de configuració no vàlid"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "Voleu afegir-hi les dreceres actuals?"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr "Només s'afegiran les dreceres que no existeixen a les dreceres actuals."

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "No hi ha dreceres noves per importar"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "Dreceres importades. S'ha superat el màxim de {SHORTCUTS_LIMIT}, de manera que la resta no s'importaran."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "Dreceres importades"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "Importa i annexa…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "Voleu reemplaçar les dreceres actuals?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "Voleu importar les dreceres?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "o reemplaçar-les…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "Importa…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "Exporta"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "S'han copiat les dreceres"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "No s'han pogut copiar les dreceres"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "S'ha copiat la configuració de la drecera"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "No es pot copiar la configuració de la drecera"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "Comparteix"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "S'estan desant les dreceres a la instància del servidor…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "S'han desat les dreceres"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "No es poden desar les dreceres"

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "Sincronitza amb la instància del servidor"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0 , plural, one {# caràcter} other {# caràcters}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "Dreceres JSON en brut"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "Importació o exportació de la configuració des de o cap a la instància del servidor (molt experimental)"

#: src/components/status.jsx:351
msgid "Unable to format math"
msgstr "No s'ha pogut donar format matemàtic"

#: src/components/status.jsx:365
msgid "Math expressions found."
msgstr "Expressions matemàtiques trobades."

#. Action to switch from rendered math back to raw (LaTeX) markup
#: src/components/status.jsx:368
msgid "Show markup"
msgstr "Mostra el marcatge"

#. Action to render math expressions from raw (LaTeX) markup
#: src/components/status.jsx:373
msgid "Format math"
msgstr "Dona format matemàtic"

#: src/components/status.jsx:772
msgid "<0/> <1>boosted</1>"
msgstr "<0/> <1>ha impulsat</1>"

#: src/components/status.jsx:875
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "Ho sentim, la instància en què heu iniciat la sessió actual no pot interactuar amb aquesta publicació des d'una altra instància."

#. placeholder {0}: username || acct
#: src/components/status.jsx:1029
msgid "Unliked @{0}'s post"
msgstr "La publicació de @{0} s'ha eliminat dels favorits"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1030
msgid "Liked @{0}'s post"
msgstr "La publicació de @{0} s'ha afegit als favorits"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1069
msgid "Unbookmarked @{0}'s post"
msgstr "La publicació de @{0} s'ha eliminat dels marcadors"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1070
msgid "Bookmarked @{0}'s post"
msgstr "La publicació de @{0} s'ha afegit als marcadors"

#: src/components/status.jsx:1169
msgid "Some media have no descriptions."
msgstr "No tots els mèdia tenen descripció."

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1176
msgid "Old post (<0>{0}</0>)"
msgstr "Publicacions antigues (<0>{0}</0>)"

#: src/components/status.jsx:1200
#: src/components/status.jsx:1240
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
msgid "Unboost"
msgstr "Desfés l'impuls"

#: src/components/status.jsx:1216
#: src/components/status.jsx:2715
msgid "Quote"
msgstr "Cita"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1228
#: src/components/status.jsx:1694
msgid "Unboosted @{0}'s post"
msgstr "S'ha eliminat l'impuls de la publicació de @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1229
#: src/components/status.jsx:1695
msgid "Boosted @{0}'s post"
msgstr "Heu impulsat la publicació de @{0}"

#: src/components/status.jsx:1241
msgid "Boost…"
msgstr "Impulsa…"

#: src/components/status.jsx:1253
#: src/components/status.jsx:1988
#: src/components/status.jsx:2736
msgid "Unlike"
msgstr "Ja no m'agrada"

#: src/components/status.jsx:1254
#: src/components/status.jsx:1988
#: src/components/status.jsx:1989
#: src/components/status.jsx:2736
#: src/components/status.jsx:2737
msgid "Like"
msgstr "M'agrada"

#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
msgid "Unbookmark"
msgstr "Suprimeix l'adreça d'interès"

#: src/components/status.jsx:1346
msgid "Post text copied"
msgstr "Publica el text copiat"

#: src/components/status.jsx:1349
msgid "Unable to copy post text"
msgstr "No es pot copiar el text de la publicació"

#: src/components/status.jsx:1355
msgid "Copy post text"
msgstr "Copia el text de la publicació"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1373
msgid "View post by <0>@{0}</0>"
msgstr "Mostra la publicació de <0>@{0}</0>"

#: src/components/status.jsx:1394
msgid "Show Edit History"
msgstr "Mostra l'historial d'edició"

#: src/components/status.jsx:1397
msgid "Edited: {editedDateText}"
msgstr "Editat: {editedDateText}"

#: src/components/status.jsx:1464
#: src/components/status.jsx:3516
msgid "Embed post"
msgstr "Insereix la publicació"

#: src/components/status.jsx:1478
msgid "Conversation unmuted"
msgstr "La conversa ha deixat d'estar silenciada"

#: src/components/status.jsx:1478
msgid "Conversation muted"
msgstr "Conversa silenciada"

#: src/components/status.jsx:1484
msgid "Unable to unmute conversation"
msgstr "No s'ha pogut reactivar la conversa"

#: src/components/status.jsx:1485
msgid "Unable to mute conversation"
msgstr "No ha estat possible silenciar la conversa"

#: src/components/status.jsx:1494
msgid "Unmute conversation"
msgstr "Deixa de silenciar la conversa"

#: src/components/status.jsx:1501
msgid "Mute conversation"
msgstr "Silencia la conversa"

#: src/components/status.jsx:1517
msgid "Post unpinned from profile"
msgstr "La publicació ja no està fixada al perfil"

#: src/components/status.jsx:1518
msgid "Post pinned to profile"
msgstr "La publicació s'ha fixat al perfil"

#: src/components/status.jsx:1523
msgid "Unable to unpin post"
msgstr "No s'ha pogut desenganxar la publicació"

#: src/components/status.jsx:1523
msgid "Unable to pin post"
msgstr "No s'ha pogut desenganxar la publicació"

#: src/components/status.jsx:1532
msgid "Unpin from profile"
msgstr "Desfixa del perfil"

#: src/components/status.jsx:1539
msgid "Pin to profile"
msgstr "Fixa al perfil"

#: src/components/status.jsx:1568
msgid "Delete this post?"
msgstr "Voleu suprimir aquesta publicació?"

#: src/components/status.jsx:1584
msgid "Post deleted"
msgstr "Publicació esborrada"

#: src/components/status.jsx:1587
msgid "Unable to delete post"
msgstr "No ha estat possible esborrar la publicació"

#: src/components/status.jsx:1615
msgid "Report post…"
msgstr "Denuncia la publicació…"

#: src/components/status.jsx:1989
#: src/components/status.jsx:2025
#: src/components/status.jsx:2737
msgid "Liked"
msgstr "M'ha agradat"

#: src/components/status.jsx:2022
#: src/components/status.jsx:2724
msgid "Boosted"
msgstr "Millorats"

#: src/components/status.jsx:2032
#: src/components/status.jsx:2749
msgid "Bookmarked"
msgstr "Afegit a marcadors"

#: src/components/status.jsx:2036
msgid "Pinned"
msgstr "Fixat"

#: src/components/status.jsx:2082
#: src/components/status.jsx:2561
msgid "Deleted"
msgstr "Eliminat"

#: src/components/status.jsx:2123
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {# resposta} other {# respostes}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2213
msgid "Thread{0}"
msgstr "Fil{0}"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
#: src/components/status.jsx:2457
msgid "Show less"
msgstr "Mostra'n menys"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
msgid "Show content"
msgstr "Mostra el contingut"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2453
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Filtrat: {0}"

#: src/components/status.jsx:2457
msgid "Show media"
msgstr "Mostra els mèdia"

#: src/components/status.jsx:2597
msgid "Edited"
msgstr "Editat"

#: src/components/status.jsx:2674
msgid "Comments"
msgstr "Comentaris"

#. More from [Author]
#: src/components/status.jsx:2974
msgid "More from <0/>"
msgstr "Més de <0/>"

#: src/components/status.jsx:3276
msgid "Edit History"
msgstr "Edita l'Historial"

#: src/components/status.jsx:3280
msgid "Failed to load history"
msgstr "No s'ha pogut carregar l'historial"

#: src/components/status.jsx:3285
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "Carregant…"

#: src/components/status.jsx:3521
msgid "HTML Code"
msgstr "Codi HTML"

#: src/components/status.jsx:3538
msgid "HTML code copied"
msgstr "Codi HTML copiat"

#: src/components/status.jsx:3541
msgid "Unable to copy HTML code"
msgstr "No s'ha pogut copiar el codi HTML"

#: src/components/status.jsx:3553
msgid "Media attachments:"
msgstr "Adjunts multimèdia:"

#: src/components/status.jsx:3575
msgid "Account Emojis:"
msgstr "Emojis d'aquest compte:"

#: src/components/status.jsx:3606
#: src/components/status.jsx:3651
msgid "static URL"
msgstr "URL estàtic"

#: src/components/status.jsx:3620
msgid "Emojis:"
msgstr "Emojis:"

#: src/components/status.jsx:3665
msgid "Notes:"
msgstr "Notes:"

#: src/components/status.jsx:3669
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "Això és estàtic, sense estil i sense guió. És possible que hàgiu d'aplicar els vostres propis estils i editar-los segons sigui necessari."

#: src/components/status.jsx:3675
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "Les enquestes no són interactives, es converteixen en una llista amb recompte de vots."

#: src/components/status.jsx:3680
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "Els mèdia adjunts poden ser imatges, vídeos, àudios o qualsevol altre tipus de fitxer."

#: src/components/status.jsx:3686
msgid "Post could be edited or deleted later."
msgstr "La publicació pot ser editada o eliminada després."

#: src/components/status.jsx:3692
msgid "Preview"
msgstr "Vista prèvia"

#: src/components/status.jsx:3701
msgid "Note: This preview is lightly styled."
msgstr "Nota: a aquesta vista prèvia se li ha aplicat cert estil."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3954
msgid "<0/> <1/> boosted"
msgstr "<0/> <1/> ha impulsat"

#: src/components/status.jsx:4056
msgid "Post hidden by your filters"
msgstr "Publicació amagada pels filtres"

#: src/components/status.jsx:4057
msgid "Post pending"
msgstr ""

#: src/components/status.jsx:4058
#: src/components/status.jsx:4059
#: src/components/status.jsx:4060
#: src/components/status.jsx:4061
msgid "Post unavailable"
msgstr ""

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "Publicacions noves"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1078
#: src/pages/status.jsx:1455
msgid "Try again"
msgstr "Torna-ho a provar"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, one {# impuls} other {# impulsos}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr "Publicacions fixades"

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "Fil"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>Filtrat</0>: <1>{0}</1>"

#: src/components/translation-block.jsx:196
msgid "Auto-translated from {sourceLangText}"
msgstr "Traducció automàtica des del {sourceLangText}"

#: src/components/translation-block.jsx:234
msgid "Translating…"
msgstr "Traducció en procés…"

#: src/components/translation-block.jsx:237
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "Tradueix des del {sourceLangText} (autodetectat)"

#: src/components/translation-block.jsx:238
msgid "Translate from {sourceLangText}"
msgstr "Tradueix des del {sourceLangText}"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:266
msgid "Auto ({0})"
msgstr "Automàtic ({0})"

#: src/components/translation-block.jsx:279
msgid "Failed to translate"
msgstr "No s'ha pogut traduir"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "S'està editant la publicació original"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "En resposta a @{0}"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "Podeu tancar aquesta pàgina ara."

#: src/compose.jsx:71
msgid "Close window"
msgstr "Tanca la finestra"

#: src/compose.jsx:87
msgid "Login required."
msgstr "Cal iniciar sessió."

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:280
msgid "Go home"
msgstr "Ves a la pàgina d'inici"

#: src/pages/account-statuses.jsx:241
msgid "Account posts"
msgstr "Publicacions del compte"

#: src/pages/account-statuses.jsx:248
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ Respostes)"

#: src/pages/account-statuses.jsx:250
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- Impulsos)"

#: src/pages/account-statuses.jsx:252
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:254
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (Mèdia)"

#: src/pages/account-statuses.jsx:260
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:331
msgid "Clear filters"
msgstr "Neteja els filtres"

#: src/pages/account-statuses.jsx:348
msgid "Showing post with replies"
msgstr "Es mostren les publicacions amb respostes"

#: src/pages/account-statuses.jsx:353
msgid "+ Replies"
msgstr "+ Respostes"

#: src/pages/account-statuses.jsx:359
msgid "Showing posts without boosts"
msgstr "Es mostren publicacions sense impulsos"

#: src/pages/account-statuses.jsx:364
msgid "- Boosts"
msgstr "- Impulsos"

#: src/pages/account-statuses.jsx:370
msgid "Showing posts with media"
msgstr "Es mostren les publicacions amb mèdia"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:387
msgid "Showing posts tagged with #{0}"
msgstr "Es mostren les publicacions etiquetades amb #{0}"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:426
msgid "Showing posts in {0}"
msgstr "Es mostren publicacions a {0}"

#: src/pages/account-statuses.jsx:516
msgid "Nothing to see here yet."
msgstr "No hi ha res a veure encara."

#: src/pages/account-statuses.jsx:517
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "No ha estat possible carregar les publicacions"

#: src/pages/account-statuses.jsx:558
#: src/pages/account-statuses.jsx:588
msgid "Unable to fetch account info"
msgstr "No ha estat possible la informació del compte"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:565
msgid "Switch to account's instance {0}"
msgstr "Canvia a la instància del compte {0}"

#: src/pages/account-statuses.jsx:595
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "Canvia a la meva instància (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:668
msgid "Month"
msgstr "Mes"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "Actual"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "Per defecte"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "Canvia a aquest compte"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "Canvia a una pestanya/finestra nova"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "Veure el perfil…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "Estableix com a predeterminat"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr "Voleu tancar la sessió de <0>@{0}</0>?"

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "Tanca la sessió…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr "Connectat a {0} (<0/>)"

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "Afegeix un compte existent"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "Nota: el compte <0>Per defecte</0> sempre s'utilitzarà per a la primera càrrega. Si canvieu de compte, aquest es mantindrà obert durant la sessió."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "No teniu marcadors. Cerqueu alguna cosa per guardar!"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "No es poden carregar les adreces d'interès."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "l'última hora"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "les últimes 2 hores"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "les últimes 3 hores"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "les últimes 4 hores"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "les últimes 5 hores"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "les últimes 6 hores"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "les últimes 7 hores"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "les últimes 8 hores"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "de les últimes 9 hores"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "les últimes 10 hores"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "les últimes 11 hores"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "les últimes 12 hores"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "més de 12 hores"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "Etiquetes seguides"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "Grups"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "Es mostren {selectedFilterCategory, select, all {totes les publicacions} original {publicacions originals} replies {respostes} boosts {impulsos} followedTags {etiquetes seguides} groups {grups} filtered {publicacions filtrades}}, {sortBy, select, createdAt {{sortOrder, select, asc {més antiga} desc {més recent}}} reblogsCount {{sortOrder, select, asc {amb menys impulsos} desc {amb més impulsos}}} favouritesCount {{sortOrder, select, asc {amb menys favorits} desc {amb més favorits}}} repliesCount {{sortOrder, select, asc {amb menys respostes} desc {amb més respostes}}} density {{sortOrder, select, asc {amb menys densitat} desc {amb més densitat}}}} primer{groupBy, select, account {, agrupat per autors} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "Posada al dia <0>beta</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "Ajuda"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "Què és això?"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "Posada al dia és una línia de temps independent per als vostres seguiments, que ofereix una visió ràpida i precisa, amb una interfície senzilla inspirada en el correu electrònic que permet ordenar i filtrar les publicacions sense esforç."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Vista prèvia de la interfície de Posada al dia"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "Posem-nos al dia"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "Poseu-vos al dia amb les publicacions de les persones que seguiu."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "Mostra'm totes les publicacions de…"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "fins al màxim"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "Posada al dia"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "Superposa amb la darrera posada al dia"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "Fins a l'última posada al dia ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "Nota: la vostra instància només pot mostrar un màxim de 800 publicacions a la línia de temps d'inici, independentment de l'interval de temps. Aquest valor podria variar."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "Anteriorment…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, one {# publicació} other {# publicacions}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "Voleu eliminar aquesta posada al dia?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr "S'està eliminant la Posada al dia {0}"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr "S'ha eliminat la Posada al dia {0}"

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "Nota: només s'emmagatzemaran un màxim de 3. La resta s'eliminarà automàticament."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "Recuperant publicacions…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "Això pot trigar una estona."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "Restableix els filtres"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "Enllaços populars"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "Compartit per {0}"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:330
msgid "All"
msgstr "Totes"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0 , plural, one {# autor} other {# autors}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "Ordre"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Data"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Densitat"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr "Filtra"

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "Autors"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "Cap"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "Mostra tots els autors"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "No cal que ho llegiu tot."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "Això és tot."

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "Torna a dalt"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "Enllaços compartits pels vostres seguits, ordenats per recomptes de compartits, impulsos i M'agrada."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "Ordre: Densitat"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "Les publicacions s'ordenen per densitat o quantitat d'informació. Les publicacions més curtes són \"més lleugeres\" mentre que les publicacions més llargues són \"més pesades\". Les publicacions amb fotos són \"més pesades\" que les publicacions sense fotos."

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "Grup: Autors"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "Les publicacions s'agrupen per autors, ordenades pel nombre de publicacions per autor."

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "Actor següent"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "Autor anterior"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "Torna a dalt"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "No teniu favorits. Cerqueu alguna cosa que us agradi!"

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "No ha estat possible carregar els favorits."

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "Inici i llistes"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "Línies de temps públiques"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "Converses"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "Perfils"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "Mai"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "Filtre nou"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0 , plural, one {# filtre} other {# filtres}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "No ha estat possible carregar els filtres."

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "Encara no hi ha cap filtre."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "Afegeix un filtre"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "Edita el filtre"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "No ha estat possible editar el filtre"

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "No ha estat possible crear el filtre"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "Títol"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "Paraula sencera"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "No hi ha paraules clau. Afegiu-ne una."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "Afegeix una paraula clau"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, one {# paraula clau} other {# paraules clau}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "Filtra des de…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* Encara no s'ha implementat"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "Status: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "Canvia la caducitat"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "Caducitat"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "La publicació filtrada serà…"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr "enfosquit (només mèdia)"

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "minimitzada"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "oculta"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "Voleu suprimir aquest filtre?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "No ha estat possible esborrar el filtre."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "Ha vençut"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "Finalitza en <0/>"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "No caduca mai"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural, one {# etiqueta} other {# etiquetes}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "No ha estat possible carregar les etiquetes seguides."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "Encara no seguiu cap etiqueta."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "No hi ha res a veure ací."

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "No ha estat possible carregar les publicacions."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{hashtagTitle} (només multimèdia) a {instance}"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{hashtagTitle} a {instance}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (només multimèdia)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "Ningú ha publicat res encara amb aquesta etiqueta."

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "No ha estat possible carregar les publicacions amb aquesta etiqueta"

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "Voleu deixar de seguir #{hashtag}?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "Deixa de seguir l'etiqueta #{hashtag}"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "Etiquetes seguides #{hashtag}"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "Seguint…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "No es mostra al perfil"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "No s'ha eliminat dels elements destacats del perfil"

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "Destacades al perfil"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr "{TOTAL_TAGS_LIMIT, plural, one {}other {Màxim de # etiquetes}}"

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "Afegeix etiqueta"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "Elimina etiqueta"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr "{SHORTCUTS_LIMIT, plural, one {S'ha arribat al màxim de # drecera. No es pot afegir un altra.} other {S'ha arribat al màxim de # dreceres. No es pot afegir un altra.}}"

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "Aquesta drecera ja existeix"

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr "S'ha afegit una drecera a l'etiqueta"

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "Afegeix a les dreceres"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "Introduïu una nova instància, p. ex. \"mastodon.social\""

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "La instància no és vàlida"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "Ves a una altra instància…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "Ves a la meva instància (<0>{currentInstance}</0>)"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "No s'han pogut recuperar les notificacions."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<0>Noves</0> <1>sol·licituds de seguiment</1>"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "S'està resolent…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "No es pot resoldre l'URL"

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "Encara res."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "Gestiona els membres"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "Voleu suprimir a <0>@{0}</0> de la llista?"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "Suprimeix…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, one {# llista} other {# llistes}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "Encara no hi ha cap llista."

#: src/pages/login.jsx:122
#: src/pages/login.jsx:132
msgid "Failed to register application"
msgstr "No s'ha pogut registrar l'aplicació"

#: src/pages/login.jsx:218
msgid "instance domain"
msgstr "domini de la instància"

#: src/pages/login.jsx:242
msgid "e.g. “mastodon.social”"
msgstr "p. ex. \"mastodont.social\""

#: src/pages/login.jsx:253
msgid "Failed to log in. Please try again or try another instance."
msgstr "No s'ha pogut iniciar la sessió. Torneu-ho a provar o canvieu d'instància."

#: src/pages/login.jsx:265
msgid "Continue with {selectedInstanceText}"
msgstr "Continueu amb {selectedInstanceText}"

#: src/pages/login.jsx:266
msgid "Continue"
msgstr "Continua"

#: src/pages/login.jsx:274
msgid "Don't have an account? Create one!"
msgstr "No hi teniu compte? Creeu-n'hi un!"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "Mencions privades"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "Privat"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "Ningú us ha esmentat :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "No ha estat possible carregar les mencions."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "No segueixes"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "Qui no et segueix"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "Amb un compte nou"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr "Amb menció privada no sol·licitada"

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr "Qui està limitat per la moderació del servidor"

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "Configuració de les notificacions"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "Notificacions noves"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, one {Anunci} other {Anuncis}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "Sol·licituds de seguiment"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, one {# petició de seguiment} other {# peticions de seguiment}}"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr "{0, plural, one {Notificacions filtrades d'# persona} other {Notificacions filtrades de # persones}}"

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "Només mencions"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "Avui"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "Ja les heu vist totes."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "Ahir"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "No s'han carregat les notificacions"

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "S’ha actualitzat la configuració de notificacions"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "Exclou les notificacions de:"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "Filtra"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "Ignora"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "Actualitzat <0>{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr "Consulteu les notificacions de <0>@{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr "Notificacions de <0>@{0}</0>"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr "Les notificacions de @{0} no es filtraran a partir d'ara."

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr "No es pot acceptar la sol·licitud de notificació"

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "Permet"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr "Les notificacions de @{0} no es mostraran a les notificacions filtrades a partir d'ara."

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr "No s'ha pogut descartar la sol·licitud de notificació"

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "Ometre"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "Omeses"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "Línia de temps local ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "Línia de temps federada ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "Línia de temps local"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "Línia de temps federada"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "Ningú ha publicat res encara."

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "Canvia a federada"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "Canvia a local"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr "No hi ha publicacions programades."

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr "Programada el <0><1/></0> <2>({0})</2>"

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr "Programada <0><1/></0>"

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr "Publicació programada reprogramada"

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr "No s'ha pogut reprogramar la publicació"

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr "Reprograma"

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr "Voleu suprimir la publicació programada?"

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr "S'ha suprimit la publicació programada"

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr "No s'ha pogut suprimir la publicació programada"

#: src/pages/search.jsx:51
msgid "Search: {q} (Posts)"
msgstr "Cerca: {q} (publicacions)"

#: src/pages/search.jsx:54
msgid "Search: {q} (Accounts)"
msgstr "Cerca: {q} (comptes)"

#: src/pages/search.jsx:57
msgid "Search: {q} (Hashtags)"
msgstr "Cerca: {q} (etiquetes)"

#: src/pages/search.jsx:60
msgid "Search: {q}"
msgstr "Cerca: {q}"

#: src/pages/search.jsx:340
#: src/pages/search.jsx:422
msgid "Hashtags"
msgstr "Etiquetes"

#: src/pages/search.jsx:372
#: src/pages/search.jsx:426
#: src/pages/search.jsx:496
msgid "See more"
msgstr "Mostra'n més"

#: src/pages/search.jsx:398
msgid "See more accounts"
msgstr "Veure més comptes"

#: src/pages/search.jsx:412
msgid "No accounts found."
msgstr "No s'ha trobat cap compte."

#: src/pages/search.jsx:468
msgid "See more hashtags"
msgstr "Veure més etiquetes"

#: src/pages/search.jsx:482
msgid "No hashtags found."
msgstr "No s'ha trobat cap etiqueta."

#: src/pages/search.jsx:526
msgid "See more posts"
msgstr "Veure més publicacions"

#: src/pages/search.jsx:540
msgid "No posts found."
msgstr "No s'ha trobat cap publicació."

#: src/pages/search.jsx:585
msgid "Enter your search term or paste a URL above to get started."
msgstr "Introduïu el vostre terme de cerca o enganxeu un URL a dalt per començar."

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "Configuració"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "Aparença"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "Clar"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "Fosc"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "Automàtic"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "Mida del text"

#. Preview of one character, in largest size
#. Preview of one character, in smallest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "A"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "Llengua de visualització"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "Ajudeu a traduir"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "Publicacions"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "Visibilitat per defecte"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "S'ha sincronitzat"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "No s'ha pogut actualitzar la privadesa de la publicació"

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "Sincronitzat amb la configuració de la instància del servidor. \n"
"<0>Aneu a la vostra instància ({instance}) per realitzar més canvis en la configuració.</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "Funcions experimentals"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "Actualitza automàticament les publicacions de la línia de temps"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "Carrusel d'impulsos"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "Traducció de les publicacions"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr "Tradueix a "

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "Llengua del sistema ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr "{0, plural, =0 {Amaga el botó \"Tradueix\" per a:} other {Amaga el botó \"Tradueix\" per a (#):}}"

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr "NOTA: Aquesta funció utilitza serveis de traducció externs, impulsat per <0>{TRANSLATION_API_NAME}</0>."

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "Traducció automàtica en línia"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr "Mostra automàticament la traducció de les publicacions a la línia de temps. Només funciona per a publicacions <0>breus</0> sense advertència de contingut, contingut multimèdia o enquesta."

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "Selector de GIF per a compositor"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "Nota: aquesta funcionalitat utilitza un servei de cerca GIF extern, impulsat per <0>GIPHY</0>. Classificació G (apte per a la visualització per a totes les edats), els paràmetres de seguiment s'eliminen, la informació de referència s'omet de les sol·licituds, però les consultes de cerca i la informació de l'adreça IP encara arribaran als seus servidors."

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "Generador de descripcions d'imatges"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr "Només per a imatges noves mentre es redacten publicacions noves."

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "Nota: aquesta funcionalitat utilitza un servei d'IA extern, impulsat per <0>img-alt-api</0>. Pot ser que no funcioni bé. Només per a imatges i en anglès."

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr "Notificacions agrupades del servidor"

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr "Funcionalitat en fase alfa. Finestra d'agrupació potencialment millorada, però amb una lògica d'agrupació bàsica."

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "Sincronitza la configuració al núvol"

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr "⚠️⚠️⚠️ Funcionalitat experimental.<0/>S'emmagatzemen en les notes del perfil propi. Les notes del perfil (privades) s'utilitzen principalment per altres perfils i estan amagades del perfil propi."

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "Nota: Aquesta funcionalitat utilitza l'API del servidor on l'usuari ha iniciat la sessió."

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr "Mode ocult <0>(<1>Text</1> → <2>████</2>)</0>"

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr "Substitueix el text per blocs, útil per prendre captures de pantalla per raons de privacitat."

#: src/pages/settings.jsx:710
msgid "About"
msgstr "Quant a"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr "<0>Creat</0> per <1>@cheeaun</1>"

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "Patrocinadors"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Donacions"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr "Què hi ha de nou"

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "Política de privadesa"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>Lloc web:</0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>Versió:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "Informació de la versió copiada"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr "No s'ha pogut copiar la versió"

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr "No s'ha actualitzat la subscripció. Si us plau, intenta-ho de nou."

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr "No s'ha eliminat la subscripció. Si us plau, intenta-ho de nou."

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "Notificacions (beta)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr "Les notificacions estan bloquejades. Si us plau, activeu-les al vostre navegador."

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "Permet-les de <0>{0}</0>"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "qualsevol"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "persones que segueixo"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "seguidors"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "Seguiments"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "Enquestes"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "Edició de publicacions"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr "No s'ha concedit el permís d'enviar notificacions des del darrer inici de sessió. Haureu d'<0><1>iniciar la sessió</1> de nou per concedir aquest permís</0>."

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "NOTA: les notificacions només funcionen per a <0>un compte</0>."

#. js-lingui-explicit-id
#: src/pages/status.jsx:658
#: src/pages/status.jsx:1221
msgid "post.title"
msgstr "Publicació"

#: src/pages/status.jsx:912
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "No heu iniciat la sessió. Les interaccions (resposta, impuls, etc.) no són possibles."

#: src/pages/status.jsx:932
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "Aquesta publicació és d'una altra instància (<0>{instance}</0>). Les interaccions (resposta, impuls, etc.) no són possibles."

#: src/pages/status.jsx:960
msgid "Error: {e}"
msgstr "Error: {e}"

#: src/pages/status.jsx:967
msgid "Switch to my instance to enable interactions"
msgstr "Canvia a la meva instància per permetre interaccions"

#: src/pages/status.jsx:1069
msgid "Unable to load replies."
msgstr "No s'han pogut obtenir les respostes."

#: src/pages/status.jsx:1181
msgid "Back"
msgstr "Enrere"

#: src/pages/status.jsx:1212
msgid "Go to main post"
msgstr "Ves a la publicació principal"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1235
msgid "{0} posts above ‒ Go to top"
msgstr "{0} publicacions més amunt ‒ Ves a la part superior"

#: src/pages/status.jsx:1282
#: src/pages/status.jsx:1345
msgid "Switch to Side Peek view"
msgstr "Canvia a vista lateral"

#: src/pages/status.jsx:1346
msgid "Switch to Full view"
msgstr "Canvia a vista completa"

#: src/pages/status.jsx:1364
msgid "Show all sensitive content"
msgstr "Mostra tot el contingut sensitiu"

#: src/pages/status.jsx:1369
msgid "Experimental"
msgstr "Experimental"

#: src/pages/status.jsx:1378
msgid "Unable to switch"
msgstr "No s'ha pogut canviar"

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1385
msgid "Switch to post's instance ({0})"
msgstr "Canvia a la instància de la publicació ({0})"

#: src/pages/status.jsx:1388
msgid "Switch to post's instance"
msgstr "Canvia a la instància de la publicació"

#: src/pages/status.jsx:1446
msgid "Unable to load post"
msgstr "No ha estat possible carregar la publicació"

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1582
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, one {# resposta} other {<0>{1}</0> respostes}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1600
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, one {# comentari} other {<0>{0}</0> comentaris}}"

#: src/pages/status.jsx:1622
msgid "View post with its replies"
msgstr "Mostra la publicació amb respostes"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "En tendència ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "Ara és tendència"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr "Per {0}"

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "Torna a les publicacions en tendència"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "Mostra publicacions mencionant <0>{0}</0>"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "Publicacions influents"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "No hi ha publicacions influents."

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "Un client web per a Mastodon minimalista i original."

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Inicia sessió amb Mastodon"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "Registreu-vos"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "Connecta el teu compte de Mastodon/Fedivers.<0/>Les teves credencials no s'emmagatzemaran en aquest servidor."

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr "<0>Creat</0> per <1>@cheeaun</1>. <2>Política de Privadesa</2>."

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "Captura del Carrusel d'impulsos"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "Carrusel d'impulsos"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "Separeu visualment les publicacions originals de les compartides (impulsos)."

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr "Captura de fil de comentaris imbricats"

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr "Fil de comentaris imbricats"

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "Seguiu les converses sense esforç. Respostes minimitzades."

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr "Captura de les notificacions agrupades"

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "Notificacions agrupades"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr "Les notificacions similars s'agrupen i es contrauen per reduir el desordre."

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "Captura de la interfície d'usuari del mode de múltiples columnes"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "Única o múltiples columnes"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "Per defecte, una única columna per qui busca tranquil·litat. Múltiples columnes configurables per a usuaris avançats."

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "Captura de la línia de temps de diverses etiquetes amb un formulari per afegir més etiquetes"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "Línia de temps de diverses etiquetes"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "Fins a 5 etiquetes combinades en una única línia de temps."

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "Sembla que el vostre navegador bloca les finestres emergents."

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr "Actualment hi ha un esborrany minimitzat. Publiqueu-lo o descarteu-lo abans de crear-ne un de nou."

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr "Actualment hi ha una publicació oberta. Publiqueu-la o descarteu-la abans de crear-ne una de nova."

