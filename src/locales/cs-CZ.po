msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: cs\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-08-05 04:12\n"
"Last-Translator: \n"
"Language-Team: Czech\n"
"Plural-Forms: nplurals=4; plural=(n==1) ? 0 : (n>=2 && n<=4) ? 1 : 3;\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: cs\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:141
msgid "Locked"
msgstr "uzamčeno"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:163
msgid "Posts: {0}"
msgstr "Příspěvky: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:168
msgid "Last posted: {0}"
msgstr "Poslední příspěvek: {0}"

#: src/components/account-block.jsx:183
#: src/components/account-info.jsx:705
msgid "Automated"
msgstr "Automaticky"

#: src/components/account-block.jsx:190
#: src/components/account-info.jsx:710
#: src/components/status.jsx:748
msgid "Group"
msgstr "Skupina"

#: src/components/account-block.jsx:200
msgid "Mutual"
msgstr "Vzájemné"

#: src/components/account-block.jsx:204
#: src/components/account-info.jsx:1907
msgid "Requested"
msgstr "Vyžadováno"

#: src/components/account-block.jsx:208
#: src/components/account-info.jsx:1898
msgid "Following"
msgstr "Sleduji"

#: src/components/account-block.jsx:212
#: src/components/account-info.jsx:1212
msgid "Follows you"
msgstr "Sleduje vás"

#: src/components/account-block.jsx:220
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, one {# odpověď} few {# odpovědi} many {# odpovědi} other {# odpovědi}}"

#: src/components/account-block.jsx:229
#: src/components/account-info.jsx:753
msgid "Verified"
msgstr "Ověřeno"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:244
#: src/components/account-info.jsx:892
msgid "Joined <0>{0}</0>"
msgstr "Připojeno <0>{0}</0>"

#: src/components/account-info.jsx:64
msgid "Forever"
msgstr "Navždy"

#: src/components/account-info.jsx:398
msgid "Unable to load account."
msgstr "Účet nelze načíst."

#: src/components/account-info.jsx:413
msgid "Go to account page"
msgstr "Přejít na stránku účtu"

#: src/components/account-info.jsx:442
#: src/components/account-info.jsx:775
msgid "Followers"
msgstr "Sledující"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:446
#: src/components/account-info.jsx:830
msgid "following.stats"
msgstr ""

#: src/components/account-info.jsx:449
#: src/pages/account-statuses.jsx:495
#: src/pages/search.jsx:345
#: src/pages/search.jsx:492
msgid "Posts"
msgstr "Příspěvky"

#: src/components/account-info.jsx:457
#: src/components/account-info.jsx:1268
#: src/components/compose.jsx:2819
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1998
#: src/components/status.jsx:2015
#: src/components/status.jsx:2140
#: src/components/status.jsx:2769
#: src/components/status.jsx:2772
#: src/pages/account-statuses.jsx:539
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1306
#: src/pages/trending.jsx:474
msgid "More"
msgstr "Více"

#: src/components/account-info.jsx:469
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> naznačil, že jejich nový účet je nyní:"

#: src/components/account-info.jsx:614
#: src/components/account-info.jsx:1496
msgid "Handle copied"
msgstr "Uživatelské jméno X zkopírováno"

#: src/components/account-info.jsx:617
#: src/components/account-info.jsx:1499
msgid "Unable to copy handle"
msgstr "Nepodařilo se zkopírovat uživatelské jméno"

#: src/components/account-info.jsx:623
#: src/components/account-info.jsx:1505
msgid "Copy handle"
msgstr "Zkopírovat uživatelské jméno"

#: src/components/account-info.jsx:629
msgid "Go to original profile page"
msgstr "Přejít na původní stránku profilu"

#: src/components/account-info.jsx:647
msgid "View profile image"
msgstr "Zobrazit profilový obrázek"

#: src/components/account-info.jsx:665
msgid "View profile header"
msgstr "Zobrazit hlavičku profilu"

#: src/components/account-info.jsx:681
#: src/components/account-info.jsx:1806
#: src/components/account-info.jsx:2332
msgid "Edit profile"
msgstr "Upravit profil"

#: src/components/account-info.jsx:700
msgid "In Memoriam"
msgstr "In Memoriam"

#: src/components/account-info.jsx:782
#: src/components/account-info.jsx:840
msgid "This user has chosen to not make this information available."
msgstr "Tento uživatel se rozhodl nezpřístupnit tyto informace."

#. placeholder {0}: shortenNumber(followersCount)
#. placeholder {1}: shortenNumber(followersCount)
#: src/components/account-info.jsx:802
msgid "{followersCount, plural, one {<0>{0}</0> Follower} other {<1>{1}</1> Followers}}"
msgstr ""

#. placeholder {0}: shortenNumber(followingCount)
#: src/components/account-info.jsx:846
msgid "{followingCount, plural, other {<0>{0}</0> Following}}"
msgstr ""

#. placeholder {0}: shortenNumber(statusesCount)
#. placeholder {1}: shortenNumber(statusesCount)
#: src/components/account-info.jsx:870
msgid "{statusesCount, plural, one {<0>{0}</0> Post} other {<1>{1}</1> Posts}}"
msgstr ""

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:917
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0} původní příspěvky, {1} odpovědi, {2} zvyšuje"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:933
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr ""

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:949
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr ""

#: src/components/account-info.jsx:974
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "Originál"

#: src/components/account-info.jsx:978
#: src/components/status.jsx:2553
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1025
#: src/pages/status.jsx:1652
msgid "Replies"
msgstr "Odpovědi"

#: src/components/account-info.jsx:982
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "Boosty"

#: src/components/account-info.jsx:988
msgid "Post stats unavailable."
msgstr "Statistiky příspěvku nejsou k dispozici."

#: src/components/account-info.jsx:1019
msgid "View post stats"
msgstr "Zobrazit statistiky příspěvku"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1216
msgid "Last post: <0>{0}</0>"
msgstr "Poslední příspěvek: <0>{0}</0>"

#: src/components/account-info.jsx:1230
msgid "Muted"
msgstr "Ztlumeno"

#: src/components/account-info.jsx:1235
msgid "Blocked"
msgstr "Blokováno"

#: src/components/account-info.jsx:1244
msgid "Private note"
msgstr "Soukromá poznámka"

#: src/components/account-info.jsx:1301
msgid "Mention <0>@{username}</0>"
msgstr "Zmínit <0>@{username}</0>"

#: src/components/account-info.jsx:1313
msgid "Translate bio"
msgstr "Přeložit bio"

#: src/components/account-info.jsx:1324
msgid "Edit private note"
msgstr "Upravit soukromou poznámku"

#: src/components/account-info.jsx:1324
msgid "Add private note"
msgstr "Přidat soukromou poznámku"

#: src/components/account-info.jsx:1344
msgid "Notifications enabled for @{username}'s posts."
msgstr "Upozornění pro příspěvky od @{username} povolena."

#: src/components/account-info.jsx:1345
msgid " Notifications disabled for @{username}'s posts."
msgstr " Upozornění pro příspěvky od @{username} zakázána."

#: src/components/account-info.jsx:1357
msgid "Disable notifications"
msgstr "Vypnout upozornění"

#: src/components/account-info.jsx:1358
msgid "Enable notifications"
msgstr "Zapnout upozornění"

#: src/components/account-info.jsx:1375
msgid "Boosts from @{username} enabled."
msgstr "Boosty od @{username} povoleny."

#: src/components/account-info.jsx:1376
msgid "Boosts from @{username} disabled."
msgstr "Boosty od @{username} zakázány."

#: src/components/account-info.jsx:1387
msgid "Disable boosts"
msgstr "Zakázat boosty"

#: src/components/account-info.jsx:1387
msgid "Enable boosts"
msgstr "Povolit boosty"

#: src/components/account-info.jsx:1406
msgid "@{username} is no longer featured on your profile."
msgstr ""

#: src/components/account-info.jsx:1416
msgid "@{username} is now featured on your profile."
msgstr ""

#: src/components/account-info.jsx:1424
msgid "Unable to unfeature @{username} on your profile."
msgstr ""

#: src/components/account-info.jsx:1428
msgid "Unable to feature @{username} on your profile."
msgstr ""

#: src/components/account-info.jsx:1437
msgid "Don't feature on profile"
msgstr ""

#: src/components/account-info.jsx:1438
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "Zmínit v profilu"

#: src/components/account-info.jsx:1447
msgid "Show featured profiles"
msgstr ""

#: src/components/account-info.jsx:1462
#: src/components/account-info.jsx:1472
#: src/components/account-info.jsx:2078
msgid "Add/Remove from Lists"
msgstr "Přidat/odebrat ze seznamu"

#: src/components/account-info.jsx:1522
#: src/components/status.jsx:1420
msgid "Link copied"
msgstr "Odkaz zkopírován"

#: src/components/account-info.jsx:1525
#: src/components/status.jsx:1423
msgid "Unable to copy link"
msgstr "Nebylo možné zkopírovat soubor"

#: src/components/account-info.jsx:1531
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1429
#: src/components/status.jsx:3547
msgid "Copy"
msgstr "Kopírovat"

#: src/components/account-info.jsx:1546
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1445
msgid "Sharing doesn't seem to work."
msgstr "Sdílení zřejmě nefunguje."

#: src/components/account-info.jsx:1552
#: src/components/status.jsx:1451
msgid "Share…"
msgstr "Sdílet…"

#: src/components/account-info.jsx:1572
msgid "Unmuted @{username}"
msgstr "Zrušeno ztlumení @{username}"

#: src/components/account-info.jsx:1584
msgid "Unmute <0>@{username}</0>"
msgstr "Zrušit ztlumení <0>@{username}</0>"

#: src/components/account-info.jsx:1600
msgid "Mute <0>@{username}</0>…"
msgstr "Ztišit <0>@{username}</0>…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1632
msgid "Muted @{username} for {0}"
msgstr "Ztlumený @{username} na {0}"

#: src/components/account-info.jsx:1644
msgid "Unable to mute @{username}"
msgstr "Nelze ztlumit @{username}"

#: src/components/account-info.jsx:1665
msgid "Remove <0>@{username}</0> from followers?"
msgstr "Odstranit <0>@{username}</0> ze sledujících?"

#: src/components/account-info.jsx:1685
msgid "@{username} removed from followers"
msgstr "@{username} odebrán/a ze sledujících"

#: src/components/account-info.jsx:1697
msgid "Remove follower…"
msgstr "Odstranit sledujícího…"

#: src/components/account-info.jsx:1708
msgid "Block <0>@{username}</0>?"
msgstr "Blokovat <0>@{username}</0>?"

#: src/components/account-info.jsx:1732
msgid "Unblocked @{username}"
msgstr "Odblokován @{username}"

#: src/components/account-info.jsx:1740
msgid "Blocked @{username}"
msgstr "Zablokován @{username}"

#: src/components/account-info.jsx:1748
msgid "Unable to unblock @{username}"
msgstr "Nelze odblokovat uživatele @{username}"

#: src/components/account-info.jsx:1750
msgid "Unable to block @{username}"
msgstr "Nelze zablokovat uživatele @{username}"

#: src/components/account-info.jsx:1760
msgid "Unblock <0>@{username}</0>"
msgstr "Odblokovat <0>@{username}</0>"

#: src/components/account-info.jsx:1769
msgid "Block <0>@{username}</0>…"
msgstr "Blok <0>@{username}</0>…"

#: src/components/account-info.jsx:1786
msgid "Report <0>@{username}</0>…"
msgstr "Nahlásit <0>@{username}</0>…"

#: src/components/account-info.jsx:1842
msgid "Withdraw follow request?"
msgstr "Zrušit žádost o sledování?"

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1843
msgid "Unfollow @{0}?"
msgstr ""

#: src/components/account-info.jsx:1901
msgid "Unfollow…"
msgstr "Přestat sledovat…"

#: src/components/account-info.jsx:1910
msgid "Withdraw…"
msgstr "Vybrat…"

#: src/components/account-info.jsx:1917
#: src/components/account-info.jsx:1921
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "Sledovat"

#: src/components/account-info.jsx:2018
#: src/components/account-info.jsx:2073
#: src/components/account-info.jsx:2207
#: src/components/account-info.jsx:2327
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:892
#: src/components/compose.jsx:2775
#: src/components/compose.jsx:3255
#: src/components/compose.jsx:3464
#: src/components/compose.jsx:3694
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:118
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3271
#: src/components/status.jsx:3511
#: src/components/status.jsx:4020
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1393
msgid "Close"
msgstr "Zavřít"

#: src/components/account-info.jsx:2023
msgid "Translated Bio"
msgstr "Přeložit bio"

#: src/components/account-info.jsx:2118
msgid "Unable to remove from list."
msgstr "Nelze odstranit ze seznamu."

#: src/components/account-info.jsx:2119
msgid "Unable to add to list."
msgstr "Nelze přidat do seznamu."

#: src/components/account-info.jsx:2138
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "Nelze načíst seznamy."

#: src/components/account-info.jsx:2142
msgid "No lists."
msgstr "Žádné seznamy."

#: src/components/account-info.jsx:2153
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "Nový seznam"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2212
msgid "Private note about <0>@{0}</0>"
msgstr "Soukromá poznámka o <0> @{0}</0>"

#: src/components/account-info.jsx:2242
msgid "Unable to update private note."
msgstr "Nelze aktualizovat soukromou poznámku."

#: src/components/account-info.jsx:2265
#: src/components/account-info.jsx:2563
msgid "Cancel"
msgstr "Zrušit"

#: src/components/account-info.jsx:2270
msgid "Save & close"
msgstr "Uložit a zavřít"

#: src/components/account-info.jsx:2387
msgid "Unable to update profile."
msgstr "Nelze aktualizovat profil."

#: src/components/account-info.jsx:2394
msgid "Header picture"
msgstr ""

#: src/components/account-info.jsx:2446
msgid "Profile picture"
msgstr ""

#: src/components/account-info.jsx:2498
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "Název"

#: src/components/account-info.jsx:2511
msgid "Bio"
msgstr "O mně"

#: src/components/account-info.jsx:2524
msgid "Extra fields"
msgstr "Extra pole"

#: src/components/account-info.jsx:2530
msgid "Label"
msgstr "Štítek"

#: src/components/account-info.jsx:2533
msgid "Content"
msgstr "Obsah"

#: src/components/account-info.jsx:2566
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "Uložit"

#: src/components/account-info.jsx:2620
msgid "username"
msgstr "uživatelské jméno"

#: src/components/account-info.jsx:2624
msgid "server domain name"
msgstr "jméno domény serveru"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2690
msgid "Profiles featured by @{0}"
msgstr ""

#: src/components/account-info.jsx:2716
msgid "No featured profiles."
msgstr ""

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "Režim zavření zakázán"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "Režim skrytí aktivován"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "Domovská stránka"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "Vytvořit"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr ""

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr ""

#: src/components/compose.jsx:212
msgid "Take photo or video"
msgstr ""

#: src/components/compose.jsx:213
msgid "Add media"
msgstr "Přidat média"

#: src/components/compose.jsx:214
msgid "Add custom emoji"
msgstr "Přidat vlastní emoji"

#: src/components/compose.jsx:215
msgid "Add GIF"
msgstr "Přidat GIF"

#: src/components/compose.jsx:216
msgid "Add poll"
msgstr "Přidat anketu"

#: src/components/compose.jsx:217
msgid "Schedule post"
msgstr ""

#: src/components/compose.jsx:417
msgid "You have unsaved changes. Discard this post?"
msgstr "Máte neuložené změny. Zrušit tento příspěvek?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:655
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr ""

#: src/components/compose.jsx:665
#: src/components/compose.jsx:683
#: src/components/compose.jsx:1795
#: src/components/compose.jsx:1930
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, one {Můžete připojit pouze 1 soubor.} few {Můžete připojit pouze # souborů.} many {Můžete připojit pouze # souborů.} other {Můžete připojit pouze # souborů.}}"

#: src/components/compose.jsx:873
msgid "Pop out"
msgstr "Vyskakovací okno"

#: src/components/compose.jsx:880
msgid "Minimize"
msgstr "Minimalizovat"

#: src/components/compose.jsx:916
msgid "Looks like you closed the parent window."
msgstr "Zdá se, že jste zavřeli nadřazené okno."

#: src/components/compose.jsx:923
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Vypadá to, že již máte složené pole otevřené v nadřazeném okně a aktuálně publikované. Počkejte prosím, než se to udělá a zkuste to znovu později."

#: src/components/compose.jsx:928
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Vypadá to, že již máte v nadřazeném okně otevřené políčko. Vyskakování v tomto okně zruší změny, které jste provedli v nadřazeném okně. Pokračovat?"

#: src/components/compose.jsx:971
msgid "Pop in"
msgstr "Ukázat v"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:981
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "Odpovědět na příspěvek od @{0} (<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:991
msgid "Replying to @{0}’s post"
msgstr "Odpovídám na příspěvek uživatele @{0}"

#: src/components/compose.jsx:1004
msgid "Editing source post"
msgstr "Editace zdrojového příspěvku"

#: src/components/compose.jsx:1057
msgid "Poll must have at least 2 options"
msgstr "Anketa musí mít alespoň dvě možnosti"

#: src/components/compose.jsx:1061
msgid "Some poll choices are empty"
msgstr "Některé volby anket jsou prázdné"

#: src/components/compose.jsx:1074
msgid "Some media have no descriptions. Continue?"
msgstr "Některá média jsou bez popisu. Pokračovat?"

#: src/components/compose.jsx:1126
msgid "Attachment #{i} failed"
msgstr "Příloha #{i} selhala"

#: src/components/compose.jsx:1222
#: src/components/status.jsx:2328
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "Varování o obsahu"

#: src/components/compose.jsx:1238
msgid "Content warning or sensitive media"
msgstr "Upozornění na obsah nebo citlivá média"

#: src/components/compose.jsx:1274
#: src/components/status.jsx:99
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "Veřejný"

#: src/components/compose.jsx:1279
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:100
msgid "Local"
msgstr "Místní"

#: src/components/compose.jsx:1283
#: src/components/status.jsx:101
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "Neveřejný"

#: src/components/compose.jsx:1286
#: src/components/status.jsx:102
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "Pouze pro sledující"

#: src/components/compose.jsx:1289
#: src/components/status.jsx:103
#: src/components/status.jsx:2204
msgid "Private mention"
msgstr "Soukromá zmínka"

#: src/components/compose.jsx:1298
msgid "Post your reply"
msgstr "Poslat odpověď"

#: src/components/compose.jsx:1300
msgid "Edit your post"
msgstr "Upravit příspěvek"

#: src/components/compose.jsx:1301
msgid "What are you doing?"
msgstr "Co děláte??"

#: src/components/compose.jsx:1380
msgid "Mark media as sensitive"
msgstr "Označit média jako citlivá"

#: src/components/compose.jsx:1417
msgid "Posting on <0/>"
msgstr ""

#: src/components/compose.jsx:1448
#: src/components/compose.jsx:3313
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "Přidat"

#: src/components/compose.jsx:1676
msgid "Schedule"
msgstr ""

#: src/components/compose.jsx:1678
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1192
#: src/components/status.jsx:1978
#: src/components/status.jsx:1979
#: src/components/status.jsx:2673
msgid "Reply"
msgstr "Odpovědět"

#: src/components/compose.jsx:1680
msgid "Update"
msgstr "Aktualizovat"

#: src/components/compose.jsx:1681
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Příspěvek"

#: src/components/compose.jsx:1807
msgid "Downloading GIF…"
msgstr "Stahování GIFu…"

#: src/components/compose.jsx:1835
msgid "Failed to download GIF"
msgstr "Nepodařilo se stáhnout GIF"

#: src/components/compose.jsx:2060
#: src/components/compose.jsx:2153
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "Více…"

#: src/components/compose.jsx:2589
msgid "Uploaded"
msgstr "Odesláno"

#: src/components/compose.jsx:2602
msgid "Image description"
msgstr "Popis obrázku"

#: src/components/compose.jsx:2603
msgid "Video description"
msgstr "Popis videa"

#: src/components/compose.jsx:2604
msgid "Audio description"
msgstr "Popis audia"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2639
#: src/components/compose.jsx:2659
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "Velikost souboru je příliš velká. Při nahrávání mohou vzniknout problémy. Zkuste zmenšit velikost souboru z {0} na {1} nebo nižší."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2651
#: src/components/compose.jsx:2671
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr ""

#: src/components/compose.jsx:2679
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "Snímková frekvence je příliš vysoká. Při nahrávání mohou vzniknout problémy."

#: src/components/compose.jsx:2739
#: src/components/compose.jsx:2989
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "Odstranit"

#: src/components/compose.jsx:2756
#: src/compose.jsx:84
msgid "Error"
msgstr "Chyba"

#: src/components/compose.jsx:2781
msgid "Edit image description"
msgstr "Upravit popis obrázku"

#: src/components/compose.jsx:2782
msgid "Edit video description"
msgstr "Upravit popis videa"

#: src/components/compose.jsx:2783
msgid "Edit audio description"
msgstr "Upravit popis audia"

#: src/components/compose.jsx:2828
#: src/components/compose.jsx:2877
msgid "Generating description. Please wait…"
msgstr "Generování popisu. Počkejte prosím…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2848
msgid "Failed to generate description: {0}"
msgstr ""

#: src/components/compose.jsx:2849
msgid "Failed to generate description"
msgstr "Nepodařilo se vytvořit popis"

#: src/components/compose.jsx:2861
#: src/components/compose.jsx:2867
#: src/components/compose.jsx:2913
msgid "Generate description…"
msgstr "Vytvořit popis…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2900
msgid "Failed to generate description{0}"
msgstr ""

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2915
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>– experimentální</0>"

#: src/components/compose.jsx:2934
msgid "Done"
msgstr "Hotovo"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2970
msgid "Choice {0}"
msgstr "Volba {0}"

#: src/components/compose.jsx:3017
msgid "Multiple choices"
msgstr "Více možností"

#: src/components/compose.jsx:3020
msgid "Duration"
msgstr "Trvání"

#: src/components/compose.jsx:3051
msgid "Remove poll"
msgstr "Odstranit anketu"

#: src/components/compose.jsx:3272
msgid "Search accounts"
msgstr "Hledat účty"

#: src/components/compose.jsx:3326
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "Chyba při načítání účtů"

#: src/components/compose.jsx:3470
msgid "Custom emojis"
msgstr "Vlastní emoji"

#: src/components/compose.jsx:3490
msgid "Search emoji"
msgstr "Hledat emoji"

#: src/components/compose.jsx:3521
msgid "Error loading custom emojis"
msgstr "Chyba při načítání vlastních emoji"

#: src/components/compose.jsx:3532
msgid "Recently used"
msgstr "Nedávno použité"

#: src/components/compose.jsx:3533
msgid "Others"
msgstr "Ostatní"

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3571
msgid "{0} more…"
msgstr "{0} další…"

#: src/components/compose.jsx:3709
msgid "Search GIFs"
msgstr "Hledat GIFy"

#: src/components/compose.jsx:3724
msgid "Powered by GIPHY"
msgstr "Běží na GIPHY"

#: src/components/compose.jsx:3732
msgid "Type to search GIFs"
msgstr "Zadejte pro vyhledávání GIFů"

#: src/components/compose.jsx:3830
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "Zpět"

#: src/components/compose.jsx:3848
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "Další"

#: src/components/compose.jsx:3865
msgid "Error loading GIFs"
msgstr "Chyba při načítání GIFů"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "Neodeslané koncepty"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "Vypadá to, že máte neodeslané koncepty. Pojďme pokračovat tam, kde jste skončili."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "Smazat koncept?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "Chyba při mazání konceptu. Zkuste to prosím znovu."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1595
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "Smazat…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "Chyba při načítání stavu odpovědi!"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "Smazat všechny koncepty?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "Chyba při mazání konceptů! Zkuste to prosím znovu."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "Smazat vše…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "Nebyly nalezeny žádné koncepty."

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "Anketa"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:375
msgid "Media"
msgstr "Média"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "Otevřít v novém okně"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "Přijmout"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "Odmítnout"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "Přijato"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "Odmítnuto"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:335
#: src/pages/search.jsx:368
msgid "Accounts"
msgstr "Účty"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:562
#: src/pages/status.jsx:1426
msgid "Show more…"
msgstr "Zobrazit více…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:567
msgid "The end."
msgstr "Konec."

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "Nic k zobrazení"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "Klávesové zkratky"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "Nápověda pro klávesové zkratky"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "Následující příspěvek"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "Předchozí příspěvek"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "Přeskočit carousel na další příspěvek"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Shift</0>+ <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "Přeskočit na předchozí příspěvek v karuselu"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Shift</0>+ <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "Načíst nové příspěvky"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "Otevře detaily příspěvku"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Enter</0> nebo <1>o </1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "Rozbalit varování obsahu nebo<0/>přepínat rozšířené nebo sbalené vlákno"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "Zavřít příspěvek nebo dialogové okno"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc</0> nebo <1>Backspace</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "Zaměřit se na sloupec v režimu více sloupců"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> do <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr "Zaměřit se na další sloupec v režimu více sloupců"

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr "Zaměřit se na předchozí sloupec v režimu více sloupců"

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "Napsat nový příspěvek"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "Napsat nový příspěvek (nové okno)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Shift</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "Odeslat příspěvek"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:204
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:47
#: src/pages/search.jsx:317
msgid "Search"
msgstr "Hledat"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "Odpovědět (nové okno)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Shift</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "Líbí se mi (oblíbené)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> nebo <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1200
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
#: src/components/status.jsx:2724
msgid "Boost"
msgstr "Boost"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Shift</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
#: src/components/status.jsx:2749
msgid "Bookmark"
msgstr "Oblíbené položky"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "Přepnout režim maskování"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Shift</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "Upravit seznam"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "Seznam nelze upravit."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "Seznam nelze vytvořit."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "Zobrazit odpovědi členům seznamu"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "Zobrazit odpovědi lidem, které sleduji"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "Nezobrazovat odpovědi"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "Skrýt příspěvky z tohoto seznamu na Domovské stránce/Sledované"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "Vytvořit"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "Smazat tento seznam?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "Seznam nelze smazat."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr ""

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "Popis médií"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1306
#: src/components/status.jsx:1315
#: src/components/translation-block.jsx:239
msgid "Translate"
msgstr "Přeložit"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1334
msgid "Speak"
msgstr "Přečíst"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "Otevřít originální média v novém okně"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "Otevřít původní média"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "Pokus o popis obrázku. Počkejte prosím,…"

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "Nepodařilo se popsat obrázek"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "Popište obrázek…"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "Zobrazit příspěvek"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "Citlivá média"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "Filtrováno: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3850
#: src/components/status.jsx:3946
#: src/components/status.jsx:4024
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "Filtrovaný"

#: src/components/media.jsx:479
msgid "Open file"
msgstr ""

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr ""

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "Příspěvek zveřejněn. Podívejte se na něj."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr ""

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "Odpověď odeslána. Podívejte se na ni."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "Příspěvek aktualizován. Podívejte se na něj."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "Menu"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "Znovu načíst stránku a aktualizovat?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "Nová aktualizace k dispozici…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr ""

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "Rekapitulace"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "Zmínky"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "Oznámení"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "Nový"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "Profil"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "Záložky"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "Líbí se mi"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "Sledované hashtagy"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:341
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "Filtry"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "Ztlumení uživatelé"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "Ztlumeni uživatelé…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "Blokovaní uživatelé"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "Blokovaní uživatelé…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "Účty…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:199
#: src/pages/status.jsx:925
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "Přihlásit se"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "Trendy"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "Federovaná"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "Zkratky / Sloupce…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "Nastavení…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "Seznam"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "Všechny seznamy"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "Oznámení"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "Toto oznámení pochází z vašeho dalšího účtu."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "Zobrazit všechna oznámení"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} reagoval na váš příspěvek s {emojiObject}"

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} publikoval příspěvek."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr ""

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, =1 {{account} vás začal sledovat.} few {<0><1>{0}</1> lidé</0> vás začali sledovat.} other {<0><1>{0}</1> lidí</0> vás začalo sledovat.}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} vás požádal o sledování."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr ""

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "Anketa, v níž jste hlasovali nebo byla vytvořena, skončila."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "Anketa, kterou jste vytvořili, skončila."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "Anketa, ve které jste hlasovali, skončila."

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "Příspěvek, na který jste reagovali, byl upraven."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr ""

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} se zaregistroval/a."

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} nahlásil {targetAccount}"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "Ztraceno spojení s <0>{name}</0>."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "Upozornění na moderování"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr "Tvůj {year} #Wrapstodon je tady!"

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "Admin z <0>{from}</0> pozastavil účet <1>{targetName}</1>, což znamená, že už nemůžete přijímat jejich aktualizace ani s nimi komunikovat."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "Admin z <0>{from}</0> zablokoval <1>{targetName}</1>. Počet ovlivněných sledujících: {followersCount}, sledovaných: {followingCount}."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "Zablokovali jste <0>{targetName}</0>. Odstranění sledujících: {followersCount}, následující: {followingCount}."

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "Váš účet obdržel upozornění na moderování."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "Váš účet je zablokován."

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "Některé z vašich příspěvků byly označeny jako citlivé."

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "Některé z vašich příspěvků byly odstraněny."

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "Vaše příspěvky budou od nynějška označeny jako citlivé."

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "Váš účet byl omezen."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "Váš účet byl pozastaven."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[Neznámý typ oznámení: {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1277
#: src/components/status.jsx:1287
msgid "Boosted/Liked by…"
msgstr "Boost/Líbilo se mi…"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "Líbí se…"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "Boostováno…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "Sleduje…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "Další informace <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr "Zobrazit #Wrapstodon"

#: src/components/notification.jsx:801
#: src/components/status.jsx:486
msgid "Read more →"
msgstr "Přečti si více →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "Hlasoval/a"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr ""

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "Skrýt výsledky"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "Hlasovat"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1295
#: src/pages/status.jsx:1318
msgid "Refresh"
msgstr "Obnovit"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "Zobrazit výsledky"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> hlas} few {<1>{1}</1> hlasy} other {<1>{1}</1> hlasů}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, one {<0>{0}</0> volič} few {<1>{1}</1> voliči} other {<1>{1}</1> voličů}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "Skončilo <0/>"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "Ukončeno"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "Konec <0/>"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "Ukončení"

#: src/components/recent-searches.jsx:27
msgid "Cleared recent searches"
msgstr ""

#: src/components/recent-searches.jsx:49
#: src/components/search-form.jsx:328
msgid "Recent searches"
msgstr ""

#: src/components/recent-searches.jsx:59
msgid "Clear all"
msgstr ""

#: src/components/recent-searches.jsx:94
#: src/pages/account-statuses.jsx:334
msgid "Clear"
msgstr "Vyčistit"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0}s"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0}m"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0}h"

#: src/components/report-modal.jsx:30
msgid "Spam"
msgstr "Nevyžádané"

#: src/components/report-modal.jsx:31
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "Škodlivé odkazy, falešné interakce nebo opakované odpovědi"

#: src/components/report-modal.jsx:34
msgid "Illegal"
msgstr "Nelegální"

#: src/components/report-modal.jsx:35
msgid "Violates the law of your or the server's country"
msgstr "Porušuje zákon vaší nebo země serveru"

#: src/components/report-modal.jsx:38
msgid "Server rule violation"
msgstr "Porušení pravidla serveru"

#: src/components/report-modal.jsx:39
msgid "Breaks specific server rules"
msgstr "Porušuje specifická pravidla serveru"

#: src/components/report-modal.jsx:40
msgid "Violation"
msgstr "Porušení"

#: src/components/report-modal.jsx:43
msgid "Other"
msgstr "Ostatní"

#: src/components/report-modal.jsx:44
msgid "Issue doesn't fit other categories"
msgstr "Problém neodpovídá ostatním kategoriím"

#: src/components/report-modal.jsx:111
msgid "Report Post"
msgstr "Nahlásit příspěvek"

#: src/components/report-modal.jsx:111
msgid "Report @{username}"
msgstr "Nahlásit @{username}"

#: src/components/report-modal.jsx:147
msgid "Pending review"
msgstr "Čeká na posouzení"

#: src/components/report-modal.jsx:189
msgid "Post reported"
msgstr "Příspěvek nahlášen"

#: src/components/report-modal.jsx:189
msgid "Profile reported"
msgstr "Profil nahlášen"

#: src/components/report-modal.jsx:197
msgid "Unable to report post"
msgstr "Nelze nahlásit příspěvek"

#: src/components/report-modal.jsx:198
msgid "Unable to report profile"
msgstr "Profil nelze nahlásit"

#: src/components/report-modal.jsx:206
msgid "What's the issue with this post?"
msgstr "Jaký je s tímto příspěvkem problém?"

#: src/components/report-modal.jsx:207
msgid "What's the issue with this profile?"
msgstr "Jaký je s tímto účtem problém?"

#: src/components/report-modal.jsx:276
msgid "Additional info"
msgstr "Další informace"

#: src/components/report-modal.jsx:299
msgid "Forward to <0>{domain}</0>"
msgstr "Přeposlat na <0>{domain}</0>"

#: src/components/report-modal.jsx:309
msgid "Send Report"
msgstr "Odeslat hlášení"

#: src/components/report-modal.jsx:318
msgid "Muted {username}"
msgstr "Ztlumit {username}"

#: src/components/report-modal.jsx:321
msgid "Unable to mute {username}"
msgstr "Nebylo možné ztlumit {username}"

#: src/components/report-modal.jsx:326
msgid "Send Report <0>+ Mute profile</0>"
msgstr "Odeslat hlášení <0>+ ztlumit profil</0>"

#: src/components/report-modal.jsx:337
msgid "Blocked {username}"
msgstr "Zablokován {username}"

#: src/components/report-modal.jsx:340
msgid "Unable to block {username}"
msgstr "Nelze zablokovat {username}"

#: src/components/report-modal.jsx:345
msgid "Send Report <0>+ Block profile</0>"
msgstr "Odeslat hlášení <0>+ Blokovat profil</0>"

#: src/components/search-form.jsx:18
msgid "Posts with <0>{query}</0>"
msgstr "Příspěvky obsahující <0>{query}</0>"

#: src/components/search-form.jsx:26
msgid "Accounts with <0>{query}</0>"
msgstr "Účty s <0>{query}</0>"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:34
msgid "Posts tagged with <0>#{0}</0>"
msgstr "Příspěvky označené <0>#{0}</0>"

#: src/components/search-form.jsx:43
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>‒ účty, hashtagy & příspěvky</0>"

#: src/components/search-form.jsx:135
msgid "Look up <0>{query}</0>"
msgstr "Vyhledat <0>{query}</0>"

#: src/components/search-form.jsx:361
#: src/pages/home.jsx:251
msgid "See all"
msgstr "Zobrazit vše"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "Domů / Sledování"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "Veřejná (Lokální/Federovaná)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "Účet"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "Hashtag"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "ID seznamu"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "Pouze místní"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:203
msgid "Instance"
msgstr "Instance"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "Nepovinné, např. mastodon.social"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "Hledaný výraz"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "Volitelné, pokud není pro více sloupců"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "např. PixelArt (Max 5, oddělený mezerami)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "Pouze média"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "Zkratky"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "beta"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "Zadejte seznam zkratek, které se zobrazí jako:"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "Plovoucí tlačítko"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "Záložka/Panel nabídek"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "Více sloupců"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "Není k dispozici v aktuálním režimu zobrazení"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "Posunout nahoru"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "Dolů"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1557
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "Upravit"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "Přidejte více než jednu zkratku/sloupec, aby to fungovalo."

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "Zatím žádné sloupce. Klikněte na tlačítko Přidat sloupec."

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "Zatím žádné zkratky. Klepněte na tlačítko Přidat zkratku."

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "Nejste si jisti, co přidat?<0/>Zkuste nejdříve přidat <1>Domů / Sledování a oznámení</1>."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "Max {SHORTCUTS_LIMIT} sloupců"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "Maximálně {SHORTCUTS_LIMIT} zkratek"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "Import/export"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "Přidat sloupec…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "Přidat zkratku…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "Specifický seznam je volitelný. Pro režim více sloupců je seznam povinný, jinak se sloupec nezobrazí."

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "Pro režim více sloupců je vyžadován hledaný výraz, jinak nebude sloupec zobrazen."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "Podporováno je více hashtagů. Oddělené mezerami."

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "Upravit zkratky"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "Přidat zkratku"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "Časová osa"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "Seznam"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "Importovat/Exportovat <0>Zástupce</0>"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "Importovat"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "Zde vložte zkratky"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "Stahuji uložené zkratky z instance serveru…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "Zkratky nelze stáhnout"

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "Stáhnout zkratky z instance serveru"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "* Existuje v aktuálních zkratkách"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "Seznam nemusí fungovat, pokud je z jiného účtu."

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "Neplatný formát nastavení"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "Existuje v aktuálních zkratkách?"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr "Budou připojeny pouze zkratky, které v současných zkratkách neexistují."

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "Žádné nové zkratky k importu"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "Zkratky importovány. Překročeno max. {SHORTCUTS_LIMIT}, zbytek tedy není importován."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "Zkratky importovány"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "Importovat & připojit…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "Přepsat aktuální zkratky?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "Importovat zkratky?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "nebo přepsat…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "Importovat…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "Exportovat"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "Zkratky zkopírovány"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "Nelze kopírovat zkratky"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "Nastavení zkratky zkopírováno"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "Nelze kopírovat nastavení zástupce"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "Sdílet"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "Ukládání zkratek na instanci serveru…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "Zkratky uloženy"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "Zkratky nelze uložit"

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "Synchronizujte se serverem instance"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0,plural, one{# znak} few {# znaky} many {# znaků} other{# znaků}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "Nezpracované zkratky JSON"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "Importovat/exportovat nastavení z/do instance serveru (experimentální)"

#: src/components/status.jsx:351
msgid "Unable to format math"
msgstr ""

#: src/components/status.jsx:365
msgid "Math expressions found."
msgstr ""

#. Action to switch from rendered math back to raw (LaTeX) markup
#: src/components/status.jsx:368
msgid "Show markup"
msgstr ""

#. Action to render math expressions from raw (LaTeX) markup
#: src/components/status.jsx:373
msgid "Format math"
msgstr ""

#: src/components/status.jsx:772
msgid "<0/> <1>boosted</1>"
msgstr "<0/> <1>boostnul</1>"

#: src/components/status.jsx:875
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "Omlouváme se, vaše aktuální přihlášená instance nemůže komunikovat s tímto příspěvkem z jiné instance."

#. placeholder {0}: username || acct
#: src/components/status.jsx:1029
msgid "Unliked @{0}'s post"
msgstr "Nelíbí se @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1030
msgid "Liked @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1069
msgid "Unbookmarked @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1070
msgid "Bookmarked @{0}'s post"
msgstr ""

#: src/components/status.jsx:1169
msgid "Some media have no descriptions."
msgstr "Některá média jsou bez popisu."

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1176
msgid "Old post (<0>{0}</0>)"
msgstr "Starý příspěvek (<0>{0}</0>)"

#: src/components/status.jsx:1200
#: src/components/status.jsx:1240
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
msgid "Unboost"
msgstr "Zrušit boostnutí"

#: src/components/status.jsx:1216
#: src/components/status.jsx:2715
msgid "Quote"
msgstr "Citace"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1228
#: src/components/status.jsx:1694
msgid "Unboosted @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1229
#: src/components/status.jsx:1695
msgid "Boosted @{0}'s post"
msgstr ""

#: src/components/status.jsx:1241
msgid "Boost…"
msgstr "Boostnout…"

#: src/components/status.jsx:1253
#: src/components/status.jsx:1988
#: src/components/status.jsx:2736
msgid "Unlike"
msgstr "Nelíbí se mi"

#: src/components/status.jsx:1254
#: src/components/status.jsx:1988
#: src/components/status.jsx:1989
#: src/components/status.jsx:2736
#: src/components/status.jsx:2737
msgid "Like"
msgstr "Líbí se mi"

#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
msgid "Unbookmark"
msgstr "Odebrat záložku"

#: src/components/status.jsx:1346
msgid "Post text copied"
msgstr ""

#: src/components/status.jsx:1349
msgid "Unable to copy post text"
msgstr ""

#: src/components/status.jsx:1355
msgid "Copy post text"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1373
msgid "View post by <0>@{0}</0>"
msgstr "Zobrazit příspěvek od <0>@{0}</0>"

#: src/components/status.jsx:1394
msgid "Show Edit History"
msgstr "Ukázat historii úprav"

#: src/components/status.jsx:1397
msgid "Edited: {editedDateText}"
msgstr "Upraveno: {editedDateText}"

#: src/components/status.jsx:1464
#: src/components/status.jsx:3516
msgid "Embed post"
msgstr "Vložit příspěvek"

#: src/components/status.jsx:1478
msgid "Conversation unmuted"
msgstr "Ztlumení konverzace zrušeno"

#: src/components/status.jsx:1478
msgid "Conversation muted"
msgstr "Konverzace ztlumena"

#: src/components/status.jsx:1484
msgid "Unable to unmute conversation"
msgstr "Nelze zrušit ztlumení konverzace"

#: src/components/status.jsx:1485
msgid "Unable to mute conversation"
msgstr "Nelze ztlumit konverzaci"

#: src/components/status.jsx:1494
msgid "Unmute conversation"
msgstr "Zrušit ztlumení konverzace"

#: src/components/status.jsx:1501
msgid "Mute conversation"
msgstr "Ztlumit konverzaci"

#: src/components/status.jsx:1517
msgid "Post unpinned from profile"
msgstr "Příspěvek odepnut z profilu"

#: src/components/status.jsx:1518
msgid "Post pinned to profile"
msgstr "Příspěvek připnut na profil"

#: src/components/status.jsx:1523
msgid "Unable to unpin post"
msgstr "Nelze odepnout příspěvek"

#: src/components/status.jsx:1523
msgid "Unable to pin post"
msgstr "Příspěvek nelze připnout"

#: src/components/status.jsx:1532
msgid "Unpin from profile"
msgstr "Odepnout z profilu"

#: src/components/status.jsx:1539
msgid "Pin to profile"
msgstr "Připnout na profil"

#: src/components/status.jsx:1568
msgid "Delete this post?"
msgstr "Smazat tento příspěvek?"

#: src/components/status.jsx:1584
msgid "Post deleted"
msgstr "Příspěvek odstraněn"

#: src/components/status.jsx:1587
msgid "Unable to delete post"
msgstr "Příspěvek nelze odstranit"

#: src/components/status.jsx:1615
msgid "Report post…"
msgstr "Nahlásit příspěvek…"

#: src/components/status.jsx:1989
#: src/components/status.jsx:2025
#: src/components/status.jsx:2737
msgid "Liked"
msgstr "Líbí se"

#: src/components/status.jsx:2022
#: src/components/status.jsx:2724
msgid "Boosted"
msgstr "Boosty"

#: src/components/status.jsx:2032
#: src/components/status.jsx:2749
msgid "Bookmarked"
msgstr "Záložky"

#: src/components/status.jsx:2036
msgid "Pinned"
msgstr "Připnuto"

#: src/components/status.jsx:2082
#: src/components/status.jsx:2561
msgid "Deleted"
msgstr "Smazat"

#: src/components/status.jsx:2123
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {# odpověď} few {# odpovědi} many {# odpovědi} other {# odpovědi}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2213
msgid "Thread{0}"
msgstr "Vlákna{0}"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
#: src/components/status.jsx:2457
msgid "Show less"
msgstr "Zobrazit méně"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
msgid "Show content"
msgstr "Zobrazit obsah"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2453
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Filtrováno: {0}"

#: src/components/status.jsx:2457
msgid "Show media"
msgstr "Zobrazit média"

#: src/components/status.jsx:2597
msgid "Edited"
msgstr "Upraveno"

#: src/components/status.jsx:2674
msgid "Comments"
msgstr "Komentáře"

#. More from [Author]
#: src/components/status.jsx:2974
msgid "More from <0/>"
msgstr "Více od <0/>"

#: src/components/status.jsx:3276
msgid "Edit History"
msgstr "Historie úprav"

#: src/components/status.jsx:3280
msgid "Failed to load history"
msgstr "Nepodařilo se načíst historii"

#: src/components/status.jsx:3285
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "Načítání…"

#: src/components/status.jsx:3521
msgid "HTML Code"
msgstr "HTML kód"

#: src/components/status.jsx:3538
msgid "HTML code copied"
msgstr "HTML kód zkopírován"

#: src/components/status.jsx:3541
msgid "Unable to copy HTML code"
msgstr "Nelze kopírovat HTML kód"

#: src/components/status.jsx:3553
msgid "Media attachments:"
msgstr "Přílohy médií:"

#: src/components/status.jsx:3575
msgid "Account Emojis:"
msgstr "Emoji účtu:"

#: src/components/status.jsx:3606
#: src/components/status.jsx:3651
msgid "static URL"
msgstr "statická URL"

#: src/components/status.jsx:3620
msgid "Emojis:"
msgstr "Emoji:"

#: src/components/status.jsx:3665
msgid "Notes:"
msgstr "Poznámky:"

#: src/components/status.jsx:3669
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "Toto je statické, nestylizované a bez skriptů. Možná budete muset použít vlastní styly a podle potřeby upravovat."

#: src/components/status.jsx:3675
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "Hlasování v anketách není interaktivní, místo toho se zobrazí seznam s počty hlasů."

#: src/components/status.jsx:3680
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "Mediální přílohy mohou být obrázky, videa, audio nebo jakékoli typy souborů."

#: src/components/status.jsx:3686
msgid "Post could be edited or deleted later."
msgstr "Příspěvek může být později upraven nebo odstraněn."

#: src/components/status.jsx:3692
msgid "Preview"
msgstr "Náhled"

#: src/components/status.jsx:3701
msgid "Note: This preview is lightly styled."
msgstr "Poznámka: Tento náhled je lehce stylizovaný."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3954
msgid "<0/> <1/> boosted"
msgstr "<0/><1/> boostnuto"

#: src/components/status.jsx:4056
msgid "Post hidden by your filters"
msgstr ""

#: src/components/status.jsx:4057
msgid "Post pending"
msgstr ""

#: src/components/status.jsx:4058
#: src/components/status.jsx:4059
#: src/components/status.jsx:4060
#: src/components/status.jsx:4061
msgid "Post unavailable"
msgstr ""

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "Nové příspěvky"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1078
#: src/pages/status.jsx:1455
msgid "Try again"
msgstr "Zkuste to znovu"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, one {# Boostnutí} other {# Boostnutí}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr "Připnuté příspěvky"

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "Vlákno"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>Filtrované</0>: <1>{0}</1>"

#: src/components/translation-block.jsx:196
msgid "Auto-translated from {sourceLangText}"
msgstr "Automaticky přeloženo z {sourceLangText}"

#: src/components/translation-block.jsx:234
msgid "Translating…"
msgstr "Překlad…"

#: src/components/translation-block.jsx:237
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "Přeložit z {sourceLangText} (detekováno automaticky)"

#: src/components/translation-block.jsx:238
msgid "Translate from {sourceLangText}"
msgstr "Přeložit z {sourceLangText}"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:266
msgid "Auto ({0})"
msgstr "Auto ({0})"

#: src/components/translation-block.jsx:279
msgid "Failed to translate"
msgstr "Překlad se nezdařil"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr ""

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "Odpověď @{0}"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "Nyní můžete tuto stránku zavřít."

#: src/compose.jsx:71
msgid "Close window"
msgstr "Zavřít okno"

#: src/compose.jsx:87
msgid "Login required."
msgstr "Vyžadováno přihlášení."

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:280
msgid "Go home"
msgstr "Domů"

#: src/pages/account-statuses.jsx:241
msgid "Account posts"
msgstr "Příspěvky účtu"

#: src/pages/account-statuses.jsx:248
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ Odpovědi)"

#: src/pages/account-statuses.jsx:250
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- Boosty)"

#: src/pages/account-statuses.jsx:252
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:254
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (Média)"

#: src/pages/account-statuses.jsx:260
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:331
msgid "Clear filters"
msgstr "Vymazat filtry"

#: src/pages/account-statuses.jsx:348
msgid "Showing post with replies"
msgstr "Zobrazení příspěvku s odpovědmi"

#: src/pages/account-statuses.jsx:353
msgid "+ Replies"
msgstr "+ Odpovědi"

#: src/pages/account-statuses.jsx:359
msgid "Showing posts without boosts"
msgstr "Zobrazení příspěvků bez boostů"

#: src/pages/account-statuses.jsx:364
msgid "- Boosts"
msgstr "- Boosty"

#: src/pages/account-statuses.jsx:370
msgid "Showing posts with media"
msgstr "Zobrazování příspěvků s médii"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:387
msgid "Showing posts tagged with #{0}"
msgstr "Zobrazuji příspěvky označené štítkem #{0}"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:426
msgid "Showing posts in {0}"
msgstr ""

#: src/pages/account-statuses.jsx:516
msgid "Nothing to see here yet."
msgstr "Zatím zde není co vidět."

#: src/pages/account-statuses.jsx:517
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "Nelze načíst příspěvky"

#: src/pages/account-statuses.jsx:558
#: src/pages/account-statuses.jsx:588
msgid "Unable to fetch account info"
msgstr "Nelze získat informace o účtu"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:565
msgid "Switch to account's instance {0}"
msgstr "Přepnout na instance účtu {0}"

#: src/pages/account-statuses.jsx:595
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "Přepněte na mou instanci (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:668
msgid "Month"
msgstr "Měsíc"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "Současný"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "Výchozí"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "Přepněte na tento účet"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "Přepnout v novém panelu/okně"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "Zobrazit profil…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "Nastavit jako výchozí"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr "Odhlásit se <0>@{0}</0>?"

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "Odhlásit se…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr ""

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "Přidejte existující účet"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "Poznámka: <0>Výchozí</0> účet bude vždy použit pro první načtení. Přepnuté účty budou přetrvávat i během relace."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "Zatím nemáte žádné záložky. Přidejte nějakou!"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "Nelze načíst záložky."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "poslední hodina"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "poslední 2 hodiny"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "poslední 3 hodiny"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "poslední 4 hodiny"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "posledních 5 hodin"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "posledních 6 hodin"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "posledních 7 hodin"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "posledních 8 hodin"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "posledních 9 hodin"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "posledních 10 hodin"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "posledních 11 hodin"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "posledních 12 hodin"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "starší než 12 hodin"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "Sledované štítky"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "Skupiny"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "Zobrazuji {selectedFilterCategory, select, all {všechny příspěvky} original {původní příspěvky} replies {odpovědi} boosts {bosty} followedTags {sledované štítky} groups {skupiny} filtered {filtrované příspěvky}}, {sortBy, select, createdAt {{sortOrder, select, asc {nejstarší} desc {nejnovější}}} reblogsCount {{sortOrder, select, asc {nejméně bostů} desc {nejvíce bostů}}} favouritesCount {{sortOrder, select, asc {nejméně lajků} desc {nejvíce lajků}}} repliesCount {{sortOrder, select, asc {nejméně odpovědí} desc {nejvíce odpovědí}}} density {{sortOrder, select, asc {nejméně husté} desc {nejhustší}}}} nejprve{groupBy, select, account {, seskupeno podle autorů} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "Catch-up <0>beta</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "Nápověda"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "Co je tohle?"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "Catch-up je samostatná časová osa pro sledované uživatele, která nabízí přehledný pohled na příspěvky s jednoduchým rozhraním inspirovaným e-mailem, což umožňuje snadné třídění a filtrování obsahu."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Náhled rozhraní Catch-up"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "Rychlá rekapitulace"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "Rekapitulace příspěvků od těch, které sledujete."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "Zobrazit všechny příspěvky od…"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "až do maxima"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "Catch up"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "Překrývá se s vaší poslední rekapitulací"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr ""

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "Poznámka: Vaše instance může zobrazit maximálně 800 příspěvků na domovské časové ose, bez ohledu na časové rozmezí. Může to být méně nebo více."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "Předchozí…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, one {# příspěvek} few {# příspěvky} many {# příspěvků} other {# příspěvků}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "Odstranit tuto rekapitulaci?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr ""

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr ""

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "Poznámka: Budou uloženy pouze maximálně 3. Zbytek bude automaticky odstraněn."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "Načítání příspěvků…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "To může chvíli trvat."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "Obnovit filtry"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "Nejlepší odkazy"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "Sdílel/a {0}"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:330
msgid "All"
msgstr "Vše"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, one {# autor} few {# autoři} many {# autorů} other {# autorů}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "Seřadit"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Datum"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Hustota"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr ""

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "Autoři"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "Žádný"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "Zobrazit všechny autory"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "Není třeba číst všechno."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "To je vše."

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "Zpět na začátek"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "Odkazy sdílené lidmi, které sledujete, seřazené podle počtu sdílení, boostů a lajků."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "Třídit: Hustota"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "Příspěvky jsou tříděny podle informační hustoty nebo hloubky. kratší příspěvky jsou \"lehčí\" zatímco delší příspěvky jsou \"těžší\". Příspěvky s fotkami jsou \"těžší\" než příspěvky bez fotografií."

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "Skupina: Autoři"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "Příspěvky jsou seskupeny podle autorů a seřazeny podle počtu příspěvků na autora."

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "Další autor"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "Předchozí autor"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "Přejít na začátek"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "Neudělili jste žádné \"líbí se mi\". Označte něco, co se vám líbí!"

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "Nepodařilo se načíst lajky."

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "Domovská časová osa a seznamy"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "Veřejná časová osa"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "Konverzace"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "Profily"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "Nikdy"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "Nový filtr"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0, plural, one {# filtr} few {# filtry} many {# filtrů} other {# filtrů}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "Nepodařilo se načíst filtry."

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "Zatím žádné filtry."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "Přidat filtr"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "Upravit filtr"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "Nepodařilo se upravit filtr"

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "Nelze vytvořit filtr"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "Název"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "Celé slovo"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "Žádná klíčová slova. Přidejte jedno."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "Přidat klíčové slovo"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, one {# klíčové slovo} few {# klíčová slova} many {# klíčových slov} other {# klíčových slov}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "Filtrovat od…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* Zatím není implementováno"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "Stav: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "Změnit vypršení platnosti"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "Vypršení platnosti"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "Filtrovaný příspěvek bude…"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr ""

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "minimalizovat"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "skrytý"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "Smazat tento filtr?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "Nepodařilo se odstranit filtr."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "Vypršelo"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "Vyprší <0/>"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "Nikdy nevyprší"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr ""

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "Nepodařilo se načíst sledované hashtagy."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "Zatím nesledujete žádné hashtagy."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "Není zde nic k zobrazení."

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "Nelze načíst příspěvky."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{hashtagTitle} (pouze média) na {instance}"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{hashtagTitle} na {instance}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (pouze média)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "S tímto štítkem zatím nikdo nic nepřidal."

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "Nepodařilo se načíst příspěvky s tímto štítkem"

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "Přestat sledovat #{hashtag}?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "#{hashtag} již není sledován"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "Sledován #{hashtag}"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "Sleduji…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "Odebráno z profilu"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "Nepodařilo se odebrat z profilu"

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "Zmíněno v profilu"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr "{TOTAL_TAGS_LIMIT, plural, one {} few {#} many {#}other {#}}"

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "Přidat hashtag"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "Odstranit hashtag"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr ""

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "Tato zkratka již existuje"

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr ""

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "Přidat do zkratek"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "Zadejte novou instanci, např. „mastodon.social“"

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "Neplatná instance"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "Přejít na jinou instanci…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "Přejít na moji instanci (<0>{currentInstance}</0>)"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "Nelze načíst oznámení."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<0>Nové</0> <1>Žádosti o sledování</1>"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "Připojování…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "Nelze najít URL adresu"

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "Zatím nic."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "Spravovat členy"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "Odstranit <0>@{0}</0> ze seznamu?"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr ""

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr ""

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "Nemáte žádné seznamy."

#: src/pages/login.jsx:122
#: src/pages/login.jsx:132
msgid "Failed to register application"
msgstr "Nepodařilo se zaregistrovat aplikaci"

#: src/pages/login.jsx:218
msgid "instance domain"
msgstr ""

#: src/pages/login.jsx:242
msgid "e.g. “mastodon.social”"
msgstr "např. “mastodon.social”"

#: src/pages/login.jsx:253
msgid "Failed to log in. Please try again or try another instance."
msgstr ""

#: src/pages/login.jsx:265
msgid "Continue with {selectedInstanceText}"
msgstr ""

#: src/pages/login.jsx:266
msgid "Continue"
msgstr "Pokračovat"

#: src/pages/login.jsx:274
msgid "Don't have an account? Create one!"
msgstr "Nemáte účet? Vytvořte si účet!"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "Soukromé zmínky"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "Soukromý"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "Nikdo vás nezmínil :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "Nepodařilo se načíst odpovědi."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "Nesledujete"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "Kdo tě nesleduje"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "S novým účtem"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr ""

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr ""

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "Nastavení oznámení"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "Nová oznámení"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr ""

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr ""

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr ""

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr ""

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "Pouze @zmínky"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "Dnes"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "Přečetli jste všechny komentáře."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "Včera"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "Nelze načíst oznámení"

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "Nastavení oznámení aktualizováno"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "Filtrovat oznámení od lidí:"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "Filtr"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "Ignorovat"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "Aktualizováno <0>{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr ""

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr ""

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr ""

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr ""

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "Povolit"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr ""

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr ""

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "Odmítnout"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr ""

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "Místní časová osa ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "Federovaná časová osa ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "Místní časová osa"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "Federovaná časová osa"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr ""

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr ""

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr ""

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr ""

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr ""

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr ""

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr ""

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr ""

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr ""

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr ""

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr ""

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr ""

#: src/pages/search.jsx:51
msgid "Search: {q} (Posts)"
msgstr ""

#: src/pages/search.jsx:54
msgid "Search: {q} (Accounts)"
msgstr ""

#: src/pages/search.jsx:57
msgid "Search: {q} (Hashtags)"
msgstr ""

#: src/pages/search.jsx:60
msgid "Search: {q}"
msgstr ""

#: src/pages/search.jsx:340
#: src/pages/search.jsx:422
msgid "Hashtags"
msgstr "Hashtagy"

#: src/pages/search.jsx:372
#: src/pages/search.jsx:426
#: src/pages/search.jsx:496
msgid "See more"
msgstr ""

#: src/pages/search.jsx:398
msgid "See more accounts"
msgstr "Zobrazit další účty"

#: src/pages/search.jsx:412
msgid "No accounts found."
msgstr ""

#: src/pages/search.jsx:468
msgid "See more hashtags"
msgstr ""

#: src/pages/search.jsx:482
msgid "No hashtags found."
msgstr ""

#: src/pages/search.jsx:526
msgid "See more posts"
msgstr "Zobrazit více příspěvků"

#: src/pages/search.jsx:540
msgid "No posts found."
msgstr ""

#: src/pages/search.jsx:585
msgid "Enter your search term or paste a URL above to get started."
msgstr ""

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "Nastavení"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "Vzhled"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "Světlý"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "Tmavý"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr ""

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "Velikost textu"

#. Preview of one character, in largest size
#. Preview of one character, in smallest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr ""

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr ""

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr ""

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr ""

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "Výchozí viditelnost"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "Synchronizováno"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr ""

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr ""

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr ""

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr ""

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr ""

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr ""

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr "Přeložit do "

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr ""

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr ""

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr ""

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr ""

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr ""

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr ""

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr ""

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "Generátor popisu obrázků"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr ""

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr ""

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr ""

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr ""

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr ""

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr ""

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr ""

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr ""

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr ""

#: src/pages/settings.jsx:710
msgid "About"
msgstr ""

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr ""

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr ""

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Přispět"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr ""

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "Ochrana osobních údajů"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr ""

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>Verze:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr ""

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr ""

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr ""

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr ""

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "Push notifikace (beta)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr ""

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr ""

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr ""

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr ""

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr ""

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr ""

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "Ankety"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr ""

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr ""

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr ""

#. js-lingui-explicit-id
#: src/pages/status.jsx:658
#: src/pages/status.jsx:1221
msgid "post.title"
msgstr ""

#: src/pages/status.jsx:912
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr ""

#: src/pages/status.jsx:932
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr ""

#: src/pages/status.jsx:960
msgid "Error: {e}"
msgstr "Chyba: {e}"

#: src/pages/status.jsx:967
msgid "Switch to my instance to enable interactions"
msgstr ""

#: src/pages/status.jsx:1069
msgid "Unable to load replies."
msgstr "Nepodařilo se načíst odpovědi."

#: src/pages/status.jsx:1181
msgid "Back"
msgstr "Zpět"

#: src/pages/status.jsx:1212
msgid "Go to main post"
msgstr ""

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1235
msgid "{0} posts above ‒ Go to top"
msgstr ""

#: src/pages/status.jsx:1282
#: src/pages/status.jsx:1345
msgid "Switch to Side Peek view"
msgstr ""

#: src/pages/status.jsx:1346
msgid "Switch to Full view"
msgstr ""

#: src/pages/status.jsx:1364
msgid "Show all sensitive content"
msgstr ""

#: src/pages/status.jsx:1369
msgid "Experimental"
msgstr ""

#: src/pages/status.jsx:1378
msgid "Unable to switch"
msgstr ""

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1385
msgid "Switch to post's instance ({0})"
msgstr ""

#: src/pages/status.jsx:1388
msgid "Switch to post's instance"
msgstr ""

#: src/pages/status.jsx:1446
msgid "Unable to load post"
msgstr ""

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1582
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr ""

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1600
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr ""

#: src/pages/status.jsx:1622
msgid "View post with its replies"
msgstr ""

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr ""

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr ""

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr ""

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr ""

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr ""

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr ""

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr ""

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr ""

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Přihlásit účtem Mastodon"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "Vytvořit účet"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "Připojte svůj existující Mastodon/Fediverse účet.<0/>Vaše přihlašovací údaje nejsou uloženy na tomto serveru."

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr ""

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr ""

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr ""

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr ""

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr ""

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr ""

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr ""

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr ""

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr ""

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr ""

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr ""

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr ""

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr ""

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr ""

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr ""

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr ""

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr ""

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr ""

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr ""

