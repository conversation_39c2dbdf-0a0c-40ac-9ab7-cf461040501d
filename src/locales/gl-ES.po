msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: gl\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-08-05 06:39\n"
"Last-Translator: \n"
"Language-Team: Galician\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: gl\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:141
msgid "Locked"
msgstr "Bloqueada"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:163
msgid "Posts: {0}"
msgstr "Publicacións: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:168
msgid "Last posted: {0}"
msgstr "Última publicación: {0}"

#: src/components/account-block.jsx:183
#: src/components/account-info.jsx:705
msgid "Automated"
msgstr "Automatizada"

#: src/components/account-block.jsx:190
#: src/components/account-info.jsx:710
#: src/components/status.jsx:748
msgid "Group"
msgstr "Grupo"

#: src/components/account-block.jsx:200
msgid "Mutual"
msgstr "Recíproco"

#: src/components/account-block.jsx:204
#: src/components/account-info.jsx:1907
msgid "Requested"
msgstr "Solicitado"

#: src/components/account-block.jsx:208
#: src/components/account-info.jsx:1898
msgid "Following"
msgstr "Seguindo"

#: src/components/account-block.jsx:212
#: src/components/account-info.jsx:1212
msgid "Follows you"
msgstr "Séguete"

#: src/components/account-block.jsx:220
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, one {# seguidora} other {# seguidoras}}"

#: src/components/account-block.jsx:229
#: src/components/account-info.jsx:753
msgid "Verified"
msgstr "Verificada"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:244
#: src/components/account-info.jsx:892
msgid "Joined <0>{0}</0>"
msgstr "Creada <0>{0}</0>"

#: src/components/account-info.jsx:64
msgid "Forever"
msgstr "Permanente"

#: src/components/account-info.jsx:398
msgid "Unable to load account."
msgstr "Non se puido cargar a conta."

#: src/components/account-info.jsx:413
msgid "Go to account page"
msgstr "Ir á páxina da conta"

#: src/components/account-info.jsx:442
#: src/components/account-info.jsx:775
msgid "Followers"
msgstr "Seguidoras"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:446
#: src/components/account-info.jsx:830
msgid "following.stats"
msgstr "Seguimentos"

#: src/components/account-info.jsx:449
#: src/pages/account-statuses.jsx:495
#: src/pages/search.jsx:345
#: src/pages/search.jsx:492
msgid "Posts"
msgstr "Publicacións"

#: src/components/account-info.jsx:457
#: src/components/account-info.jsx:1268
#: src/components/compose.jsx:2819
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1998
#: src/components/status.jsx:2015
#: src/components/status.jsx:2140
#: src/components/status.jsx:2769
#: src/components/status.jsx:2772
#: src/pages/account-statuses.jsx:539
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1306
#: src/pages/trending.jsx:474
msgid "More"
msgstr "Máis"

#: src/components/account-info.jsx:469
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> informou de que agora a súa conta é:"

#: src/components/account-info.jsx:614
#: src/components/account-info.jsx:1496
msgid "Handle copied"
msgstr "Copiouse o identificador"

#: src/components/account-info.jsx:617
#: src/components/account-info.jsx:1499
msgid "Unable to copy handle"
msgstr "Non se puido copiar o identificador"

#: src/components/account-info.jsx:623
#: src/components/account-info.jsx:1505
msgid "Copy handle"
msgstr "Copiar identificador"

#: src/components/account-info.jsx:629
msgid "Go to original profile page"
msgstr "Ir á páxina orixinal do perfil"

#: src/components/account-info.jsx:647
msgid "View profile image"
msgstr "Ver imaxe do perfil"

#: src/components/account-info.jsx:665
msgid "View profile header"
msgstr "Ver cabeceira do perfil"

#: src/components/account-info.jsx:681
#: src/components/account-info.jsx:1806
#: src/components/account-info.jsx:2332
msgid "Edit profile"
msgstr "Editar perfil"

#: src/components/account-info.jsx:700
msgid "In Memoriam"
msgstr "Lembranzas"

#: src/components/account-info.jsx:782
#: src/components/account-info.jsx:840
msgid "This user has chosen to not make this information available."
msgstr "A usuaria decidiu non ofrecer esta información."

#. placeholder {0}: shortenNumber(followersCount)
#. placeholder {1}: shortenNumber(followersCount)
#: src/components/account-info.jsx:802
msgid "{followersCount, plural, one {<0>{0}</0> Follower} other {<1>{1}</1> Followers}}"
msgstr "{followersCount, plural, one {<0>{0}</0> Seguidora} other {<1>{1}</1> Seguidoras}}"

#. placeholder {0}: shortenNumber(followingCount)
#: src/components/account-info.jsx:846
msgid "{followingCount, plural, other {<0>{0}</0> Following}}"
msgstr "{followingCount, plural, one {}other {<0>{0}</0> Seguimentos}}"

#. placeholder {0}: shortenNumber(statusesCount)
#. placeholder {1}: shortenNumber(statusesCount)
#: src/components/account-info.jsx:870
msgid "{statusesCount, plural, one {<0>{0}</0> Post} other {<1>{1}</1> Posts}}"
msgstr "{statusesCount, plural, one {<0>{0}</0> Publicación} other {<1>{1}</1> Publicacións}}"

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:917
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0} publicacións orixinais, {1} respostas, {2} promocións"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:933
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural, one {{1, plural, one {A última publicación desde onte} other {A última publicación desde fai {2} días}}} other {{3, plural, one {As úlltimas {4} publicacións desde onte} other {As últimas {5} publicacións dos últimos {6} días}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:949
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {Última publicación no último ano(s)} other {Últimas {1} publicacións no último ano(s)}}"

#: src/components/account-info.jsx:974
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "Orixinal"

#: src/components/account-info.jsx:978
#: src/components/status.jsx:2553
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1025
#: src/pages/status.jsx:1652
msgid "Replies"
msgstr "Respostas"

#: src/components/account-info.jsx:982
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "Promocións"

#: src/components/account-info.jsx:988
msgid "Post stats unavailable."
msgstr "Estatísticas non dispoñibles."

#: src/components/account-info.jsx:1019
msgid "View post stats"
msgstr "Ver estatísticas de publicación"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1216
msgid "Last post: <0>{0}</0>"
msgstr "Última publicación: <0>{0}</0>"

#: src/components/account-info.jsx:1230
msgid "Muted"
msgstr "Acalada"

#: src/components/account-info.jsx:1235
msgid "Blocked"
msgstr "Bloqueada"

#: src/components/account-info.jsx:1244
msgid "Private note"
msgstr "Nota privada"

#: src/components/account-info.jsx:1301
msgid "Mention <0>@{username}</0>"
msgstr "Mencionar a <0>@{username}</0>"

#: src/components/account-info.jsx:1313
msgid "Translate bio"
msgstr "Traducir bio"

#: src/components/account-info.jsx:1324
msgid "Edit private note"
msgstr "Editar nota privada"

#: src/components/account-info.jsx:1324
msgid "Add private note"
msgstr "Engadir nota privada"

#: src/components/account-info.jsx:1344
msgid "Notifications enabled for @{username}'s posts."
msgstr "Activadas as notificacións para as publicacións de @{username}."

#: src/components/account-info.jsx:1345
msgid " Notifications disabled for @{username}'s posts."
msgstr " Desactivadas as notificacións para as publicacións de @{username}."

#: src/components/account-info.jsx:1357
msgid "Disable notifications"
msgstr "Desactivar notificacións"

#: src/components/account-info.jsx:1358
msgid "Enable notifications"
msgstr "Activar notificacións"

#: src/components/account-info.jsx:1375
msgid "Boosts from @{username} enabled."
msgstr "Activadas as promocións de @{username}."

#: src/components/account-info.jsx:1376
msgid "Boosts from @{username} disabled."
msgstr "Desactivadas as promocións de @{username}."

#: src/components/account-info.jsx:1387
msgid "Disable boosts"
msgstr "Desactivar promocións"

#: src/components/account-info.jsx:1387
msgid "Enable boosts"
msgstr "Activar promocións"

#: src/components/account-info.jsx:1406
msgid "@{username} is no longer featured on your profile."
msgstr "@{username} xa non esta destacado no teu perfil."

#: src/components/account-info.jsx:1416
msgid "@{username} is now featured on your profile."
msgstr "@{username} agora está destacado no teu perfil."

#: src/components/account-info.jsx:1424
msgid "Unable to unfeature @{username} on your profile."
msgstr "Non se puido retirar a @{username} do teu perfil"

#: src/components/account-info.jsx:1428
msgid "Unable to feature @{username} on your profile."
msgstr "Non se puido destacar a @{username} no teu perfil."

#: src/components/account-info.jsx:1437
msgid "Don't feature on profile"
msgstr "Non mostrar no perfil"

#: src/components/account-info.jsx:1438
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "Destacar no perfil"

#: src/components/account-info.jsx:1447
msgid "Show featured profiles"
msgstr "Mostrar perfís destacados"

#: src/components/account-info.jsx:1462
#: src/components/account-info.jsx:1472
#: src/components/account-info.jsx:2078
msgid "Add/Remove from Lists"
msgstr "Engadir/Retirar das Listas"

#: src/components/account-info.jsx:1522
#: src/components/status.jsx:1420
msgid "Link copied"
msgstr "Copiouse a ligazón"

#: src/components/account-info.jsx:1525
#: src/components/status.jsx:1423
msgid "Unable to copy link"
msgstr "Non se puido copiar a ligazón"

#: src/components/account-info.jsx:1531
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1429
#: src/components/status.jsx:3547
msgid "Copy"
msgstr "Copiar"

#: src/components/account-info.jsx:1546
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1445
msgid "Sharing doesn't seem to work."
msgstr "Semella que non se pode compartir."

#: src/components/account-info.jsx:1552
#: src/components/status.jsx:1451
msgid "Share…"
msgstr "Compartir…"

#: src/components/account-info.jsx:1572
msgid "Unmuted @{username}"
msgstr "Reactivouse a @{username}"

#: src/components/account-info.jsx:1584
msgid "Unmute <0>@{username}</0>"
msgstr "Reactivar a <0>@{username}</0>"

#: src/components/account-info.jsx:1600
msgid "Mute <0>@{username}</0>…"
msgstr "Acalar a <0>@{username}</0>…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1632
msgid "Muted @{username} for {0}"
msgstr "Silenciaches a @{username} durante {0}"

#: src/components/account-info.jsx:1644
msgid "Unable to mute @{username}"
msgstr "Non se puido silenciar a @{username}"

#: src/components/account-info.jsx:1665
msgid "Remove <0>@{username}</0> from followers?"
msgstr "Quitar a <0>@{username}</0> das túas seguidoras?"

#: src/components/account-info.jsx:1685
msgid "@{username} removed from followers"
msgstr "Retirouse a @{username} das seguidoras"

#: src/components/account-info.jsx:1697
msgid "Remove follower…"
msgstr "Retirar seguidora…"

#: src/components/account-info.jsx:1708
msgid "Block <0>@{username}</0>?"
msgstr "Bloquear a <0>@{username}</0>?"

#: src/components/account-info.jsx:1732
msgid "Unblocked @{username}"
msgstr "Desbloqueouse a @{username}"

#: src/components/account-info.jsx:1740
msgid "Blocked @{username}"
msgstr "Bloqueouse a @{username}"

#: src/components/account-info.jsx:1748
msgid "Unable to unblock @{username}"
msgstr "Non se puido desbloquear a @{username}"

#: src/components/account-info.jsx:1750
msgid "Unable to block @{username}"
msgstr "Non se puido bloquear a @{username}"

#: src/components/account-info.jsx:1760
msgid "Unblock <0>@{username}</0>"
msgstr "Desbloquear a <0>@{username}</0>"

#: src/components/account-info.jsx:1769
msgid "Block <0>@{username}</0>…"
msgstr "Bloquear a <0>@{username}</0>…"

#: src/components/account-info.jsx:1786
msgid "Report <0>@{username}</0>…"
msgstr "Denunciar a <0>@{username}</0>…"

#: src/components/account-info.jsx:1842
msgid "Withdraw follow request?"
msgstr "Retirar solicitude de seguimento?"

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1843
msgid "Unfollow @{0}?"
msgstr "Deixar de seguir a @{0}?"

#: src/components/account-info.jsx:1901
msgid "Unfollow…"
msgstr "Deixar de seguir…"

#: src/components/account-info.jsx:1910
msgid "Withdraw…"
msgstr "Retirar…"

#: src/components/account-info.jsx:1917
#: src/components/account-info.jsx:1921
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "Seguir"

#: src/components/account-info.jsx:2018
#: src/components/account-info.jsx:2073
#: src/components/account-info.jsx:2207
#: src/components/account-info.jsx:2327
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:892
#: src/components/compose.jsx:2775
#: src/components/compose.jsx:3255
#: src/components/compose.jsx:3464
#: src/components/compose.jsx:3694
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:118
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3271
#: src/components/status.jsx:3511
#: src/components/status.jsx:4020
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1393
msgid "Close"
msgstr "Pechar"

#: src/components/account-info.jsx:2023
msgid "Translated Bio"
msgstr "Traduciuse a Bio"

#: src/components/account-info.jsx:2118
msgid "Unable to remove from list."
msgstr "Non se puido retirar da lista."

#: src/components/account-info.jsx:2119
msgid "Unable to add to list."
msgstr "Non se puido engadir á lista."

#: src/components/account-info.jsx:2138
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "Non se puideron cargar as listas."

#: src/components/account-info.jsx:2142
msgid "No lists."
msgstr "Sen listas."

#: src/components/account-info.jsx:2153
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "Nova lista"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2212
msgid "Private note about <0>@{0}</0>"
msgstr "Nota privada sobre <0>@{0}</0>"

#: src/components/account-info.jsx:2242
msgid "Unable to update private note."
msgstr "Non se puido actualizar a nota privada."

#: src/components/account-info.jsx:2265
#: src/components/account-info.jsx:2563
msgid "Cancel"
msgstr "Desbotar"

#: src/components/account-info.jsx:2270
msgid "Save & close"
msgstr "Gardar e pechar"

#: src/components/account-info.jsx:2387
msgid "Unable to update profile."
msgstr "Non se puido actualizar o perfil."

#: src/components/account-info.jsx:2394
msgid "Header picture"
msgstr "Imaxe de cabeceira"

#: src/components/account-info.jsx:2446
msgid "Profile picture"
msgstr "Imaxe do perfil"

#: src/components/account-info.jsx:2498
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "Nome"

#: src/components/account-info.jsx:2511
msgid "Bio"
msgstr "Biografía"

#: src/components/account-info.jsx:2524
msgid "Extra fields"
msgstr "Campos extra"

#: src/components/account-info.jsx:2530
msgid "Label"
msgstr "Etiqueta"

#: src/components/account-info.jsx:2533
msgid "Content"
msgstr "Contido"

#: src/components/account-info.jsx:2566
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "Gardar"

#: src/components/account-info.jsx:2620
msgid "username"
msgstr "nome de usuaria"

#: src/components/account-info.jsx:2624
msgid "server domain name"
msgstr "dominio do servidor"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2690
msgid "Profiles featured by @{0}"
msgstr "Perfís destacados por @{0}"

#: src/components/account-info.jsx:2716
msgid "No featured profiles."
msgstr "Sen perfís destacados"

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "Desactivada a capa"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "Capa activada"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "Inicio"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "Escribir"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr "Publicacións programadas"

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr "Engadir ao fío"

#: src/components/compose.jsx:212
msgid "Take photo or video"
msgstr "Facer foto ou vídeo"

#: src/components/compose.jsx:213
msgid "Add media"
msgstr "Engadir multimedia"

#: src/components/compose.jsx:214
msgid "Add custom emoji"
msgstr "Engadir emoji persoal"

#: src/components/compose.jsx:215
msgid "Add GIF"
msgstr "Engadir GIF"

#: src/components/compose.jsx:216
msgid "Add poll"
msgstr "Engadir enquisa"

#: src/components/compose.jsx:217
msgid "Schedule post"
msgstr "Programar publicación"

#: src/components/compose.jsx:417
msgid "You have unsaved changes. Discard this post?"
msgstr "Tes cambios sen gardar. Desbotas esta publicación?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:655
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr "{0, plural, one {O ficheiro {1} non é compatible.} other {Os ficheiros {2} non son compatibles.}}"

#: src/components/compose.jsx:665
#: src/components/compose.jsx:683
#: src/components/compose.jsx:1795
#: src/components/compose.jsx:1930
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, one {Só podes anexar un ficheiro.} other {Só podes anexar ata # ficheiros.}}"

#: src/components/compose.jsx:873
msgid "Pop out"
msgstr "Despregar"

#: src/components/compose.jsx:880
msgid "Minimize"
msgstr "Minimizar"

#: src/components/compose.jsx:916
msgid "Looks like you closed the parent window."
msgstr "Semella que fechaches a xanela nai."

#: src/components/compose.jsx:923
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Semella que xa tes aberto un cadro de edición na xanela nai e estase a publicar. Por favor agarda a que remate e inténtao outra vez máis tarde."

#: src/components/compose.jsx:928
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Semella que xa tes un cadro de edición aberto na xanela nai. Ao traelo a esta xanela desbotarás os cambios realizados na xanela nai. Queres continuar?"

#: src/components/compose.jsx:971
msgid "Pop in"
msgstr "Restablecer"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:981
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "Respondendo á publicación de @{0} (<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:991
msgid "Replying to @{0}’s post"
msgstr "Respondendo á publicación de @{0}"

#: src/components/compose.jsx:1004
msgid "Editing source post"
msgstr "Editando o contido da publicación"

#: src/components/compose.jsx:1057
msgid "Poll must have at least 2 options"
msgstr "A enquisa ten que ter 2 opcións como mínimo"

#: src/components/compose.jsx:1061
msgid "Some poll choices are empty"
msgstr "Algunhas opcións da enquisa están baleiras"

#: src/components/compose.jsx:1074
msgid "Some media have no descriptions. Continue?"
msgstr "Algún do multimedia non ten descrición. Queres continuar?"

#: src/components/compose.jsx:1126
msgid "Attachment #{i} failed"
msgstr "Fallou o anexo #{i}"

#: src/components/compose.jsx:1222
#: src/components/status.jsx:2328
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "Aviso sobre o contido"

#: src/components/compose.jsx:1238
msgid "Content warning or sensitive media"
msgstr "Aviso sobre o contido ou multimedia sensible"

#: src/components/compose.jsx:1274
#: src/components/status.jsx:99
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "Pública"

#: src/components/compose.jsx:1279
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:100
msgid "Local"
msgstr "Local"

#: src/components/compose.jsx:1283
#: src/components/status.jsx:101
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "Fóra das listas"

#: src/components/compose.jsx:1286
#: src/components/status.jsx:102
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "Só para seguidoras"

#: src/components/compose.jsx:1289
#: src/components/status.jsx:103
#: src/components/status.jsx:2204
msgid "Private mention"
msgstr "Mención privada"

#: src/components/compose.jsx:1298
msgid "Post your reply"
msgstr "Publica a resposta"

#: src/components/compose.jsx:1300
msgid "Edit your post"
msgstr "Edita a publicación"

#: src/components/compose.jsx:1301
msgid "What are you doing?"
msgstr "Que estás a facer?"

#: src/components/compose.jsx:1380
msgid "Mark media as sensitive"
msgstr "Marcar o multimedia como sensible"

#: src/components/compose.jsx:1417
msgid "Posting on <0/>"
msgstr "Publicar o <0/>"

#: src/components/compose.jsx:1448
#: src/components/compose.jsx:3313
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "Engadir"

#: src/components/compose.jsx:1676
msgid "Schedule"
msgstr "Programar"

#: src/components/compose.jsx:1678
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1192
#: src/components/status.jsx:1978
#: src/components/status.jsx:1979
#: src/components/status.jsx:2673
msgid "Reply"
msgstr "Responder"

#: src/components/compose.jsx:1680
msgid "Update"
msgstr "Actualizar"

#: src/components/compose.jsx:1681
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Publicar"

#: src/components/compose.jsx:1807
msgid "Downloading GIF…"
msgstr "Descargando GIF…"

#: src/components/compose.jsx:1835
msgid "Failed to download GIF"
msgstr "Fallou a descarga da GIF"

#: src/components/compose.jsx:2060
#: src/components/compose.jsx:2153
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "Máis…"

#: src/components/compose.jsx:2589
msgid "Uploaded"
msgstr "Cargada"

#: src/components/compose.jsx:2602
msgid "Image description"
msgstr "Descrición da imaxe"

#: src/components/compose.jsx:2603
msgid "Video description"
msgstr "Descrición do vídeo"

#: src/components/compose.jsx:2604
msgid "Audio description"
msgstr "Descrición do audio"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2639
#: src/components/compose.jsx:2659
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "Ficheiro demasiado grande. Podería haber problemas ao cargalo. Intenta reducir o tamaño de {0} a {1} ou inferior."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2651
#: src/components/compose.jsx:2671
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr "Tamaño demasiado grande. Podería dar problemas ao cargala. Intenta reducir o tamaño de {0}×{1}px a {2}×{3}px."

#: src/components/compose.jsx:2679
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "Taxa de imaxes demasiado alta. Podería dar problemas ao cargalo."

#: src/components/compose.jsx:2739
#: src/components/compose.jsx:2989
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "Retirar"

#: src/components/compose.jsx:2756
#: src/compose.jsx:84
msgid "Error"
msgstr "Erro"

#: src/components/compose.jsx:2781
msgid "Edit image description"
msgstr "Editar descrición da imaxe"

#: src/components/compose.jsx:2782
msgid "Edit video description"
msgstr "Editar descrición do vídeo"

#: src/components/compose.jsx:2783
msgid "Edit audio description"
msgstr "Editar descrición do audio"

#: src/components/compose.jsx:2828
#: src/components/compose.jsx:2877
msgid "Generating description. Please wait…"
msgstr "Creando a descrición. Agarda…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2848
msgid "Failed to generate description: {0}"
msgstr "Fallou a creación da descrición: {0}"

#: src/components/compose.jsx:2849
msgid "Failed to generate description"
msgstr "Fallou a creación da descrición"

#: src/components/compose.jsx:2861
#: src/components/compose.jsx:2867
#: src/components/compose.jsx:2913
msgid "Generate description…"
msgstr "Crear unha descrición…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2900
msgid "Failed to generate description{0}"
msgstr "Fallou a creación da descrición{0}"

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2915
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>— experimental</0>"

#: src/components/compose.jsx:2934
msgid "Done"
msgstr "Feito"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2970
msgid "Choice {0}"
msgstr "Opción {0}"

#: src/components/compose.jsx:3017
msgid "Multiple choices"
msgstr "Varias opcións"

#: src/components/compose.jsx:3020
msgid "Duration"
msgstr "Duración"

#: src/components/compose.jsx:3051
msgid "Remove poll"
msgstr "Retirar enquisa"

#: src/components/compose.jsx:3272
msgid "Search accounts"
msgstr "Buscar contas"

#: src/components/compose.jsx:3326
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "Erro ao cargar as contas"

#: src/components/compose.jsx:3470
msgid "Custom emojis"
msgstr "Emojis personais"

#: src/components/compose.jsx:3490
msgid "Search emoji"
msgstr "Buscar emoji"

#: src/components/compose.jsx:3521
msgid "Error loading custom emojis"
msgstr "Erro ao cargar os emojis personais"

#: src/components/compose.jsx:3532
msgid "Recently used"
msgstr "Usados recentemente"

#: src/components/compose.jsx:3533
msgid "Others"
msgstr "Outros"

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3571
msgid "{0} more…"
msgstr "{0} mais…"

#: src/components/compose.jsx:3709
msgid "Search GIFs"
msgstr "Buscar GIFs"

#: src/components/compose.jsx:3724
msgid "Powered by GIPHY"
msgstr "Proporcionado por GIPHY"

#: src/components/compose.jsx:3732
msgid "Type to search GIFs"
msgstr "Escribe para buscar GIFs"

#: src/components/compose.jsx:3830
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "Anterior"

#: src/components/compose.jsx:3848
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "Seguinte"

#: src/components/compose.jsx:3865
msgid "Error loading GIFs"
msgstr "Erro ao cargar GIFs"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "Borradores non enviados"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "Semella que tes borradores sen enviar. Continuemos onde o deixaches."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "Desbotar este borrador?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "Erro ao desbotar o borrador! Inténtao outra vez."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1595
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "Eliminar…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "Erro ao obter o estado ao que responder!"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "Desbotar todos os borradores?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "Erro ao eliminar os borradores! Inténtao outra vez."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "Eliminar todo…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "Non hai borradores."

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "Enquisa"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:375
msgid "Media"
msgstr "Multimedia"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "Abrir nunha nova xanela"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "Aceptar"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "Rexeitar"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "Aceptado"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "Rexeitado"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:335
#: src/pages/search.jsx:368
msgid "Accounts"
msgstr "Contas"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:562
#: src/pages/status.jsx:1426
msgid "Show more…"
msgstr "Mostrar máis…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:567
msgid "The end."
msgstr "Fin."

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "Nada que mostrar"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "Atallos do teclado"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "Axuda sobre atallos do teclado"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "Seguinte publicación"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "Publicación anterior"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "Omitir carrusel e ir á seguinte publicación"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Maiús</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "Omitir carrusel e ir á publicación anterior"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Maiús</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "Cargar novas publicacións"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "Abrir detalles da publicación"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Enter</0> ou <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "Despregar o aviso sobre o contido ou<0/>pregar/despregar os fíos"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "Fechar publicación ou diálogos"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc</0> or <1>Retroceso</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "Foco na columna no modo con varias columnas"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> a <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr "Foco na seguinte columna no modo multi-columna"

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr "Foco na columna anterior no modo multi-columna"

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "Escribir nova publicación"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "Escribir nova publicación (nova xanela)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Maiús</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "Enviar publicación"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Ctrl</0> + <1>Enter</1> ou <2>⌘</2> + <3>Enter</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:204
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:47
#: src/pages/search.jsx:317
msgid "Search"
msgstr "Buscar"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "Responder (nova xanela)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Maiús</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "Favorecer (favorita)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> ou <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1200
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
#: src/components/status.jsx:2724
msgid "Boost"
msgstr "Promover"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Maiús</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
#: src/components/status.jsx:2749
msgid "Bookmark"
msgstr "Marcar"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "Cambiar o Modo Capa"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Maiús</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "Editar lista"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "Non se puido editar a lista."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "Non se puido crear a lista."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "Mostrar respostas a membros da lista"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "Mostrar respostas a persoas que sigo"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "Non mostrar respostas"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "Nesta lista, agochar as publicacións que están en Inicio/Seguindo"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "Crear"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "Eliminar esta lista?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "Non se puido eliminar a lista."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr "As publicacións desta lista non aparecen en Inicio/Seguindo"

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "Descrición do multimedia"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1306
#: src/components/status.jsx:1315
#: src/components/translation-block.jsx:239
msgid "Translate"
msgstr "Traducir"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1334
msgid "Speak"
msgstr "Falar"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "Abrir multimedia orixinal nunha nova xanela"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "Abrir multimedia orixinal"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "Intentando describir a imaxe. Agarda…"

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "Non se puido describir a imaxe"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "Describir a imaxe…"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "Ver publicación"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "Multimedia sensible"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "Filtrado: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3850
#: src/components/status.jsx:3946
#: src/components/status.jsx:4024
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "Filtrado"

#: src/components/media.jsx:479
msgid "Open file"
msgstr "Abrir ficheiro"

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr "Publicación programada"

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "Publicouse a mensaxe. Compróbao."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr "Resposta programada"

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "Publicouse a resposta. Compróbao."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "Actualizouse a publicación. Compróbao."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "Menú"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "Volver cargar a páxina para actualizar?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "Nova actualización dispoñible…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr "Seguimento"

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "Ponte ao día"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "Mencións"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "Notificacións"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "Novidade"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "Perfil"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "Marcadores"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "Favorecementos"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "Cancelos seguidos"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:341
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "Filtros"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "Usuarias acaladas"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "Usuarias acaladas…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "Usuarias bloqueadas"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "Usuarias bloqueadas…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "Contas…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:199
#: src/pages/status.jsx:925
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "Acceder"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "En voga"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "Federada"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "Atallos / Columnas…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "Axustes…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "Listas"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "Todas as Listas"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "Notificación"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "Esta notificación procede de outra conta."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "Ver todas as notificacións"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} reaccionou á túa publicación con {emojiObject}"

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} enviou unha publicación."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} promoveu a túa resposta.} other {{account} promoveu a túa publicación.}}} other {{account} promoveu {postsCount} das túas publicacións.}}} other {{postType, select, reply {<0><1>{0}</1> persoas</0> promoveron a túa resposta.} other {<2><3>{1}</3> persoas</2> promoveron a túa publicación.}}}}"

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, one {}=1 {{account} seguiute.} other {<0><1>{0}</1> persoas</0> seguíronte.}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} solicitou seguirte."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural,  =1 {{postsCount, plural, =1 {{postType, select, reply {a {account} gustoulle a túa resposta.} other {a {account} gustoulle a túa publicación.}}} other {a {account} gustoulle {postsCount} das túas publicacións.}}} other {{postType, select, reply {a <0><1>{0}</1> persoas</0> gustoulle a túa resposta.} other {a <2><3>{1}</3> persoas</2> gustoulle a túa publicación.}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "Finalizou unha enquisa creada por ti ou na que votaches."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "Finalizou unha enquisa creada por ti."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "Finalizou unha enquisa na que votaches."

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "Editouse unha publicación coa que interactuaches."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {a {account} gustoulle e promoveu a túa resposta.} other {a {account} gustoulle e promoveu a túa publicación.}}} other {a {account} gustoulle e promoveu {postsCount} das túas publicacións.}}} other {{postType, select, reply {a <0><1>{0}</1> persoas</0> gustoulles e promoveron a túa resposta.} other {a <2><3>{1}</3> persoas</2> gustoulle e promoveron a túa publicación.}}}}"

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} creou a conta."

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} denunciou a {targetAccount}"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "Conexións perdidas con <0>{name}</0>."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "Aviso da Moderación"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr "Xa chegou o teu {year} #Wrapstodon!"

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "A administración de <0>{from}</0> suspendeu a <1>{targetName}</1>, xa que logo non vas recibir actualizacións desa conta ou interactuar con ela."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "A administración de <0>{from}</0> bloqueou a <1>{targetName}</1>. Seguidoras afectadas: {followersCount}; seguimentos: {followingCount}."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "Bloqueaches a <0>{targetName}</0>. Seguidoras eliminadas: {followersCount}; seguimentos: {followingCount}."

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "A túa conta recibeu unha advertencia da moderación."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "A túa conta foi desactivada."

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "Algunha das túas publicacións foron marcadas como sensibles."

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "Algunha das túas publicacións foron eliminadas."

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "De agora en diante as túas publicacións van ser marcadas como sensibles."

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "A túa conta foi limitada."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "A túa conta foi suspendida."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[Tipo de notificación descoñecido: {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1277
#: src/components/status.jsx:1287
msgid "Boosted/Liked by…"
msgstr "Promovida/Favorecida por…"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "Favorecida por…"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "Promovida por…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "Seguida por…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "Saber mais <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr "Ver #Wrapstodon"

#: src/components/notification.jsx:801
#: src/components/status.jsx:486
msgid "Read more →"
msgstr "Ler mais →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "Xa votaches"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr "{optionVotesCount, plural, one {# voto} other {# votos}}"

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "Agochar resultados"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "Votar"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1295
#: src/pages/status.jsx:1318
msgid "Refresh"
msgstr "Actualizar"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "Mostrar resultados"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> voto} other {<1>{1}</1> votos}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, one {<0>{0}</0> votante} other {<1>{1}</1> votantes}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "Finalizou <0/>"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "Finalizou"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "Finaliza en <0/>"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "Finaliza"

#: src/components/recent-searches.jsx:27
msgid "Cleared recent searches"
msgstr "Limpáronse as buscas recentes"

#: src/components/recent-searches.jsx:49
#: src/components/search-form.jsx:328
msgid "Recent searches"
msgstr "Buscas recentes"

#: src/components/recent-searches.jsx:59
msgid "Clear all"
msgstr "Limpar todo"

#: src/components/recent-searches.jsx:94
#: src/pages/account-statuses.jsx:334
msgid "Clear"
msgstr "Limpar"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0}s"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0}m"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0}h"

#: src/components/report-modal.jsx:30
msgid "Spam"
msgstr "Spam"

#: src/components/report-modal.jsx:31
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "Ligazóns maliciosas, relacións engañosas, ou respostas repetitivas"

#: src/components/report-modal.jsx:34
msgid "Illegal"
msgstr "Ilegalidade"

#: src/components/report-modal.jsx:35
msgid "Violates the law of your or the server's country"
msgstr "Viola as leis do país do servidor"

#: src/components/report-modal.jsx:38
msgid "Server rule violation"
msgstr "Violación das regras do servidor"

#: src/components/report-modal.jsx:39
msgid "Breaks specific server rules"
msgstr "Contravén as regras propias do servidor"

#: src/components/report-modal.jsx:40
msgid "Violation"
msgstr "Violación"

#: src/components/report-modal.jsx:43
msgid "Other"
msgstr "Outra"

#: src/components/report-modal.jsx:44
msgid "Issue doesn't fit other categories"
msgstr "O problema non cae en ningunha das categorías"

#: src/components/report-modal.jsx:111
msgid "Report Post"
msgstr "Denunciar Publicación"

#: src/components/report-modal.jsx:111
msgid "Report @{username}"
msgstr "Denunciar a @{username}"

#: src/components/report-modal.jsx:147
msgid "Pending review"
msgstr "Pendente de revisión"

#: src/components/report-modal.jsx:189
msgid "Post reported"
msgstr "Publicación denunciada"

#: src/components/report-modal.jsx:189
msgid "Profile reported"
msgstr "Perfil denunciado"

#: src/components/report-modal.jsx:197
msgid "Unable to report post"
msgstr "Non se puido denunciar a publicación"

#: src/components/report-modal.jsx:198
msgid "Unable to report profile"
msgstr "Non se puido denunciar o perfil"

#: src/components/report-modal.jsx:206
msgid "What's the issue with this post?"
msgstr "Cal é o problema con esta publicación?"

#: src/components/report-modal.jsx:207
msgid "What's the issue with this profile?"
msgstr "Cal é o problema con este perfil?"

#: src/components/report-modal.jsx:276
msgid "Additional info"
msgstr "Info adicional"

#: src/components/report-modal.jsx:299
msgid "Forward to <0>{domain}</0>"
msgstr "Reenviar a <0>{domain}</0>"

#: src/components/report-modal.jsx:309
msgid "Send Report"
msgstr "Enviar Denuncia"

#: src/components/report-modal.jsx:318
msgid "Muted {username}"
msgstr "Silenciou a {username}"

#: src/components/report-modal.jsx:321
msgid "Unable to mute {username}"
msgstr "Non se puido acalar a {username}"

#: src/components/report-modal.jsx:326
msgid "Send Report <0>+ Mute profile</0>"
msgstr "Enviar Denuncia <0>+Silenciar perfil</0>"

#: src/components/report-modal.jsx:337
msgid "Blocked {username}"
msgstr "Bloqueouse a {username}"

#: src/components/report-modal.jsx:340
msgid "Unable to block {username}"
msgstr "Non se puido bloquear a {username}"

#: src/components/report-modal.jsx:345
msgid "Send Report <0>+ Block profile</0>"
msgstr "Enviar Denuncia <0>+ Bloquear o perfil</0>"

#: src/components/search-form.jsx:18
msgid "Posts with <0>{query}</0>"
msgstr "Publicacións con <0>{query}</0>"

#: src/components/search-form.jsx:26
msgid "Accounts with <0>{query}</0>"
msgstr "Contas con <0>{query}</0>"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:34
msgid "Posts tagged with <0>#{0}</0>"
msgstr "Publicacións co cancelo <0>#{0}</0>"

#: src/components/search-form.jsx:43
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>‒ contas, cancelos e publicacións</0>"

#: src/components/search-form.jsx:135
msgid "Look up <0>{query}</0>"
msgstr "Busca de <0>{query}</0>"

#: src/components/search-form.jsx:361
#: src/pages/home.jsx:251
msgid "See all"
msgstr "Ver todo"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "Inicio / Seguindo"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "Pública (Local / Federada)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "Conta"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "Cancelo"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "ID da lista"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "Só local"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:203
msgid "Instance"
msgstr "Instancia"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "Optativo, ex. mastodon.social"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "Termo a buscar"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "Optativo, a non ser para o modo de varias columnas"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "ex. PixelArt (Máx 5, separado por espazos)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "Só multimedia"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "Atallos"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "beta"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "Indicar unha lista de atallos que aparecerán como:"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "Botón flotante"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "Barra con Menú/Pestana"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "Varias columnas"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "Non dispoñible coa visualización actual"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "Ir arriba"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "Ir abaixo"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1557
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "Editar"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "Engadir máis de un atallo/columna para que isto funcione."

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "Aínda non hai columnas. Toca no botón Engadir columna."

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "Aínda non hai atallos. Toca no botón Engadir atallo."

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "Non sabes que engadir?<0/>Intenta engadir <1>Inicio / Seguindo e Notificacións</1>."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "Máx {SHORTCUTS_LIMIT} columnas"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "Máx {SHORTCUTS_LIMIT} atallos"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "Importar/exportar"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "Engadir columna…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "Engadir atallo…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "É opcional seleccionar unha lista. No modo de varias columnas hai que indicar unha, se non non se mostrará."

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "No modo de varias columnas, requírese unha palabra a buscar, se non a columna no se vai mostrar."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "Hai compatibilidade para varios cancelos, separados por espazos."

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "Editar atallo"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "Engadir atallo"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "Cronoloxía"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "Lista"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "Importar/Exportar <0>Atallos</0>"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "Importar"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "Pega aquí os atallos"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "A descargar os atallos gardados desde o servidor da instancia…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "Non se puideron descargar os atallos"

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "Descargar atallos desde o servidor da instancia"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "* Existe nos atallos actuais"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "A lista podería non funcionar se procede de outra conta."

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "O formato dos axustes non é válido"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "Engadir aos atallos actuais?"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr "Só se engadirán os atallos que non existan nos atallos actuais."

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "Non hai atallos que importar"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "Importáronse os atallos. Excedeuse o máximo de {SHORTCUTS_LIMIT}, así que o resto non se importaron."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "Atallos importados"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "Importar e engadir…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "Sobrescribir os atallos actuais?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "Importar os atallos?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "ou sobrescribir…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "Importar…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "Exportar"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "Copiáronse os atallos"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "Non se puideron copiar os atallos"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "Copiáronse os axustes do atallo"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "Non se puideron copiar os axustes do atallo"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "Compartir"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "A gardar os atallos no servidor da instancia…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "Gardáronse os atallos"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "Non se puideron gardar os atallos"

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "Sincronizar co servidor da instancia"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0, plural, one {# caracter} other {# caracteres}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "Atallos en formato JSON"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "Importar/exportar os axustes de/para o servidor da instancia (moi experimental)"

#: src/components/status.jsx:351
msgid "Unable to format math"
msgstr "Non se puido dar formato"

#: src/components/status.jsx:365
msgid "Math expressions found."
msgstr "Hai expresións matemáticas."

#. Action to switch from rendered math back to raw (LaTeX) markup
#: src/components/status.jsx:368
msgid "Show markup"
msgstr "Mostrar marcado"

#. Action to render math expressions from raw (LaTeX) markup
#: src/components/status.jsx:373
msgid "Format math"
msgstr "Dar formato matemático\n"

#: src/components/status.jsx:772
msgid "<0/> <1>boosted</1>"
msgstr "<0/> <1>promoveu</1>"

#: src/components/status.jsx:875
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "Sentímolo pero a instancia na que iniciaches sesión non pode interactuar coa publicación desde outra instancia."

#. placeholder {0}: username || acct
#: src/components/status.jsx:1029
msgid "Unliked @{0}'s post"
msgstr "Retirado o favorecemento a @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1030
msgid "Liked @{0}'s post"
msgstr "Favorecida a publicación de @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1069
msgid "Unbookmarked @{0}'s post"
msgstr "Desmarcouse a publicación de @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1070
msgid "Bookmarked @{0}'s post"
msgstr "Marcouse a publicación de @{0}"

#: src/components/status.jsx:1169
msgid "Some media have no descriptions."
msgstr "Algún multimedia non ten descrición."

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1176
msgid "Old post (<0>{0}</0>)"
msgstr "Publicación antiga (<0>{0}</0>)"

#: src/components/status.jsx:1200
#: src/components/status.jsx:1240
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
msgid "Unboost"
msgstr "Retirar promoción"

#: src/components/status.jsx:1216
#: src/components/status.jsx:2715
msgid "Quote"
msgstr "Cita"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1228
#: src/components/status.jsx:1694
msgid "Unboosted @{0}'s post"
msgstr "Retirada a promoción da publicación de @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1229
#: src/components/status.jsx:1695
msgid "Boosted @{0}'s post"
msgstr "Promoveu a publicación de @{0}"

#: src/components/status.jsx:1241
msgid "Boost…"
msgstr "Promover…"

#: src/components/status.jsx:1253
#: src/components/status.jsx:1988
#: src/components/status.jsx:2736
msgid "Unlike"
msgstr "Xa non me gusta"

#: src/components/status.jsx:1254
#: src/components/status.jsx:1988
#: src/components/status.jsx:1989
#: src/components/status.jsx:2736
#: src/components/status.jsx:2737
msgid "Like"
msgstr "Gústame"

#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
msgid "Unbookmark"
msgstr "Retirar marcador"

#: src/components/status.jsx:1346
msgid "Post text copied"
msgstr "Copiouse o texto da publicación"

#: src/components/status.jsx:1349
msgid "Unable to copy post text"
msgstr "Non se puido copiar o texto da publicación"

#: src/components/status.jsx:1355
msgid "Copy post text"
msgstr "Copiar texto da publicación"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1373
msgid "View post by <0>@{0}</0>"
msgstr "Ver publicación de <0>@{0}</0>"

#: src/components/status.jsx:1394
msgid "Show Edit History"
msgstr "Mostrar historial de edicións"

#: src/components/status.jsx:1397
msgid "Edited: {editedDateText}"
msgstr "Editada: {editedDateText}"

#: src/components/status.jsx:1464
#: src/components/status.jsx:3516
msgid "Embed post"
msgstr "Incluír a publicación"

#: src/components/status.jsx:1478
msgid "Conversation unmuted"
msgstr "Conversa reactivada"

#: src/components/status.jsx:1478
msgid "Conversation muted"
msgstr "Conversa acalada"

#: src/components/status.jsx:1484
msgid "Unable to unmute conversation"
msgstr "Non se puido reactivar a conversa"

#: src/components/status.jsx:1485
msgid "Unable to mute conversation"
msgstr "Non se puido acalar a conversa"

#: src/components/status.jsx:1494
msgid "Unmute conversation"
msgstr "Reactivar a conversa"

#: src/components/status.jsx:1501
msgid "Mute conversation"
msgstr "Acalar conversa"

#: src/components/status.jsx:1517
msgid "Post unpinned from profile"
msgstr "Desprendeuse do perfil a publicación"

#: src/components/status.jsx:1518
msgid "Post pinned to profile"
msgstr "Publicación fixada ao perfil"

#: src/components/status.jsx:1523
msgid "Unable to unpin post"
msgstr "Non se puido desprender a publicación"

#: src/components/status.jsx:1523
msgid "Unable to pin post"
msgstr "Non se puido fixar a publicación"

#: src/components/status.jsx:1532
msgid "Unpin from profile"
msgstr "Soltar do perfil"

#: src/components/status.jsx:1539
msgid "Pin to profile"
msgstr "Fixar no perfil"

#: src/components/status.jsx:1568
msgid "Delete this post?"
msgstr "Eliminar publicación?"

#: src/components/status.jsx:1584
msgid "Post deleted"
msgstr "Eliminouse a publicación"

#: src/components/status.jsx:1587
msgid "Unable to delete post"
msgstr "Non se puido eliminar a publicación"

#: src/components/status.jsx:1615
msgid "Report post…"
msgstr "Denunciar publicación…"

#: src/components/status.jsx:1989
#: src/components/status.jsx:2025
#: src/components/status.jsx:2737
msgid "Liked"
msgstr "Favorecida"

#: src/components/status.jsx:2022
#: src/components/status.jsx:2724
msgid "Boosted"
msgstr "Promovida"

#: src/components/status.jsx:2032
#: src/components/status.jsx:2749
msgid "Bookmarked"
msgstr "Marcada"

#: src/components/status.jsx:2036
msgid "Pinned"
msgstr "Fixada"

#: src/components/status.jsx:2082
#: src/components/status.jsx:2561
msgid "Deleted"
msgstr "Eliminada"

#: src/components/status.jsx:2123
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {# resposta} other {# respostas}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2213
msgid "Thread{0}"
msgstr "Fío{0}"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
#: src/components/status.jsx:2457
msgid "Show less"
msgstr "Ver menos"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
msgid "Show content"
msgstr "Mostrar contido"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2453
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Filtrado: {0}"

#: src/components/status.jsx:2457
msgid "Show media"
msgstr "Mostrar multimedia"

#: src/components/status.jsx:2597
msgid "Edited"
msgstr "Editada"

#: src/components/status.jsx:2674
msgid "Comments"
msgstr "Comentarios"

#. More from [Author]
#: src/components/status.jsx:2974
msgid "More from <0/>"
msgstr "Mais de <0/>"

#: src/components/status.jsx:3276
msgid "Edit History"
msgstr "Editar Historial"

#: src/components/status.jsx:3280
msgid "Failed to load history"
msgstr "Fallou a carga do historial"

#: src/components/status.jsx:3285
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "Cargando…"

#: src/components/status.jsx:3521
msgid "HTML Code"
msgstr "Código HTML"

#: src/components/status.jsx:3538
msgid "HTML code copied"
msgstr "Copiouse o código HTML"

#: src/components/status.jsx:3541
msgid "Unable to copy HTML code"
msgstr "Non se puido copiar o código HTML"

#: src/components/status.jsx:3553
msgid "Media attachments:"
msgstr "Anexos multimedia:"

#: src/components/status.jsx:3575
msgid "Account Emojis:"
msgstr "Emojis da conta:"

#: src/components/status.jsx:3606
#: src/components/status.jsx:3651
msgid "static URL"
msgstr "URL estático"

#: src/components/status.jsx:3620
msgid "Emojis:"
msgstr "Emojis:"

#: src/components/status.jsx:3665
msgid "Notes:"
msgstr "Notas:"

#: src/components/status.jsx:3669
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "Isto é contido estático, sen estilo nin scripts. Pode que teñas que aplicar o teu propio estilo e adaptalo ás necesidades."

#: src/components/status.jsx:3675
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "As enquisas non son interactivas, convértese nunha lista con conta dos votos."

#: src/components/status.jsx:3680
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "O multimedia anexo poden ser imaxes, vídeos, audios ou varios tipos de ficheiros."

#: src/components/status.jsx:3686
msgid "Post could be edited or deleted later."
msgstr "A publicación pode editarse ou eliminarse con posterioridade."

#: src/components/status.jsx:3692
msgid "Preview"
msgstr "Vista previa"

#: src/components/status.jsx:3701
msgid "Note: This preview is lightly styled."
msgstr "Nota: a vista previa está lixeiramente editada."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3954
msgid "<0/> <1/> boosted"
msgstr "<0/> <1/> promoveu"

#: src/components/status.jsx:4056
msgid "Post hidden by your filters"
msgstr "Publicación oculta por mor dos teus filtros"

#: src/components/status.jsx:4057
msgid "Post pending"
msgstr "Publicación pendente"

#: src/components/status.jsx:4058
#: src/components/status.jsx:4059
#: src/components/status.jsx:4060
#: src/components/status.jsx:4061
msgid "Post unavailable"
msgstr "Publicación non dispoñible"

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "Novas publicacións"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1078
#: src/pages/status.jsx:1455
msgid "Try again"
msgstr "Inténtao outra vez"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, one {# Promoción} other {# Promocións}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr "Publicacións fixadas"

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "Fío"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>Filtrado</0>: <1>{0}</1>"

#: src/components/translation-block.jsx:196
msgid "Auto-translated from {sourceLangText}"
msgstr "Tradución automática desde o {sourceLangText}"

#: src/components/translation-block.jsx:234
msgid "Translating…"
msgstr "A traducir…"

#: src/components/translation-block.jsx:237
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "Traducir do {sourceLangText} (detección automática)"

#: src/components/translation-block.jsx:238
msgid "Translate from {sourceLangText}"
msgstr "Traducir do {sourceLangText}"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:266
msgid "Auto ({0})"
msgstr "Auto ({0})"

#: src/components/translation-block.jsx:279
msgid "Failed to translate"
msgstr "Fallou a tradución"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "A editar o contido do estado"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "En resposta a @{0}"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "Xa podes fechar esta páxina."

#: src/compose.jsx:71
msgid "Close window"
msgstr "Fechar xanela"

#: src/compose.jsx:87
msgid "Login required."
msgstr "Precisa acceder."

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:280
msgid "Go home"
msgstr "Ir ao inicio"

#: src/pages/account-statuses.jsx:241
msgid "Account posts"
msgstr "Publicacións da conta"

#: src/pages/account-statuses.jsx:248
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ Respostas)"

#: src/pages/account-statuses.jsx:250
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- Promocións)"

#: src/pages/account-statuses.jsx:252
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:254
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (Multimedia)"

#: src/pages/account-statuses.jsx:260
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:331
msgid "Clear filters"
msgstr "Limpar filtros"

#: src/pages/account-statuses.jsx:348
msgid "Showing post with replies"
msgstr "Mostrando publicacións con resposta"

#: src/pages/account-statuses.jsx:353
msgid "+ Replies"
msgstr "+ Respostas"

#: src/pages/account-statuses.jsx:359
msgid "Showing posts without boosts"
msgstr "Mostrando publicacións sen promocións"

#: src/pages/account-statuses.jsx:364
msgid "- Boosts"
msgstr "- Promocións"

#: src/pages/account-statuses.jsx:370
msgid "Showing posts with media"
msgstr "Mostrando publicacións con multimedia"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:387
msgid "Showing posts tagged with #{0}"
msgstr "Mostrando publicacións etiquetadas con #{0}"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:426
msgid "Showing posts in {0}"
msgstr "Mostando publicacións en {0}"

#: src/pages/account-statuses.jsx:516
msgid "Nothing to see here yet."
msgstr "Aínda non hai nada que ver."

#: src/pages/account-statuses.jsx:517
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "Non se puideron cargar as publicacións"

#: src/pages/account-statuses.jsx:558
#: src/pages/account-statuses.jsx:588
msgid "Unable to fetch account info"
msgstr "Non se puido obter a información da conta"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:565
msgid "Switch to account's instance {0}"
msgstr "Cambiar á instancia da conta {0}"

#: src/pages/account-statuses.jsx:595
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "Cambiar á miña instancia (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:668
msgid "Month"
msgstr "Mes"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "Actual"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "Por defecto"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "Cambiar a esta conta"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "Cambiar en nova pestana/xanela"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "Ver perfil…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "Establecer por defecto"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr "Pechar a sesión de <0>@{0}</0>?"

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "Fechar sesión…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr "Connectada o {0} (<0/>)"

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "Engadir unha conta existente"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "Nota: A conta <0>por defecto</0> sempre será a que se mostre ao iniciar. O cambio de conta manterase durante a sesión."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "Non tes marcadores, vai mirar e garda o que che interese!"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "Non se cargaron os marcadores."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "última hora"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "últimas 2 horas"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "últimas 3 horas"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "últimas 4 horas"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "últimas 5 horas"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "últimas 6 horas"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "últimas 7 horas"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "últimas 8 horas"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "últimas 9 horas"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "últimas 10 horas"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "últimas 11 horas"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "últimas 12 horas"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "máis de 12 horas"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "Cancelos seguidos"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "Grupos"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "Móstrase {selectedFilterCategory, select, all {todas as publicacións} original {publicacións orixinais} replies {respostas} boosts {promocións} followedTags {cancelos seguidos} groups {grupos} filtered {publicacións filtradas}}, {sortBy, select, createdAt {{sortOrder, select, asc {máis antigo} desc {máis recente}}} reblogsCount {{sortOrder, select, asc {con menos promocións} desc {con máis promocións}}} favouritesCount {{sortOrder, select, asc {con menos favorecementos} desc {con máis favorecementos}}} repliesCount {{sortOrder, select, asc {con menos respostas} desc {con máis respostas}}} density {{sortOrder, select, asc {menor densidade} desc {maior densidade}}}} primeiro{groupBy, select, account {, agrupado por autoría} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "Ponte ao día <0>beta</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "Axuda"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "Que é isto?"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "Ponte ao Día é unha cronoloxía separada para os teus seguimentos onde obter unha visión rápida e doada do publicado, cunha interface inspirada na do correo electrónico para organizar e filtrar as publicacións."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Vista previa da interface de Ponte ao día"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "Poñámonos ao día"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "Vexamos o que publicaron as persoas que segues."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "Móstrame todas as publicacións das…"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "o máximo"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "Obter"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "Entraría na túa última posta ao día"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "Ata a última posta ao día ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "Nota: pode que a túa instancia mostre un máximo de 800 publicacións na cronoloxía de Inicio independentemente do tempo que marques. Podería variar."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "Anteriormente…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, one {# publicación} other {# publicacións}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "Retirar esta posta ao día?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr "Retirar Ponte ao Día {0}"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr "Eliminouse o Ponte ao Día {0}"

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "Nota: só se gardan 3 postas ao día. O resto elimínanse automaticamente."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "Obtendo as publicacións…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "Podería levarlle un anaco."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "Restablecer filtros"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "O máis compartido"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "Compartido por {0}"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:330
msgid "All"
msgstr "Todo"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, one {# autora} other {# autoras}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "Orde"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Data"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Densidade"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr "group.filter"

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "Autoría"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "Ningún"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "Mostra todas as autoras"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "Non tes que ler todo."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "Isto é todo."

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "Volver arriba"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "Ligazóns compartidas polos teus seguimentos, organizados por contas compartidas, promocións e favorecementos."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "Orde: Densidade"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "As publicacións están ordenadas pola densidade de información, pola cantidade relativa. As publicacións curtas son «lixeiras» mentres que as máis longas «pesan máis». As publicacións con fotos «pesan máis» que as que non as teñen."

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "Agrupar: Autoría"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "As publicacións están agrupadas pola autoría, e ordenadas por número de publicacións por autora."

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "Seguinte autora"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "Autora anterior"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "Ir arriba"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "Sen favoritas. Reparte ❤️! "

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "Non se cargaron os favorecementos."

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "Inicio e listas"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "Cronoloxías públicas"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "Conversas"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "Perfís"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "Nunca"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "Novo filtro"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0, plural, one {# filtro} other {# filtros}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "Non se cargaron os filtros."

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "Aínda non hai filtros."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "Engadir filtro"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "Editar filtro"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "Non se puido editar o filtro"

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "Non se puido crear o filtro"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "Título"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "Palabra completa"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "Sen palabras chave. Engade unha."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "Engadir palabra chave"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, one {# palabra chave} other {# palabras chave}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "Filtro desde…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* Aínda non se aplicou"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "Estado: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "Cambiar a caducidade"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "Caducidade"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "A publicación filtrada será…"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr "esvaecido (só multimedia)"

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "minimizadas"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "agochadas"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "Eliminar este filtro?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "Non se puido eliminar o filtro."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "Finalizou"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "Finaliza en <0/>"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "Non finaliza"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural, one {# cancelo} other {# cancelos}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "Non se puideron cargar os cancelos seguidos."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "Aínda non segues ningún cancelo."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "Non hai nada que ver aquí."

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "Non se puideron cargar as publicacións."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{hashtagTitle} (Só multimedia) en {instance}"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{hashtagTitle} en {instance}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (Só multimedia)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "Ninguén publicou usando este cancelo por agora."

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "Non se puideron cargar publicacións con este cancelo"

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "Deixar de seguir #{hashtag}?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "Retirouse o seguimento a #{hashtag}"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "Seguiches a #{hashtag}"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "Seguindo…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "Non se mostra no perfil"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "Non se retirou dos destacados no perfil"

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "Destacado no perfil"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr "{TOTAL_TAGS_LIMIT, plural, one {}other {Máx # cancelos}}"

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "Engadir cancelo"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "Retirar cancelo"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr "{SHORTCUTS_LIMIT, plural, one {Acadouse o máx. de # atallo. Non se puido engadir outro.} other {Acadouse o máx. de # atallos. Non se puido engadir outro.}}"

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "Xa existe este atallo"

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr "Engadiuse o atallo ao cancelo"

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "Engadir a Atallos"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "Escribe unha nova instancia, ex. \"mastodon.social\""

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "Instancia non válida"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "Ver outra instancia…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "Ir á miña instancia (<0>{currentInstance}</0>)"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "Non se obtiveron as notificacións."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<0>Nova</0> <1>Solicitude de Seguimento</1>"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "Resolvendo…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "Non se puido resolver o URL"

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "Nada por aquí."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "Xestionar membros"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "Retirar a <0>@{0}</0> da lista?"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "Retirar…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, one {# lista} other {# listas}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "Sen listas por agora."

#: src/pages/login.jsx:122
#: src/pages/login.jsx:132
msgid "Failed to register application"
msgstr "Fallou o rexistro da aplicación"

#: src/pages/login.jsx:218
msgid "instance domain"
msgstr "dominio da instancia"

#: src/pages/login.jsx:242
msgid "e.g. “mastodon.social”"
msgstr "ex. \"mastodon.social\""

#: src/pages/login.jsx:253
msgid "Failed to log in. Please try again or try another instance."
msgstr "Erro ao acceder. Inténtao outra vez ou cambiando de instancia."

#: src/pages/login.jsx:265
msgid "Continue with {selectedInstanceText}"
msgstr "Continuar con {selectedInstanceText}"

#: src/pages/login.jsx:266
msgid "Continue"
msgstr "Continuar"

#: src/pages/login.jsx:274
msgid "Don't have an account? Create one!"
msgstr "Non tes unha conta? Crea unha!"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "Mencións privadas"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "Privadas"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "Ninguén te mencionou :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "Non se cargaron as mencións."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "Non estás a seguir"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "Que non te segue"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "Cunha nova conta"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr "Con mención privada non solicitada"

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr "Que está limitada pola moderación do servidor"

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "Axustes das notificacións"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "Novas notificacións"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, one {Anuncio} other {Anuncios}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "Solicitudes de seguimento"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, one {# solicitude de seguimento} other {# solicitudes de seguimento}}"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr "{0, plural, one {Notificacións filtradas de # persoa} other {Notificacións filtradas de # persoas}}"

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "Só mencións"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "Hoxe"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "Xa estás ao día."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "Onte"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "Non se cargaron as notificacións"

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "Actualizáronse os axustes das notificacións"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "Filtrar as notificacións de persoas:"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "Filtrar"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "Ignorar"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "Acutalizado <0>{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr "Ver notificacións de <0>@{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr "Notificacións de <0>@{0}</0>"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr "A partir de agora non se filtrarán as notificacións de @{0}."

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr "Non se puido aceptar a solicitude de notificación"

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "Permitir"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr "A partir de agora non se mostrarán as notificacións de @{0} nas Notificacións filtradas."

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr "Non se puido desbotar a solicitude de notificación"

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "Desbotar"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "Desbotada"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "Cronoloxía local ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "Cronoloxía federada ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "Cronoloxía local"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "Cronoloxía federada"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "Ningén publicou nada por agora."

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "Cambiar á Federada"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "Cambiar á Local"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr "Sen publicacións programadas."

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr "Programada <0><1/></0> <2>({0})</2>"

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr "Programada <0><1/></0>"

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr "Publicación reprogramada"

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr "Fallou a reprogramación"

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr "Reprogramar"

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr "Eliminar publicación programada?"

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr "Eliminouse a programación"

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr "Fallou a eliminación da programación"

#: src/pages/search.jsx:51
msgid "Search: {q} (Posts)"
msgstr "Buscar: {q} (Publicacións)"

#: src/pages/search.jsx:54
msgid "Search: {q} (Accounts)"
msgstr "Buscar: {q} (Contas)"

#: src/pages/search.jsx:57
msgid "Search: {q} (Hashtags)"
msgstr "Buscar: {q} (Cancelos)"

#: src/pages/search.jsx:60
msgid "Search: {q}"
msgstr "Buscar: {q}"

#: src/pages/search.jsx:340
#: src/pages/search.jsx:422
msgid "Hashtags"
msgstr "Cancelos"

#: src/pages/search.jsx:372
#: src/pages/search.jsx:426
#: src/pages/search.jsx:496
msgid "See more"
msgstr "Ver máis"

#: src/pages/search.jsx:398
msgid "See more accounts"
msgstr "Ver máis contas"

#: src/pages/search.jsx:412
msgid "No accounts found."
msgstr "Non se atopan contas."

#: src/pages/search.jsx:468
msgid "See more hashtags"
msgstr "Ver máis cancelos"

#: src/pages/search.jsx:482
msgid "No hashtags found."
msgstr "Non se atopan cancelos."

#: src/pages/search.jsx:526
msgid "See more posts"
msgstr "Ver máis publicacións"

#: src/pages/search.jsx:540
msgid "No posts found."
msgstr "Non hai publicacións."

#: src/pages/search.jsx:585
msgid "Enter your search term or paste a URL above to get started."
msgstr "Escribe o que queres buscar ou pega un URL na caixa de busca."

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "Axustes"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "Aparencia"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "Claro"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "Escuro"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "Auto"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "Tamaño da letra"

#. Preview of one character, in largest size
#. Preview of one character, in smallest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "A"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "Idioma da interface"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "Traducida por voluntarias"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "Ao publicar"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "Visibilidade por defecto"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "Sincronizado"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "Erro ao actualizar a privacidade ao publicar"

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "Sincronizado cos axustes do servidor da túa instancia. <0>Vai á túa instancia ({instance}) para realizar máis axustes.</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "Experimentos"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "Actualización automática das cronoloxías"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "Carrusel de promocións"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "Tradución das publicacións"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr "Traducir ao "

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "Idioma do sistema ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr "{0, plural, one {}=0 {Ocultar o botón \"Traducir\" para:} other {Ocultar o botón \"Traducir\" para (#):}}"

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr "Nota: esta ferramenta usa servizos de tradución externos, proporcionados por <0>{TRANSLATION_API_NAME}</0>."

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "Tradución automática en liña"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr "Mostra automaticamente na cronoloxía a tradución das publicacións. Só funciona para publicacións <0>curtas</0> sen aviso sobre o contido, multimedia ou enquisa."

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "Selector de GIF para o editor"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "Nota: Esta ferramenta usa un servizo externo para buscar GIF, proporcionado por <0>GIPHY</0>. G-rated (axeitado para todas as idades), quítanse todos os parámetros de seguimento, omítese na solicitude a información da orixe da mesma, pero os termos da consulta e o enderezo IP acadan igualmente o seu servidor."

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "Xerador da descrición de imaxes"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr "Só para novas imaxes ao redactar novas publicacións."

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "Nota: esta ferramenta usa un servizo de IA externo, proporcionado por <0>img-alt-api</0>. Pode que non funcione moi ben. Só para imaxes e en Inglés."

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr "Notificacións agrupadas polo servidor"

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr "Característica en fase Alpha. Mellora potencial no agrupamento cunha lóxica básica para agrupar."

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "Importar/exportar na \"Nube\" os axustes dos atallos"

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr "⚠️⚠️⚠️ Moi experimental.<0/>Gárdase nas notas do teu propio perfil. As notasdo perfil (privadas) normalmente úsanse para outras contas e están ocultas no teu perfil."

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "Nota: Esta ferramenta usa a API do servidor da instancia con sesión iniciada actualmente."

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr "Modo Capa <0>(<1>Texto</1> → <2>████</2>)</0>"

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr "Substitúe texto por bloques, útil para facer capturas de pantalla, por privacidade."

#: src/pages/settings.jsx:710
msgid "About"
msgstr "Sobre"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr "<0>Creado</0> por <1>@cheeaun</1>"

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "Patrocinios"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Doar"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr "Novidades"

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "Política de Privacidade"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>Web:</0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>Versión:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "Copiouse o número de versión"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr "Non se copiou a cadea coa versión"

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr "Fallou a actualización da subscrición. Inténtao outra vez."

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr "Fallou a retirada da subscrición. Inténtao outra vez."

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "Notificacións Push (beta)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr "As notificacións Push están bloqueadas. Actívaas nos axustes do teu navegador."

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "Permitir de <0>{0}</0>"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "calquera"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "persoas que sigo"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "seguidoras"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "Segue"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "Enquisas"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "Edicións de publicacións"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr "Non se concedeu o permiso para Push desde o último acceso. Terás que <0><1>acceder</1> outra vez para conceder o permiso</0>."

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "NOTA: As notificacións Push só funcionan para <0>unha conta</0>."

#. js-lingui-explicit-id
#: src/pages/status.jsx:658
#: src/pages/status.jsx:1221
msgid "post.title"
msgstr "Publicación"

#: src/pages/status.jsx:912
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "Non iniciaches sesión. Non é posible interactuar (responder, promover, etc)."

#: src/pages/status.jsx:932
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "Esta publicación procede de outra instancia (<0>{instance}</0>). Non é posible interaccionar (responder, promover, etc)."

#: src/pages/status.jsx:960
msgid "Error: {e}"
msgstr "Erro: {e}"

#: src/pages/status.jsx:967
msgid "Switch to my instance to enable interactions"
msgstr "Cambiar á miña instancia para poder interactuar"

#: src/pages/status.jsx:1069
msgid "Unable to load replies."
msgstr "Non se puideron cargar as respostas."

#: src/pages/status.jsx:1181
msgid "Back"
msgstr "Atrás"

#: src/pages/status.jsx:1212
msgid "Go to main post"
msgstr "Ir á publicación principal"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1235
msgid "{0} posts above ‒ Go to top"
msgstr "{0} publicacións máis arriba ― Ir arriba"

#: src/pages/status.jsx:1282
#: src/pages/status.jsx:1345
msgid "Switch to Side Peek view"
msgstr "Cambiar a Vista Lateral con detalle"

#: src/pages/status.jsx:1346
msgid "Switch to Full view"
msgstr "Cambiar a Vista completa"

#: src/pages/status.jsx:1364
msgid "Show all sensitive content"
msgstr "Mostrar todo o contido sensible"

#: src/pages/status.jsx:1369
msgid "Experimental"
msgstr "Experimental"

#: src/pages/status.jsx:1378
msgid "Unable to switch"
msgstr "Non se puido cambiar"

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1385
msgid "Switch to post's instance ({0})"
msgstr "Cambiar á instancia ({0}) da publicación"

#: src/pages/status.jsx:1388
msgid "Switch to post's instance"
msgstr "Cambiar á instancia da publicación"

#: src/pages/status.jsx:1446
msgid "Unable to load post"
msgstr "Non se puido cargar a publicación"

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1582
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, one {# resposta} other {<0>{1}</0> respostas}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1600
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, one {# comentario} other {<0>{0}</0> comentarios}}"

#: src/pages/status.jsx:1622
msgid "View post with its replies"
msgstr "Ver publicación coas suas respostas"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "En voga en ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "Novas populares"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr "Por {0}"

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "Volver a mostrar publicacións populares"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "A mostrar publicacións que mencionan <0>{0}</0>"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "Publicacións populares"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "Sen publicacións en voga."

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "Un cliente web minimalista para Mastodon que fai as cousas ao seu xeito."

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Accede con Mastodon"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "Crear conta"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "Conecta a túa conta Mastodon/Fediverso. <0/>As credenciais non se gardan neste servidor."

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr "<0>Creado</0> por <1>@cheeaun</1>. <2>Política de Privacidade</2>."

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "Captura de pantalla do Carrusel de Promocións"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "Carrusel de Promocións"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "Separa visualmente as publicacións orixinais daquelas que foron compartidas (publicacións promovidas)."

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr "Captura de pantalla dos comentarios agrupados do fío"

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr "Fío cos comentarios agrupados"

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "Sigue as conversas fácilmente. Respostas semicontraídas."

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr "Captura de pantalla das notificacións agrupadas"

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "Notificacións agrupadas"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr "As notificacións que se parecen agrúpanse e contráense para reducir o barullo."

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "Captura de pantalla da interface con varias columnas"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "Unha ou Varias columnas"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "Para quen busca tranquilidade, por defecto só temos unha columna. Se o precisas podes engadir varias."

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "Captura de pantalla dunha cronoloxía con varios cancelos co formulario para engadir máis cancelos"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "Cronoloxía con varios cancelos"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "Podes combinar ata 5 cancelos na mesma cronoloxía."

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "Semella que o teu navegador está a bloquear xanelas emerxentes."

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr "Actualmente hai un borrador minimizado. Publícao ou desbótao antes de crear un novo."

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr "Actualmente hai unha publicación aberta. Publícaa ou desbótaa antes de crear unha nova."

