msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: es\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-08-05 13:53\n"
"Last-Translator: \n"
"Language-Team: Spanish\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: es-ES\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:141
msgid "Locked"
msgstr "Bloqueado"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:163
msgid "Posts: {0}"
msgstr "Publicaciones: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:168
msgid "Last posted: {0}"
msgstr "Última publicación: {0}"

#: src/components/account-block.jsx:183
#: src/components/account-info.jsx:705
msgid "Automated"
msgstr "Automatizado"

#: src/components/account-block.jsx:190
#: src/components/account-info.jsx:710
#: src/components/status.jsx:748
msgid "Group"
msgstr "Grupo"

#: src/components/account-block.jsx:200
msgid "Mutual"
msgstr "Mutuo"

#: src/components/account-block.jsx:204
#: src/components/account-info.jsx:1907
msgid "Requested"
msgstr "Solicitado"

#: src/components/account-block.jsx:208
#: src/components/account-info.jsx:1898
msgid "Following"
msgstr "Siguiendo"

#: src/components/account-block.jsx:212
#: src/components/account-info.jsx:1212
msgid "Follows you"
msgstr "Te sigue"

#: src/components/account-block.jsx:220
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, one {# seguidor} other {# seguidores}}"

#: src/components/account-block.jsx:229
#: src/components/account-info.jsx:753
msgid "Verified"
msgstr "Verificado"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:244
#: src/components/account-info.jsx:892
msgid "Joined <0>{0}</0>"
msgstr "Se unió el <0>{0}</0>"

#: src/components/account-info.jsx:64
msgid "Forever"
msgstr "Siempre"

#: src/components/account-info.jsx:398
msgid "Unable to load account."
msgstr "No se ha podido cargar la cuenta."

#: src/components/account-info.jsx:413
msgid "Go to account page"
msgstr "Ir a la página de la cuenta"

#: src/components/account-info.jsx:442
#: src/components/account-info.jsx:775
msgid "Followers"
msgstr "Seguidores"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:446
#: src/components/account-info.jsx:830
msgid "following.stats"
msgstr "Seguidos"

#: src/components/account-info.jsx:449
#: src/pages/account-statuses.jsx:495
#: src/pages/search.jsx:345
#: src/pages/search.jsx:492
msgid "Posts"
msgstr "Publicaciones"

#: src/components/account-info.jsx:457
#: src/components/account-info.jsx:1268
#: src/components/compose.jsx:2819
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1998
#: src/components/status.jsx:2015
#: src/components/status.jsx:2140
#: src/components/status.jsx:2769
#: src/components/status.jsx:2772
#: src/pages/account-statuses.jsx:539
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1306
#: src/pages/trending.jsx:474
msgid "More"
msgstr "Más"

#: src/components/account-info.jsx:469
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> ha indicado que su nueva cuenta es:"

#: src/components/account-info.jsx:614
#: src/components/account-info.jsx:1496
msgid "Handle copied"
msgstr "Identificador copiado"

#: src/components/account-info.jsx:617
#: src/components/account-info.jsx:1499
msgid "Unable to copy handle"
msgstr "No se ha podido copiar el identificador"

#: src/components/account-info.jsx:623
#: src/components/account-info.jsx:1505
msgid "Copy handle"
msgstr "Copiar identificador"

#: src/components/account-info.jsx:629
msgid "Go to original profile page"
msgstr "Ir a la página de perfil original"

#: src/components/account-info.jsx:647
msgid "View profile image"
msgstr "Ver imagen del perfil"

#: src/components/account-info.jsx:665
msgid "View profile header"
msgstr "Ver cabecera del perfil"

#: src/components/account-info.jsx:681
#: src/components/account-info.jsx:1806
#: src/components/account-info.jsx:2332
msgid "Edit profile"
msgstr "Editar perfil"

#: src/components/account-info.jsx:700
msgid "In Memoriam"
msgstr "Cuenta conmemorativa"

#: src/components/account-info.jsx:782
#: src/components/account-info.jsx:840
msgid "This user has chosen to not make this information available."
msgstr "Este usuario ha optado por no hacer esta información disponible."

#. placeholder {0}: shortenNumber(followersCount)
#. placeholder {1}: shortenNumber(followersCount)
#: src/components/account-info.jsx:802
msgid "{followersCount, plural, one {<0>{0}</0> Follower} other {<1>{1}</1> Followers}}"
msgstr "{followersCount, plural,one {<0>{0}</0> Seguidor}other {<1>{1}</1> Seguidores}}"

#. placeholder {0}: shortenNumber(followingCount)
#: src/components/account-info.jsx:846
msgid "{followingCount, plural, other {<0>{0}</0> Following}}"
msgstr "{followingCount, plural,other {<0>{0}</0> Siguiendo}}"

#. placeholder {0}: shortenNumber(statusesCount)
#. placeholder {1}: shortenNumber(statusesCount)
#: src/components/account-info.jsx:870
msgid "{statusesCount, plural, one {<0>{0}</0> Post} other {<1>{1}</1> Posts}}"
msgstr "{statusesCount, plural,one {<0>{0}</0> Publicación} other {<1>{1}</1> Publicaciones}}"

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:917
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0} originales, {1} respuestas, {2} impulsos"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:933
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural, one {{1, plural, one {Última publicación en el último día} other {Última publicación en los últimos {2} días}}} other {{3, plural, one {Últimas {4} publicaciones en el último día} other {Últimas {5} publicaciones en los últimos {6} días}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:949
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {Última publicación en el último año(s)} other {Últimas {1} publicaciones en el último año(s)}}"

#: src/components/account-info.jsx:974
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "Originales"

#: src/components/account-info.jsx:978
#: src/components/status.jsx:2553
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1025
#: src/pages/status.jsx:1652
msgid "Replies"
msgstr "Respuestas"

#: src/components/account-info.jsx:982
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "Impulsos"

#: src/components/account-info.jsx:988
msgid "Post stats unavailable."
msgstr "Las estadísticas de las publicaciones no están disponibles."

#: src/components/account-info.jsx:1019
msgid "View post stats"
msgstr "Ver las estadísticas de las publicaciones"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1216
msgid "Last post: <0>{0}</0>"
msgstr "Última publicación: <0>{0}</0>"

#: src/components/account-info.jsx:1230
msgid "Muted"
msgstr "Silenciado"

#: src/components/account-info.jsx:1235
msgid "Blocked"
msgstr "Bloqueado"

#: src/components/account-info.jsx:1244
msgid "Private note"
msgstr "Nota privada"

#: src/components/account-info.jsx:1301
msgid "Mention <0>@{username}</0>"
msgstr "Mencionar a <0>@{username}</0>"

#: src/components/account-info.jsx:1313
msgid "Translate bio"
msgstr "Traducir biografía"

#: src/components/account-info.jsx:1324
msgid "Edit private note"
msgstr "Editar nota privada"

#: src/components/account-info.jsx:1324
msgid "Add private note"
msgstr "Añadir nota privada"

#: src/components/account-info.jsx:1344
msgid "Notifications enabled for @{username}'s posts."
msgstr "Notificaciones activadas para las publicaciones de @{username}."

#: src/components/account-info.jsx:1345
msgid " Notifications disabled for @{username}'s posts."
msgstr " Notificaciones desactivadas para las publicaciones de @{username}."

#: src/components/account-info.jsx:1357
msgid "Disable notifications"
msgstr "Desactivar notificaciones"

#: src/components/account-info.jsx:1358
msgid "Enable notifications"
msgstr "Activar notificaciones"

#: src/components/account-info.jsx:1375
msgid "Boosts from @{username} enabled."
msgstr "Impulsos de @{username} activados."

#: src/components/account-info.jsx:1376
msgid "Boosts from @{username} disabled."
msgstr "Impulsos de @{username} desactivados."

#: src/components/account-info.jsx:1387
msgid "Disable boosts"
msgstr "Desactivar impulsos"

#: src/components/account-info.jsx:1387
msgid "Enable boosts"
msgstr "Activar impulsos"

#: src/components/account-info.jsx:1406
msgid "@{username} is no longer featured on your profile."
msgstr "@{username} ya no está destacado en tu perfil."

#: src/components/account-info.jsx:1416
msgid "@{username} is now featured on your profile."
msgstr "@{username} ahora está destacado en tu perfil."

#: src/components/account-info.jsx:1424
msgid "Unable to unfeature @{username} on your profile."
msgstr "No se puede dejar de destacar a @{username} en tu perfil."

#: src/components/account-info.jsx:1428
msgid "Unable to feature @{username} on your profile."
msgstr "No se puede destacar a @{username} en tu perfil."

#: src/components/account-info.jsx:1437
msgid "Don't feature on profile"
msgstr "No destacar en el perfil"

#: src/components/account-info.jsx:1438
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "Destacar en el perfil"

#: src/components/account-info.jsx:1447
msgid "Show featured profiles"
msgstr "Mostrar perfiles destacados"

#: src/components/account-info.jsx:1462
#: src/components/account-info.jsx:1472
#: src/components/account-info.jsx:2078
msgid "Add/Remove from Lists"
msgstr "Añadir/eliminar de las listas"

#: src/components/account-info.jsx:1522
#: src/components/status.jsx:1420
msgid "Link copied"
msgstr "El enlace ha sido copiado"

#: src/components/account-info.jsx:1525
#: src/components/status.jsx:1423
msgid "Unable to copy link"
msgstr "No se ha podido copiar el enlace"

#: src/components/account-info.jsx:1531
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1429
#: src/components/status.jsx:3547
msgid "Copy"
msgstr "Copiar"

#: src/components/account-info.jsx:1546
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1445
msgid "Sharing doesn't seem to work."
msgstr "Compartir parece no funcionar."

#: src/components/account-info.jsx:1552
#: src/components/status.jsx:1451
msgid "Share…"
msgstr "Compartir…"

#: src/components/account-info.jsx:1572
msgid "Unmuted @{username}"
msgstr "Se ha dejado de silenciar a @{username}"

#: src/components/account-info.jsx:1584
msgid "Unmute <0>@{username}</0>"
msgstr "Dejar de silenciar a <0>@{username}</0>"

#: src/components/account-info.jsx:1600
msgid "Mute <0>@{username}</0>…"
msgstr "Silenciar a <0>@{username}</0>…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1632
msgid "Muted @{username} for {0}"
msgstr "Se ha silenciado a @{username} por {0}"

#: src/components/account-info.jsx:1644
msgid "Unable to mute @{username}"
msgstr "No se ha podido silenciar a @{username}"

#: src/components/account-info.jsx:1665
msgid "Remove <0>@{username}</0> from followers?"
msgstr "¿Deseas eliminar a <0>@{username}</0> de los seguidores?"

#: src/components/account-info.jsx:1685
msgid "@{username} removed from followers"
msgstr "@{username} se ha eliminado de los seguidores"

#: src/components/account-info.jsx:1697
msgid "Remove follower…"
msgstr "Eliminar seguidor…"

#: src/components/account-info.jsx:1708
msgid "Block <0>@{username}</0>?"
msgstr "¿Deseas bloquear a <0>@{username}</0>?"

#: src/components/account-info.jsx:1732
msgid "Unblocked @{username}"
msgstr "Se ha desbloqueado a @{username}"

#: src/components/account-info.jsx:1740
msgid "Blocked @{username}"
msgstr "Se ha bloqueado a @{username}"

#: src/components/account-info.jsx:1748
msgid "Unable to unblock @{username}"
msgstr "No se ha podido desbloquear a @{username}"

#: src/components/account-info.jsx:1750
msgid "Unable to block @{username}"
msgstr "No se ha podido bloquear a @{username}"

#: src/components/account-info.jsx:1760
msgid "Unblock <0>@{username}</0>"
msgstr "Desbloquear a <0>@{username}</0>"

#: src/components/account-info.jsx:1769
msgid "Block <0>@{username}</0>…"
msgstr "Bloquear a <0>@{username}</0>…"

#: src/components/account-info.jsx:1786
msgid "Report <0>@{username}</0>…"
msgstr "Reportar a <0>@{username}</0>…"

#: src/components/account-info.jsx:1842
msgid "Withdraw follow request?"
msgstr "¿Desear retirar la solicitud de seguimiento?"

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1843
msgid "Unfollow @{0}?"
msgstr "¿Dejar de seguir a @{0}?"

#: src/components/account-info.jsx:1901
msgid "Unfollow…"
msgstr "Dejar de seguir…"

#: src/components/account-info.jsx:1910
msgid "Withdraw…"
msgstr "Descartar…"

#: src/components/account-info.jsx:1917
#: src/components/account-info.jsx:1921
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "Seguir"

#: src/components/account-info.jsx:2018
#: src/components/account-info.jsx:2073
#: src/components/account-info.jsx:2207
#: src/components/account-info.jsx:2327
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:892
#: src/components/compose.jsx:2775
#: src/components/compose.jsx:3255
#: src/components/compose.jsx:3464
#: src/components/compose.jsx:3694
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:118
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3271
#: src/components/status.jsx:3511
#: src/components/status.jsx:4020
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1393
msgid "Close"
msgstr "Cerrar"

#: src/components/account-info.jsx:2023
msgid "Translated Bio"
msgstr "Biografía traducida"

#: src/components/account-info.jsx:2118
msgid "Unable to remove from list."
msgstr "No se ha podido eliminar de la lista."

#: src/components/account-info.jsx:2119
msgid "Unable to add to list."
msgstr "No se ha podido añadir a la lista."

#: src/components/account-info.jsx:2138
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "No se ha podido cargar las listas."

#: src/components/account-info.jsx:2142
msgid "No lists."
msgstr "No hay listas."

#: src/components/account-info.jsx:2153
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "Nueva lista"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2212
msgid "Private note about <0>@{0}</0>"
msgstr "Nota privada acerca de <0>@{0}</0>"

#: src/components/account-info.jsx:2242
msgid "Unable to update private note."
msgstr "No se ha podido actualizar la nota privada."

#: src/components/account-info.jsx:2265
#: src/components/account-info.jsx:2563
msgid "Cancel"
msgstr "Cancelar"

#: src/components/account-info.jsx:2270
msgid "Save & close"
msgstr "Guardar y cerrar"

#: src/components/account-info.jsx:2387
msgid "Unable to update profile."
msgstr "No se ha podido actualizar el perfil."

#: src/components/account-info.jsx:2394
msgid "Header picture"
msgstr "Imagen de encabezado"

#: src/components/account-info.jsx:2446
msgid "Profile picture"
msgstr "Imagen de perfil"

#: src/components/account-info.jsx:2498
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "Nombre"

#: src/components/account-info.jsx:2511
msgid "Bio"
msgstr "Biografía"

#: src/components/account-info.jsx:2524
msgid "Extra fields"
msgstr "Campos adicionales"

#: src/components/account-info.jsx:2530
msgid "Label"
msgstr "Etiqueta"

#: src/components/account-info.jsx:2533
msgid "Content"
msgstr "Contenido"

#: src/components/account-info.jsx:2566
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "Guardar"

#: src/components/account-info.jsx:2620
msgid "username"
msgstr "nombre de usuario"

#: src/components/account-info.jsx:2624
msgid "server domain name"
msgstr "nombre de dominio del servidor"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2690
msgid "Profiles featured by @{0}"
msgstr "Perfiles destacados por @{0}"

#: src/components/account-info.jsx:2716
msgid "No featured profiles."
msgstr "No hay perfiles destacados."

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "El modo oculto ha sido desactivado"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "El modo oculto ha sido activado"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "Inicio"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "Redactar"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr "Publicaciones programadas"

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr "Añadir al hilo"

#: src/components/compose.jsx:212
msgid "Take photo or video"
msgstr "Hacer una foto o un vídeo"

#: src/components/compose.jsx:213
msgid "Add media"
msgstr "Añadir multimedia"

#: src/components/compose.jsx:214
msgid "Add custom emoji"
msgstr "Insertar emoji personalizado"

#: src/components/compose.jsx:215
msgid "Add GIF"
msgstr "Añadir GIF"

#: src/components/compose.jsx:216
msgid "Add poll"
msgstr "Añadir una encuesta"

#: src/components/compose.jsx:217
msgid "Schedule post"
msgstr "Programar publicación"

#: src/components/compose.jsx:417
msgid "You have unsaved changes. Discard this post?"
msgstr "Tienes cambios sin guardar. ¿Deseas descartar esta publicación?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:655
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr "{0, plural, one {El archivo {1} no es compatible.} other {Los archivos {2} no son compatibles.}}"

#: src/components/compose.jsx:665
#: src/components/compose.jsx:683
#: src/components/compose.jsx:1795
#: src/components/compose.jsx:1930
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, one {Solo puedes adjuntar hasta 1 archivo.} other {Solo puedes adjuntar hasta # archivos.}}"

#: src/components/compose.jsx:873
msgid "Pop out"
msgstr "Salir"

#: src/components/compose.jsx:880
msgid "Minimize"
msgstr "Minimizar"

#: src/components/compose.jsx:916
msgid "Looks like you closed the parent window."
msgstr "Parece que has cerrado la ventana principal."

#: src/components/compose.jsx:923
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Parece que ya tienes una ventana de edición abierta en la ventana principal y que se está publicando. Por favor, espera a que se publique e inténtalo de nuevo más tarde."

#: src/components/compose.jsx:928
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Parece que ya tienes una ventana de redacción abierta en la ventana principal. Entrando en esta ventana se descartarán los cambios que hiciste. ¿Deseas continuar?"

#: src/components/compose.jsx:971
msgid "Pop in"
msgstr "Mostrar"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:981
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "Respondiendo a la publicación de @{0}(<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:991
msgid "Replying to @{0}’s post"
msgstr "Respondiendo a la publicación de @{0}"

#: src/components/compose.jsx:1004
msgid "Editing source post"
msgstr "Editando la publicación original"

#: src/components/compose.jsx:1057
msgid "Poll must have at least 2 options"
msgstr "La encuesta debe tener al menos 2 opciones"

#: src/components/compose.jsx:1061
msgid "Some poll choices are empty"
msgstr "Algunas opciones de la encuesta están vacías"

#: src/components/compose.jsx:1074
msgid "Some media have no descriptions. Continue?"
msgstr "Algunos archivos no tienen descripciones. ¿Desear continuar?"

#: src/components/compose.jsx:1126
msgid "Attachment #{i} failed"
msgstr "Error al adjuntar #{i}"

#: src/components/compose.jsx:1222
#: src/components/status.jsx:2328
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "Advertencia de contenido"

#: src/components/compose.jsx:1238
msgid "Content warning or sensitive media"
msgstr "Advertencia de contenido o medio sensible"

#: src/components/compose.jsx:1274
#: src/components/status.jsx:99
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "Público"

#: src/components/compose.jsx:1279
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:100
msgid "Local"
msgstr "Local"

#: src/components/compose.jsx:1283
#: src/components/status.jsx:101
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "No listado"

#: src/components/compose.jsx:1286
#: src/components/status.jsx:102
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "Solo seguidores"

#: src/components/compose.jsx:1289
#: src/components/status.jsx:103
#: src/components/status.jsx:2204
msgid "Private mention"
msgstr "Mención privada"

#: src/components/compose.jsx:1298
msgid "Post your reply"
msgstr "Publica tu respuesta"

#: src/components/compose.jsx:1300
msgid "Edit your post"
msgstr "Edita tu publicación"

#: src/components/compose.jsx:1301
msgid "What are you doing?"
msgstr "¿En qué estás pensando?"

#: src/components/compose.jsx:1380
msgid "Mark media as sensitive"
msgstr "Añadir advertencia de contenido"

#: src/components/compose.jsx:1417
msgid "Posting on <0/>"
msgstr "Publicar el <0/>"

#: src/components/compose.jsx:1448
#: src/components/compose.jsx:3313
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "Añadir"

#: src/components/compose.jsx:1676
msgid "Schedule"
msgstr "Programar"

#: src/components/compose.jsx:1678
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1192
#: src/components/status.jsx:1978
#: src/components/status.jsx:1979
#: src/components/status.jsx:2673
msgid "Reply"
msgstr "Responder"

#: src/components/compose.jsx:1680
msgid "Update"
msgstr "Actualizar"

#: src/components/compose.jsx:1681
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Publicar"

#: src/components/compose.jsx:1807
msgid "Downloading GIF…"
msgstr "Descargando GIF…"

#: src/components/compose.jsx:1835
msgid "Failed to download GIF"
msgstr "Hubo un fallo al descargar el GIF"

#: src/components/compose.jsx:2060
#: src/components/compose.jsx:2153
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "Más…"

#: src/components/compose.jsx:2589
msgid "Uploaded"
msgstr "Cargado"

#: src/components/compose.jsx:2602
msgid "Image description"
msgstr "Descripción de la imagen"

#: src/components/compose.jsx:2603
msgid "Video description"
msgstr "Descripción del vídeo"

#: src/components/compose.jsx:2604
msgid "Audio description"
msgstr "Descripción del audio"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2639
#: src/components/compose.jsx:2659
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "El tamaño del archivo es demasiado grande. La carga podría tener problemas. Intenta reducir el tamaño del archivo de {0} a {1} o inferior."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2651
#: src/components/compose.jsx:2671
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr "La dimensión es demasiado grande. La carga podrá tener problemas. Trata de reducir la dimensión de {0}×{1} a {2}×{3}px."

#: src/components/compose.jsx:2679
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "Tasa de fotogramas demasiado alta. La carga podría tener problemas."

#: src/components/compose.jsx:2739
#: src/components/compose.jsx:2989
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "Eliminar"

#: src/components/compose.jsx:2756
#: src/compose.jsx:84
msgid "Error"
msgstr "Error"

#: src/components/compose.jsx:2781
msgid "Edit image description"
msgstr "Editar descripción de la imagen"

#: src/components/compose.jsx:2782
msgid "Edit video description"
msgstr "Editar descripción del vídeo"

#: src/components/compose.jsx:2783
msgid "Edit audio description"
msgstr "Editar descripción del audio"

#: src/components/compose.jsx:2828
#: src/components/compose.jsx:2877
msgid "Generating description. Please wait…"
msgstr "Generando descripción. Por favor, espera…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2848
msgid "Failed to generate description: {0}"
msgstr "No se ha podido generar la descripción: {0}"

#: src/components/compose.jsx:2849
msgid "Failed to generate description"
msgstr "No se ha podido generar la descripción"

#: src/components/compose.jsx:2861
#: src/components/compose.jsx:2867
#: src/components/compose.jsx:2913
msgid "Generate description…"
msgstr "Generar descripción…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2900
msgid "Failed to generate description{0}"
msgstr "No se ha podido generar la descripción{0}"

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2915
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>— experimental</0>"

#: src/components/compose.jsx:2934
msgid "Done"
msgstr "Hecho"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2970
msgid "Choice {0}"
msgstr "Opción {0}"

#: src/components/compose.jsx:3017
msgid "Multiple choices"
msgstr "Selección múltiple"

#: src/components/compose.jsx:3020
msgid "Duration"
msgstr "Duración"

#: src/components/compose.jsx:3051
msgid "Remove poll"
msgstr "Eliminar encuesta"

#: src/components/compose.jsx:3272
msgid "Search accounts"
msgstr "Buscar cuentas"

#: src/components/compose.jsx:3326
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "Error al cargar cuentas"

#: src/components/compose.jsx:3470
msgid "Custom emojis"
msgstr "Emojis personalizados"

#: src/components/compose.jsx:3490
msgid "Search emoji"
msgstr "Buscar emoji"

#: src/components/compose.jsx:3521
msgid "Error loading custom emojis"
msgstr "Error al cargar emojis personalizados"

#: src/components/compose.jsx:3532
msgid "Recently used"
msgstr "Usados recientemente"

#: src/components/compose.jsx:3533
msgid "Others"
msgstr "Otros"

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3571
msgid "{0} more…"
msgstr "{0} más…"

#: src/components/compose.jsx:3709
msgid "Search GIFs"
msgstr "Buscar GIF"

#: src/components/compose.jsx:3724
msgid "Powered by GIPHY"
msgstr "Con tecnología de GIPHY"

#: src/components/compose.jsx:3732
msgid "Type to search GIFs"
msgstr "Escribe para buscar un GIF"

#: src/components/compose.jsx:3830
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "Anterior"

#: src/components/compose.jsx:3848
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "Siguiente"

#: src/components/compose.jsx:3865
msgid "Error loading GIFs"
msgstr "Error al cargar los GIF"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "Borradores no enviados"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "Parece que tienes borradores sin enviar. Prosigamos donde lo dejaste."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "¿Deseas eliminar este borrador?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "¡Error al eliminar el borrador! Por favor, inténtalo de nuevo."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1595
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "Eliminar…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "¡Error al obtener el estado de la respuesta!"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "¿Deseas eliminar todos los borradores?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "¡Error al eliminar borradores! Por favor, inténtalo de nuevo."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "Eliminar todo…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "No se han encontrado borradores."

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "Encuesta"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:375
msgid "Media"
msgstr "Multimedia"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "Abrir en una nueva ventana"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "Aceptar"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "Rechazar"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "Aceptado"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "Rechazado"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:335
#: src/pages/search.jsx:368
msgid "Accounts"
msgstr "Cuentas"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:562
#: src/pages/status.jsx:1426
msgid "Show more…"
msgstr "Mostrar más…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:567
msgid "The end."
msgstr "El fin."

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "No hay nada que mostrar"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "Atajos de teclado"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "Ayuda de los atajos de teclado"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "Siguiente publicación"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "Anterior publicación"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "Saltar carrusel a la siguiente publicación"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Mayús</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "Saltar carrusel a la anterior publicación"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Mayús</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "Cargar nuevas publicaciones"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "Abrir detalles de la publicación"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Intro</0> u <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "Expandir advertencia de contenido o<0/>alternar hilo ampliado/colapsado"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "Cerrar publicación o diálogos"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc</0> o <1>Retroceso</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "Enfocar columna en el modo de múltiples columnas"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> a <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr "Enfocarse en la siguiente columna en el modo multicolumna"

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr "Enfocarse en la anterior columna en el modo multicolumna"

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "Redactar nueva publicación"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "Redactar nueva publicación (nueva ventana)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Mayús</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "Enviar publicación"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Ctrl</0> + <1>Intro</1> o <2>⌘</2> + <3>Intro</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:204
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:47
#: src/pages/search.jsx:317
msgid "Search"
msgstr "Buscar"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "Responder (nueva ventana)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Mayús</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "Me gusta (favorito)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> o <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1200
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
#: src/components/status.jsx:2724
msgid "Boost"
msgstr "Impulsar"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Mayús</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
#: src/components/status.jsx:2749
msgid "Bookmark"
msgstr "Añadir marcador"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "Activar o desactivar el modo oculto"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Mayús</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "Editar lista"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "No se ha podido editar la lista."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "No se ha podido crear la lista."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "Mostrar respuestas a la lista de miembros"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "Mostrar respuestas a las personas que sigo"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "No mostrar respuestas"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "Ocultar publicaciones en esta lista de Inicio/Seguidos"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "Crear"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "¿Deseas eliminar esta lista?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "No se ha podido eliminar la lista."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr "Las publicaciones en esta lista están ocultas en Inicio/Seguidos"

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "Descripción de la imagen"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1306
#: src/components/status.jsx:1315
#: src/components/translation-block.jsx:239
msgid "Translate"
msgstr "Traducir"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1334
msgid "Speak"
msgstr "Hablar"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "Abrir archivo original en una nueva ventana"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "Abrir archivo original"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "Intentando describir la imagen. Por favor, espera…"

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "No se ha podido describir la imagen"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "Describe la imagen…"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "Ver publicaciones"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "Imagen sensible"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "Filtrado: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3850
#: src/components/status.jsx:3946
#: src/components/status.jsx:4024
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "Filtrado"

#: src/components/media.jsx:479
msgid "Open file"
msgstr "Abrir archivo"

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr "Publicación programada"

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "Publicado. Échale un vistazo."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr "Respuesta programada"

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "Respuesta publicada. Échale un vistazo."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "Publicación actualizada. Échale un vistazo."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "Menú"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "¿Recargar página ahora para actualizar?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "Hay una nueva actualización disponible…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr "Seguidos"

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "Ponerse al día"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "Menciones"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "Notificaciones"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "Nuevo"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "Perfil"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "Marcadores"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "Me gustan"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "Etiquetas seguidas"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:341
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "Filtros"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "Usuarios silenciados"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "Usuarios silenciados…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "Usuarios bloqueados"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "Usuarios bloqueados…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "Cuentas…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:199
#: src/pages/status.jsx:925
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "Inicia sesión"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "Tendencias"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "Federados"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "Atajos / Columnas…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "Configuración…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "Listas"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "Todas las listas"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "Notificación"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "Esta notificación proviene de tu otra cuenta."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "Ver todas las notificaciones"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} reaccionó a tu publicación con {emojiObject}"

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} ha hecho una publicación."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} impulsó tu respuesta.} other {{account} impulsó tu publicación.}}} other {{account} impulsó {postsCount} de tus publicaciones.}}} other {{postType, select, reply {<0><1>{0}</1> personas</0> impulsaron tu respuesta.} other {<2><3>{1}</3> persona</2> impulsaron tu publicación.}}}}"

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, =1 {{account} te comenzó a seguir.} other {<0><1>{0}</1> personas</0> te comenzaron a seguir.}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} ha solicitado seguirte."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {A {account} le gustó tu respuesta.} other {A {account} le gustó tu publicación.}}} other {A {account} le gustó {postsCount} de tus publicaciones.}}} other {{postType, select, reply {A <0><1>{0}</1> personas</0> les gustó tu respuesta.} other {A <2><3>{1}</3> personas</2> les gustó tu publicación.}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "Una encuesta en la que has votado o creado ha finalizado."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "Una encuesta que has creado ha finalizado."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "Una encuesta en la que has votado ha finalizado."

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "Una publicación con la que interactuaste ha sido editada."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} impulsó y le gustó tu respuesta.} other {{account} impulsó y le gustó tu publicación.}}} other {{account} impulsó y le gustó {postsCount} de tus publicaciones.}}} other {{postType, select, reply {<0><1>{0}</1> personas</0> impulsaron y les gustó tu respuesta.} other {<2><3>{1}</3> personas</2> impulsaron y les gustó tu publicación.}}}}"

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} registrado."

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} reportó {targetAccount}"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "Conexiones perdidas con <0>{name}</0>."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "Advertencia de moderación"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr "¡Tu #Wrapstodon {year} está aquí!"

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "Un administrador de <0>{from}</0> ha suspendido <1>{targetName}</1>, lo que significa que ya no puedes recibir actualizaciones de sus cuentas o interactuar con ellas."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "Un administrador de <0>{from}</0> ha bloqueado <1>{targetName}</1>. Seguidores afectados: {followersCount}, siguiendo: {followingCount}."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "Has bloqueado <0>{targetName}</0>. Seguidores eliminados: {followersCount}, siguiendo: {followingCount}."

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "Tu cuenta ha recibido un aviso de moderación."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "Tu cuenta ha sido desactivada."

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "Se han marcado como sensibles algunas de tus publicaciones."

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "Algunas de tus publicaciones han sido eliminadas."

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "De ahora en adelante, todas tus publicaciones se marcarán como sensibles."

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "Tu cuenta ha sido limitada."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "Tu cuenta ha sido suspendida."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[Tipo de notificación desconocido: {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1277
#: src/components/status.jsx:1287
msgid "Boosted/Liked by…"
msgstr "Impulsado por/Le gustó a…"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "Le gusta a…"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "Impulsado por…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "Seguido por…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "Más información <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr "Ver #Wrapstodon"

#: src/components/notification.jsx:801
#: src/components/status.jsx:486
msgid "Read more →"
msgstr "Leer más →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "Votado"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr "{optionVotesCount, plural, one {# voto} other {# votos}}"

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "Ocultar resultados"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "Votar"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1295
#: src/pages/status.jsx:1318
msgid "Refresh"
msgstr "Actualizar"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "Mostrar resultado"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> voto} other {<1>{1}</1> votos}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, one {<0>{0}</0> votante} other {<1>{1}</1> votantes}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "Terminado <0/>"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "Terminado"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "Terminando <0/>"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "Terminando"

#: src/components/recent-searches.jsx:27
msgid "Cleared recent searches"
msgstr "Búsquedas recientes limpiadas"

#: src/components/recent-searches.jsx:49
#: src/components/search-form.jsx:328
msgid "Recent searches"
msgstr "Búsquedas recientes"

#: src/components/recent-searches.jsx:59
msgid "Clear all"
msgstr "Limpiar todo"

#: src/components/recent-searches.jsx:94
#: src/pages/account-statuses.jsx:334
msgid "Clear"
msgstr "Limpiar"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0}s"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0}m"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0}h"

#: src/components/report-modal.jsx:30
msgid "Spam"
msgstr "Spam"

#: src/components/report-modal.jsx:31
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "Enlaces maliciosos, interacciones falsas o respuestas repetitivas"

#: src/components/report-modal.jsx:34
msgid "Illegal"
msgstr "Ilegal"

#: src/components/report-modal.jsx:35
msgid "Violates the law of your or the server's country"
msgstr "Viola la ley de tu país o del servidor"

#: src/components/report-modal.jsx:38
msgid "Server rule violation"
msgstr "Violación de regla del servidor"

#: src/components/report-modal.jsx:39
msgid "Breaks specific server rules"
msgstr "Incumple normas específicas del servidor"

#: src/components/report-modal.jsx:40
msgid "Violation"
msgstr "Violación"

#: src/components/report-modal.jsx:43
msgid "Other"
msgstr "Otros"

#: src/components/report-modal.jsx:44
msgid "Issue doesn't fit other categories"
msgstr "El problema no encaja en otras categorías"

#: src/components/report-modal.jsx:111
msgid "Report Post"
msgstr "Reportar esta publicación"

#: src/components/report-modal.jsx:111
msgid "Report @{username}"
msgstr "Reportar a @{username}"

#: src/components/report-modal.jsx:147
msgid "Pending review"
msgstr "Revisiones pendientes"

#: src/components/report-modal.jsx:189
msgid "Post reported"
msgstr "Publicación reportada"

#: src/components/report-modal.jsx:189
msgid "Profile reported"
msgstr "Perfil reportado"

#: src/components/report-modal.jsx:197
msgid "Unable to report post"
msgstr "No se ha podido reportar la publicación"

#: src/components/report-modal.jsx:198
msgid "Unable to report profile"
msgstr "No se ha podido reportar el perfil"

#: src/components/report-modal.jsx:206
msgid "What's the issue with this post?"
msgstr "¿Cuál es el problema con esta publicación?"

#: src/components/report-modal.jsx:207
msgid "What's the issue with this profile?"
msgstr "¿Cuál es el problema con este perfil?"

#: src/components/report-modal.jsx:276
msgid "Additional info"
msgstr "Información adicional"

#: src/components/report-modal.jsx:299
msgid "Forward to <0>{domain}</0>"
msgstr "Enviar a <0>{domain}</0>"

#: src/components/report-modal.jsx:309
msgid "Send Report"
msgstr "Enviar reporte"

#: src/components/report-modal.jsx:318
msgid "Muted {username}"
msgstr "Silenciar a {username}"

#: src/components/report-modal.jsx:321
msgid "Unable to mute {username}"
msgstr "No se ha podido silenciar a {username}"

#: src/components/report-modal.jsx:326
msgid "Send Report <0>+ Mute profile</0>"
msgstr "Enviar reporte <0>+ Silenciar perfil</0>"

#: src/components/report-modal.jsx:337
msgid "Blocked {username}"
msgstr "Bloquear a @{username}"

#: src/components/report-modal.jsx:340
msgid "Unable to block {username}"
msgstr "No se ha podido bloquear a {username}"

#: src/components/report-modal.jsx:345
msgid "Send Report <0>+ Block profile</0>"
msgstr "Enviar reporte <0>+ Bloquear perfil</0>"

#: src/components/search-form.jsx:18
msgid "Posts with <0>{query}</0>"
msgstr "Publicaciones con <0>{query}</0>"

#: src/components/search-form.jsx:26
msgid "Accounts with <0>{query}</0>"
msgstr "Cuentas con <0>{query}</0>"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:34
msgid "Posts tagged with <0>#{0}</0>"
msgstr "Publicaciones etiquetadas con <0>#{0}</0>"

#: src/components/search-form.jsx:43
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>‒ cuentas, etiquetas y publicaciones</0>"

#: src/components/search-form.jsx:135
msgid "Look up <0>{query}</0>"
msgstr "Buscar <0>{query}</0>"

#: src/components/search-form.jsx:361
#: src/pages/home.jsx:251
msgid "See all"
msgstr "Ver todo"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "Inicio / Seguidos"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "Público (Local / Federado)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "Cuenta"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "Etiqueta"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "ID de lista"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "Solo local"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:203
msgid "Instance"
msgstr "Instancia"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "Opcional, por ejemplo, mastodon.social"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "Buscar término"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "Opcional, excepto para el modo de múltiples columnas"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "p. ej. PixelArt (Max 5, separada por espacios)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "Solo multimedia"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "Atajos"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "versión beta"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "Especifica una lista de atajos que aparecerán como:"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "Botón flotante"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "Pestaña/Barra de menú"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "Múltiples columnas"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "No está disponible en el modo de vista actual"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "Subir"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "Bajar"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1557
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "Editar"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "Añade más de un atajo/columna para hacer que esto funcione."

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "No hay columnas todavía. Pulsa en el botón Añadir columna."

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "No hay atajos todavía. Pulsa en el botón Añadir atajo."

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "¿No estás seguro de qué añadir?<0/>Prueba a añadir <1>Inicio / Seguidos y Notificaciones</1> primero."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "Máx. de columnas {SHORTCUTS_LIMIT}"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "Max. de atajos {SHORTCUTS_LIMIT}"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "Importar/exportar"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "Añadir columna…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "Añadir atajo…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "Especificar una lista es opcional. Para el modo de múltiples columnas, la lista es obligatoria, de lo contrario la columna no se mostrará."

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "Para el modo de múltiples columnas, es necesario un término de búsqueda; sin él, no se mostrará la columna."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "Se permiten múltiples etiquetas, separadas por espacios."

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "Editar atajo"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "Añadir atajo"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "Línea de tiempo"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "Lista"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "Importar/Exportar <0>Atajos</0>"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "Importar"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "Pegar atajos aquí"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "Descargando atajos guardados desde el servidor de instancia…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "No se han podido descargar los atajos"

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "Descargando atajos guardados desde el servidor de la instancia"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "* Existe en los atajos actuales"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "La lista puede no funcionar si es de una cuenta diferente."

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "Formato de ajustes inválido"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "¿Deseas añadir a los atajos actuales?"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr "Solo se agregarán los atajos que no existan en los actuales atajos."

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "No hay nuevos atajos para importar"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "Los atajos han sido importados. Se ha excedido el máximo {SHORTCUTS_LIMIT}, por lo que el resto no serán importados."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "Atajos importados"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "Importar y añadir…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "¿Deseas anular los atajos actuales?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "¿Deseas importar atajos?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "o sobreescribir…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "Importar…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "Exportar"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "Los atajos han sido copiados"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "No se ha podido copiar los atajos"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "Ajustes de los atajos copiados"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "No se ha podido copiar los ajustes de los atajos"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "Compartir"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "Guardando atajos a la instancia del servidor…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "Los atajos han sido guardados"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "No se ha podido guardar los atajos"

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "Sincronizar al servidor de instancia"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0,plural, one{# carácter} other{# caracteres}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "No hay más atajos que importar"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "Importar / exportar ajustes del / hacia el servidor de la instancia (En fase muy experimental)"

#: src/components/status.jsx:351
msgid "Unable to format math"
msgstr "No se pudo dar formato matemático"

#: src/components/status.jsx:365
msgid "Math expressions found."
msgstr "Expresiones matemáticas encontradas."

#. Action to switch from rendered math back to raw (LaTeX) markup
#: src/components/status.jsx:368
msgid "Show markup"
msgstr "Mostrar anotaciones"

#. Action to render math expressions from raw (LaTeX) markup
#: src/components/status.jsx:373
msgid "Format math"
msgstr "Formato matemático"

#: src/components/status.jsx:772
msgid "<0/> <1>boosted</1>"
msgstr "<0/> <1>impulsó</1>"

#: src/components/status.jsx:875
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "Lo sentimos, la instancia en la que estás conectado no te permite interactuar con esta publicación de otra instancia."

#. placeholder {0}: username || acct
#: src/components/status.jsx:1029
msgid "Unliked @{0}'s post"
msgstr "No me gustó la publicación de {0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1030
msgid "Liked @{0}'s post"
msgstr "Le gustó la publicación de @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1069
msgid "Unbookmarked @{0}'s post"
msgstr "Dejó de guardar como marcador la publicación de @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1070
msgid "Bookmarked @{0}'s post"
msgstr "Guardó como marcador la publicación de @{0}"

#: src/components/status.jsx:1169
msgid "Some media have no descriptions."
msgstr "Algunos archivos no tienen descripciones."

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1176
msgid "Old post (<0>{0}</0>)"
msgstr "Antiguas publicaciones (<0>{0}</0>)"

#: src/components/status.jsx:1200
#: src/components/status.jsx:1240
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
msgid "Unboost"
msgstr "Dejar de impulsar"

#: src/components/status.jsx:1216
#: src/components/status.jsx:2715
msgid "Quote"
msgstr "Cita"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1228
#: src/components/status.jsx:1694
msgid "Unboosted @{0}'s post"
msgstr "Dejó de impulsar la publicación de @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1229
#: src/components/status.jsx:1695
msgid "Boosted @{0}'s post"
msgstr "Impulsó la publicación de @{0}"

#: src/components/status.jsx:1241
msgid "Boost…"
msgstr "Impulsar…"

#: src/components/status.jsx:1253
#: src/components/status.jsx:1988
#: src/components/status.jsx:2736
msgid "Unlike"
msgstr "No me gusta"

#: src/components/status.jsx:1254
#: src/components/status.jsx:1988
#: src/components/status.jsx:1989
#: src/components/status.jsx:2736
#: src/components/status.jsx:2737
msgid "Like"
msgstr "Me gusta"

#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
msgid "Unbookmark"
msgstr "Quitar marcador"

#: src/components/status.jsx:1346
msgid "Post text copied"
msgstr "Texto de la publicación copiado"

#: src/components/status.jsx:1349
msgid "Unable to copy post text"
msgstr "No se puede copiar el texto de la publicación"

#: src/components/status.jsx:1355
msgid "Copy post text"
msgstr "Copiar el texto de la publicación"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1373
msgid "View post by <0>@{0}</0>"
msgstr "Ver publicación de <0>@{0}</0>"

#: src/components/status.jsx:1394
msgid "Show Edit History"
msgstr "Mostrar historial de ediciones"

#: src/components/status.jsx:1397
msgid "Edited: {editedDateText}"
msgstr "Se editó el: {editedDateText}"

#: src/components/status.jsx:1464
#: src/components/status.jsx:3516
msgid "Embed post"
msgstr "Insertar publicación"

#: src/components/status.jsx:1478
msgid "Conversation unmuted"
msgstr "Has dejado de silenciar la conversación"

#: src/components/status.jsx:1478
msgid "Conversation muted"
msgstr "Has silenciado la conversación"

#: src/components/status.jsx:1484
msgid "Unable to unmute conversation"
msgstr "No se ha podido dejar de silenciar la conversación"

#: src/components/status.jsx:1485
msgid "Unable to mute conversation"
msgstr "No se ha podido silenciar la conversación"

#: src/components/status.jsx:1494
msgid "Unmute conversation"
msgstr "Dejar de silenciar conversación"

#: src/components/status.jsx:1501
msgid "Mute conversation"
msgstr "Silenciar conversación"

#: src/components/status.jsx:1517
msgid "Post unpinned from profile"
msgstr "Publicación no fijada del perfil"

#: src/components/status.jsx:1518
msgid "Post pinned to profile"
msgstr "Publicación fijada en el perfil"

#: src/components/status.jsx:1523
msgid "Unable to unpin post"
msgstr "No se ha podido dejar sin fijar la publicación"

#: src/components/status.jsx:1523
msgid "Unable to pin post"
msgstr "No se ha podido fijar la publicación"

#: src/components/status.jsx:1532
msgid "Unpin from profile"
msgstr "Dejar de fijar del perfil"

#: src/components/status.jsx:1539
msgid "Pin to profile"
msgstr "Fijar al perfil"

#: src/components/status.jsx:1568
msgid "Delete this post?"
msgstr "¿Deseas eliminar esta publicación?"

#: src/components/status.jsx:1584
msgid "Post deleted"
msgstr "Se ha eliminado la publicación"

#: src/components/status.jsx:1587
msgid "Unable to delete post"
msgstr "No se ha podido eliminar la publicación"

#: src/components/status.jsx:1615
msgid "Report post…"
msgstr "Reportar publicación…"

#: src/components/status.jsx:1989
#: src/components/status.jsx:2025
#: src/components/status.jsx:2737
msgid "Liked"
msgstr "Me gusta"

#: src/components/status.jsx:2022
#: src/components/status.jsx:2724
msgid "Boosted"
msgstr "Impulsó"

#: src/components/status.jsx:2032
#: src/components/status.jsx:2749
msgid "Bookmarked"
msgstr "Añadido a marcadores"

#: src/components/status.jsx:2036
msgid "Pinned"
msgstr "Fijada"

#: src/components/status.jsx:2082
#: src/components/status.jsx:2561
msgid "Deleted"
msgstr "Eliminada"

#: src/components/status.jsx:2123
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {# respuesta} other {# respuestas}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2213
msgid "Thread{0}"
msgstr "Hilo{0}"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
#: src/components/status.jsx:2457
msgid "Show less"
msgstr "Mostrar menos"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
msgid "Show content"
msgstr "Mostrar contenido"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2453
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Filtrado: {0}"

#: src/components/status.jsx:2457
msgid "Show media"
msgstr "Mostrar multimedia"

#: src/components/status.jsx:2597
msgid "Edited"
msgstr "Se editó"

#: src/components/status.jsx:2674
msgid "Comments"
msgstr "Comentarios"

#. More from [Author]
#: src/components/status.jsx:2974
msgid "More from <0/>"
msgstr "Más de <0/>"

#: src/components/status.jsx:3276
msgid "Edit History"
msgstr "Historial de ediciones"

#: src/components/status.jsx:3280
msgid "Failed to load history"
msgstr "Error al cargar el historial"

#: src/components/status.jsx:3285
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "Cargando…"

#: src/components/status.jsx:3521
msgid "HTML Code"
msgstr "Código HTML"

#: src/components/status.jsx:3538
msgid "HTML code copied"
msgstr "Código HTML copiado"

#: src/components/status.jsx:3541
msgid "Unable to copy HTML code"
msgstr "No se ha podido copiar el código HTML"

#: src/components/status.jsx:3553
msgid "Media attachments:"
msgstr "Archivos adjuntos:"

#: src/components/status.jsx:3575
msgid "Account Emojis:"
msgstr "Emojis de esta cuenta:"

#: src/components/status.jsx:3606
#: src/components/status.jsx:3651
msgid "static URL"
msgstr "URL estática"

#: src/components/status.jsx:3620
msgid "Emojis:"
msgstr "Emojis:"

#: src/components/status.jsx:3665
msgid "Notes:"
msgstr "Notas:"

#: src/components/status.jsx:3669
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "Esto es estático, sin estilos y sin guion. Necesitarás añadir tus propios estilos y edición si lo necesitas."

#: src/components/status.jsx:3675
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "Las encuestas no son interactivas y se convertirán en una lista con recuento de votos."

#: src/components/status.jsx:3680
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "Los archivos adjuntos pueden ser imágenes, vídeos, audios o cualquier tipo de archivo."

#: src/components/status.jsx:3686
msgid "Post could be edited or deleted later."
msgstr "La publicación podría ser editada o eliminada más adelante."

#: src/components/status.jsx:3692
msgid "Preview"
msgstr "Vista previa"

#: src/components/status.jsx:3701
msgid "Note: This preview is lightly styled."
msgstr "Nota: Esta vista previa está ligeramente definida."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3954
msgid "<0/> <1/> boosted"
msgstr "<0/> <1/> impulsó"

#: src/components/status.jsx:4056
msgid "Post hidden by your filters"
msgstr "Publicación oculta por tus filtros"

#: src/components/status.jsx:4057
msgid "Post pending"
msgstr "Publicación pendiente"

#: src/components/status.jsx:4058
#: src/components/status.jsx:4059
#: src/components/status.jsx:4060
#: src/components/status.jsx:4061
msgid "Post unavailable"
msgstr "Publicación no disponible"

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "Nuevas publicaciones"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1078
#: src/pages/status.jsx:1455
msgid "Try again"
msgstr "Inténtalo de nuevo"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, one {# Impulso} other {# Impulsos}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr "Publicaciones fijadas"

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "Hilo"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>Filtrado</0>: <1>{0}</1>"

#: src/components/translation-block.jsx:196
msgid "Auto-translated from {sourceLangText}"
msgstr "Traducción automática desde {sourceLangText}"

#: src/components/translation-block.jsx:234
msgid "Translating…"
msgstr "Traduciendo…"

#: src/components/translation-block.jsx:237
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "Traducir desde {sourceLangText} (autodetectado)"

#: src/components/translation-block.jsx:238
msgid "Translate from {sourceLangText}"
msgstr "Traducir desde el {sourceLangText}"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:266
msgid "Auto ({0})"
msgstr "Automático ({0})"

#: src/components/translation-block.jsx:279
msgid "Failed to translate"
msgstr "Error al traducir"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "Editando el mensaje original"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "Respondiendo a @{0}"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "Ahora puedes cerrar esta página."

#: src/compose.jsx:71
msgid "Close window"
msgstr "Cerrar la ventana"

#: src/compose.jsx:87
msgid "Login required."
msgstr "Se requiere iniciar sesión."

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:280
msgid "Go home"
msgstr "Ir a la página de inicio"

#: src/pages/account-statuses.jsx:241
msgid "Account posts"
msgstr "Publicaciones de la cuenta"

#: src/pages/account-statuses.jsx:248
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ Respuestas)"

#: src/pages/account-statuses.jsx:250
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- Impulsos)"

#: src/pages/account-statuses.jsx:252
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:254
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (Multimedia)"

#: src/pages/account-statuses.jsx:260
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:331
msgid "Clear filters"
msgstr "Limpiar filtros"

#: src/pages/account-statuses.jsx:348
msgid "Showing post with replies"
msgstr "Mostrando publicaciones con respuestas"

#: src/pages/account-statuses.jsx:353
msgid "+ Replies"
msgstr "+ Respuestas"

#: src/pages/account-statuses.jsx:359
msgid "Showing posts without boosts"
msgstr "Mostrando publicaciones sin impulsos"

#: src/pages/account-statuses.jsx:364
msgid "- Boosts"
msgstr "- Impulsos"

#: src/pages/account-statuses.jsx:370
msgid "Showing posts with media"
msgstr "Mostrando publicaciones con archivos"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:387
msgid "Showing posts tagged with #{0}"
msgstr "Mostrando publicaciones etiquetadas con #{0}"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:426
msgid "Showing posts in {0}"
msgstr "Mostrando publicaciones en {0}"

#: src/pages/account-statuses.jsx:516
msgid "Nothing to see here yet."
msgstr "No hay nada que ver aquí… todavía."

#: src/pages/account-statuses.jsx:517
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "No se han podido cargar las publicaciones"

#: src/pages/account-statuses.jsx:558
#: src/pages/account-statuses.jsx:588
msgid "Unable to fetch account info"
msgstr "No se ha podido obtener información de la cuenta"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:565
msgid "Switch to account's instance {0}"
msgstr "Cambiar a instancia de la cuenta {0}"

#: src/pages/account-statuses.jsx:595
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "Cambiar a mi instancia (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:668
msgid "Month"
msgstr "Mes"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "Actual"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "Predefinida"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "Cambiar a esta cuenta"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "Cambiar a una nueva pestaña/ventana"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "Ver perfil…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "Establecer como predefinida"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr "¿Deseas cerrar sesión <0>@{0}</0>?"

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "Cerrar sesión…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr "Conectado en {0} (<0/>)"

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "Añadir una cuenta existente"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "Nota: la cuenta <0>Predefinida</0> se cargará siempre primero. Si cambias de cuenta, esta se mantendrá durante la sesión."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "No hay marcadores todavía. ¡Busca algo que marcar!"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "No se han podido cargar los marcadores."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "la última hora"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "las últimas 2 horas"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "las últimas 3 horas"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "las últimas 4 horas"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "las últimas 5 horas"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "las últimas 6 horas"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "las últimas 7 horas"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "las últimas 8 horas"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "las últimas 9 horas"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "las últimas 10 horas"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "las últimas 11 horas"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "las últimas 12 horas"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "después de 12 horas"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "Etiquetas seguidas"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "Grupos"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "Mostrando {selectedFilterCategory, select, all {todas las publicaciones} original {publicaciones originales} replies {respuestas} boosts {impulsos} followedTags {etiquetas seguidas} groups {grupos} filtered {publicaciones filtradas}}, {sortBy, select, createdAt {{sortOrder, select, asc {más antiguos} desc {más recientes}}} reblogsCount {{sortOrder, select, asc {menos impulsos} desc {más impulsos}}} favouritesCount {{sortOrder, select, asc {menos likes} desc {más likes}}} repliesCount {{sortOrder, select, asc {menos respuestas} desc {más respuestas}}} density {{sortOrder, select, asc {menos denso} desc {más denso}}}} first{groupBy, select, account {, agrupado por autores} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "Ponerse al día <0>beta</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "Ayuda"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "¿De qué se trata?"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "La puesta al día es una línea de tiempo independiente de las personas a las que sigues, que ofrece una panorámica de gran nivel de un vistazo, con una interfaz sencilla inspirada en el correo electrónico para ordenar y filtrar sin esfuerzo las publicaciones."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Vista previa de la interfaz de puesta al día"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "Pongámonos al día"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "Pongámonos al día con las publicaciones de las cuentas que sigues."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "Mostrarme todas las publicaciones de…"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "hasta el máximo"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "Ponerse al día"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "Superpone tu última puesta al día"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "Hasta la última puesta al día ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "Nota: Es posible que tu instancia solamente muestre un máximo de 800 publicaciones en la línea temporal de Inicio, independientemente del intervalo de tiempo. Podría ser menos o más."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "Anteriormente…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, one {# publicación} other {# publicaciones}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "¿Deseas eliminar esta puesta al día?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr "Eliminando la puesta al día {0}"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr "La puesta al día {0} ha sido eliminada"

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "Nota: Solo se almacenarán un máximo de 3. El resto se eliminará automáticamente."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "Obteniendo publicaciones…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "Esto podría tardar un rato."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "Limpiar filtros"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "Mejores enlaces"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "Compartido por {0}"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:330
msgid "All"
msgstr "Todos"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, one {# autor} other {# autores}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "Ordenar"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Fecha"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Densidad"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr "Filtrar"

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "Autores"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "Nada"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "Mostrar todos los autores"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "No es necesario leer todo."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "Eso es todo."

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "Volver arriba"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "Enlaces compartidos por seguidores, ordenados por recuento de compartidos, impulsos y me gustas."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "Ordenar: densidad"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "Las publicaciones están ordenadas por densidad de información o profundidad. Las publicaciones más cortas son \"más ligeras\" mientras que las publicaciones más largas son \"más pesadas\". Las publicaciones con fotos son \"más pesadas\" que los mensajes sin fotos."

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "Grupo: Autores"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "Las publicaciones están agrupadas por autores, ordenadas por conteo de publicaciones por autor."

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "Siguiente autor"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "Autor anterior"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "Ir arriba"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "No hay me gustas todavía. ¡Busca algo que te guste!"

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "No se pueden cargar los \"me gustas\"."

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "Inicio y listas"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "Cronología pública"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "Conversaciones"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "Perfiles"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "Nunca"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "Nuevo filtro"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0, plural, one {# filtro} other {# filtros}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "No se han podido cargar los filtros."

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "No hay ningún filtro todavía."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "Añadir filtro"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "Editar filtro"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "No se ha podido editar el filtro"

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "No se ha podido crear el filtro"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "Título"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "Palabra completa"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "No hay palabras clave. Añade una."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "Añadir palabra clave"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, one {# palabra clave} other {# palabras claves}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "Filtrar desde…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* Todavía no se ha implementado"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "Estado: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "Cambiar caducidad"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "Caducidad"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "La publicación filtrada será…"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr "oscurecido (solo medios)"

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "minimizada"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "escondida"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "¿Deseas eliminar este filtro?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "No se ha podido eliminar el filtro."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "Caducado"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "Caducando <0/>"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "Nunca caduca"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural, one {# etiqueta} other {# etiquetas}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "No se han podido cargar las etiquetas seguidas."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "No se han seguido etiquetas todavía."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "No hay resultados para mostrar."

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "No se han podido cargar las publicaciones."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{hashtagTitle} (solo multimedia) en {instance}"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{hashtagTitle} en {instance}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (solo multimedia)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "Nadie ha publicado nada con esta etiqueta todavía."

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "No se han podido cargar las publicaciones con esta etiqueta"

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "¿Deseas dejar de seguir la etiqueta #{hashtag}?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "Se ha dejado de seguir la etiqueta #{hashtag}"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "Se ha comenzado a seguir la etiqueta #{hashtag}"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "Siguiendo…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "No destacado en el perfil"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "No se ha podido desactivar en el perfil"

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "Destacado en el perfil"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr "{TOTAL_TAGS_LIMIT, plural, one {}other {Máximo # etiquetas}}"

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "Añadir etiqueta"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "Eliminar etiqueta"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr "{SHORTCUTS_LIMIT, plural, one {Se ha alcanzado el límite máximo de # atajos. No se ha podido añadir el atajo.} other {Se ha alcanzado el límite máximo de # atajos. No se ha podido añadir el atajo.}}"

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "Este atajo ya existe"

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr "Atajo de etiqueta añadido"

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "Añadir a atajos"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "Introduce una nueva instancia, por ejemplo \"mastodon.social\""

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "Instancia inválida"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "Ir a otra instancia…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "Ir a mi instancia (<0>{currentInstance}</0>)"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "No se han podido cargar las notificaciones."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<0>Nuevas</0> <1>solicitudes de seguimiento</1>"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "Resolviendo…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "No se ha podido determinar la URL"

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "Nada aún."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "Administrar miembros"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "¿Deseas eliminar a <0>@{0}</0> de la lista?"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "Eliminar…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, one {# lista} other {# listas}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "Aún no hay listas."

#: src/pages/login.jsx:122
#: src/pages/login.jsx:132
msgid "Failed to register application"
msgstr "No se pudo registrar la aplicación"

#: src/pages/login.jsx:218
msgid "instance domain"
msgstr "dominio de la instancia"

#: src/pages/login.jsx:242
msgid "e.g. “mastodon.social”"
msgstr "ejemplo \"mastodon.social\""

#: src/pages/login.jsx:253
msgid "Failed to log in. Please try again or try another instance."
msgstr "Error al iniciar sesión. Por favor, inténtalo de nuevo o usa otra instancia."

#: src/pages/login.jsx:265
msgid "Continue with {selectedInstanceText}"
msgstr "Continuar con {selectedInstanceText}"

#: src/pages/login.jsx:266
msgid "Continue"
msgstr "Continuar"

#: src/pages/login.jsx:274
msgid "Don't have an account? Create one!"
msgstr "¿No tienes una cuenta? ¡Crea una!"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "Menciones privadas"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "Privado"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "Nadie te ha mencionado :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "No se han podido cargar las menciones."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "Que tú no sigues"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "Que no te siguen"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "Con una nueva cuenta"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr "Que te mencionan de forma no solicitada"

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr "Que están limitadas por los moderadores del servidor"

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "Configuración de notificaciones"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "Nuevas notificaciones"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, one {Anuncio} other {Anuncios}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "Solicitudes de seguimiento"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, one {# solicitud de seguimiento} other {# solicitudes de seguimiento}}"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr "{0, plural, one {Notificaciones filtradas de # persona} other {Notificaciones filtradas de # personas}}"

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "Solo menciones"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "Hoy"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "Estás al día."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "Ayer"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "No se han podido cargar las notificaciones"

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "Configuración de las notificaciones actualizada"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "Filtrar notificaciones de personas:"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "Filtrar"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "Ignorar"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "Actualizado <0>{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr "Mostrar notificaciones de <0>@{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr "Notificaciones de <0>@{0}</0>"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr "Las notificaciones de @{0} no serán filtradas a partir de ahora."

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr "No se ha podido aceptar la solicitud de notificación"

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "Permitir"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr "Las notificaciones de @{0} no se mostrarán en las notificaciones filtradas a partir de ahora."

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr "No se ha podido descartar la solicitud de notificación"

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "Descartar"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "Descartado"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "Línea de tiempo local ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "Línea temporal federada ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "Línea de tiempo local"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "Línea temporal federada"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "Nadie ha publicado nada todavía."

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "Cambiar a Federado"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "Cambiar a Local"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr "No hay publicaciones programadas."

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr "Programada <0><1/></0> <2>({0})</2>"

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr "Programada <0><1/></0>"

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr "Publicación programada reprogramada"

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr "No se pudo reprogramar la publicación"

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr "Reprogramar"

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr "¿Deseas eliminar la publicación programada?"

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr "Se ha eliminado la publicación programada"

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr "No se pudo eliminar la publicación programada"

#: src/pages/search.jsx:51
msgid "Search: {q} (Posts)"
msgstr "Buscar: {q} (Publicaciones)"

#: src/pages/search.jsx:54
msgid "Search: {q} (Accounts)"
msgstr "Buscar: {q} (Cuentas)"

#: src/pages/search.jsx:57
msgid "Search: {q} (Hashtags)"
msgstr "Buscar: {q} (Etiquetas)"

#: src/pages/search.jsx:60
msgid "Search: {q}"
msgstr "Buscar: {q}"

#: src/pages/search.jsx:340
#: src/pages/search.jsx:422
msgid "Hashtags"
msgstr "Etiquetas"

#: src/pages/search.jsx:372
#: src/pages/search.jsx:426
#: src/pages/search.jsx:496
msgid "See more"
msgstr "Ver más"

#: src/pages/search.jsx:398
msgid "See more accounts"
msgstr "Ver más cuentas"

#: src/pages/search.jsx:412
msgid "No accounts found."
msgstr "No se encontraron cuentas."

#: src/pages/search.jsx:468
msgid "See more hashtags"
msgstr "Ver más etiquetas"

#: src/pages/search.jsx:482
msgid "No hashtags found."
msgstr "No se han encontrado etiquetas."

#: src/pages/search.jsx:526
msgid "See more posts"
msgstr "Ver más publicaciones"

#: src/pages/search.jsx:540
msgid "No posts found."
msgstr "No se han encontrado publicaciones."

#: src/pages/search.jsx:585
msgid "Enter your search term or paste a URL above to get started."
msgstr "Introduce un término de búsqueda o pega una URL arriba para empezar."

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "Ajustes"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "Apariencia"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "Claro"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "Oscuro"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "Automático"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "Tamaño del texto"

#. Preview of one character, in largest size
#. Preview of one character, in smallest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "A"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "Idioma en pantalla"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "Traducciones voluntarias"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "Publicando"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "Visibilidad por defecto"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "Sincronizado"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "Ha ocurrido un error al actualizar la privacidad de la publicación"

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "Sincronizado con los ajustes del servidor de tu instancia. <0> Ve a tu instancia ({instance}) para más ajustes.</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "Experimentos"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "Actualizar automáticamente las publicaciones de tu línea de tiempo"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "Carrusel de publicaciones impulsadas"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "Traducción de publicaciones"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr "Traducir a "

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "Idioma del sistema ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr "{0, plural,=0 {Esconder el botón de \"Traducir\" para:} other {Esconder el botón de traducir para (#):}}"

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr "Nota: Esta función usa servicios de traducción externos, con la tecnología <0>{TRANSLATION_API_NAME}</0>."

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "Traducción automática"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr "Mostrar automáticamente la traducción de las publicaciones en la línea de tiempo. Funciona únicamente en publicaciones <0>cortas</0> sin advertencias de contenido, archivos multimedia o encuestas."

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "Selector de GIF durante la redacción"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "Nota: Esta característica emplea un servicio de búsqueda GIF externo, desarrollado por <0>GIPHY</0>. Estos son adecuados para ser vistos por todas las edades, los parámetros de seguimiento son despojados, la información de referencia se omite de las solicitudes, pero las consultas de búsqueda y la información de direcciones IP seguirán llegando a sus servidores."

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "Generador de descripción de imagen"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr "Solo para imágenes nuevas mientras se redactan nuevas publicaciones."

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "Nota: Esta característica usa un servicio de IA externo, desarrollado por <0>img-alt-api</0>. Puede no funcionar bien. Solamente para imágenes y en inglés."

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr "Notificaciones agrupadas del lado del servidor"

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr "Función en fase alfa. Ventana de agrupación potencialmente mejorada, pero con una lógica de agrupación básica."

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "Importación/exportación \"en la nube\" para la configuración de atajos"

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr "⚠️⚠️⚠️ Muy experimental.<0/>Guardado en las notas de tu propio perfil. Las notas del perfil (privadas) se utilizan principalmente para otros perfiles, y se ocultan para el perfil propio."

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "Nota: Esta función utiliza la API del servidor de instancias conectado en ese momento."

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr "Modo oculto <0>(<1>Texto</1> → <2>████</2>)</0>"

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr "Reemplazar el texto por bloques, útil al tomar capturas de pantalla, por motivos de privacidad."

#: src/pages/settings.jsx:710
msgid "About"
msgstr "Acerca de"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr "<0>Creado</0> por <1>@cheeaun</1>"

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "Patrocina"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Dona"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr "Qué hay de nuevo"

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "Política de privacidad"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>Sitio:</0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>Versión:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "Versión copiada"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr "No se ha podido copiar la versión"

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr "No se ha podido actualizar la suscripción. Inténtalo de nuevo."

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr "No se ha podido cancelar la suscripción. Por favor, inténtalo de nuevo."

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "Notificaciones push (beta)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr "Las notificaciones push están bloqueadas. Actívalas en los ajustes de tu navegador."

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "Permitir desde <0>{0}</0>"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "cualquiera"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "personas a las que sigo"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "seguidores"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "Seguidos"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "Encuestas"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "Ediciones de publicaciones"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr "No se han permitido notificaciones desde la última vez que te conectaste. Necesitarás <0><1>conectarte de nuevo</1> para permitir las notificaciones. </0>."

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "NOTA: Las notificaciones push solo se permiten para <0>una cuenta</0>."

#. js-lingui-explicit-id
#: src/pages/status.jsx:658
#: src/pages/status.jsx:1221
msgid "post.title"
msgstr "Publicación"

#: src/pages/status.jsx:912
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "No estás conectado. No puedes interactuar (responder, impulsar, etc.)."

#: src/pages/status.jsx:932
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "Esta publicación es de otra instancia (<0>{instance}</0>). Las interacciones (responder, impulsar, etc.) no son posibles."

#: src/pages/status.jsx:960
msgid "Error: {e}"
msgstr "Error: {e}"

#: src/pages/status.jsx:967
msgid "Switch to my instance to enable interactions"
msgstr "Cambiar a mi instancia para activar interacciones"

#: src/pages/status.jsx:1069
msgid "Unable to load replies."
msgstr "No se han podido cargar las respuestas."

#: src/pages/status.jsx:1181
msgid "Back"
msgstr "Volver"

#: src/pages/status.jsx:1212
msgid "Go to main post"
msgstr "Ir a la publicación principal"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1235
msgid "{0} posts above ‒ Go to top"
msgstr "{0} publicaciones arriba - Ir hacia arriba"

#: src/pages/status.jsx:1282
#: src/pages/status.jsx:1345
msgid "Switch to Side Peek view"
msgstr "Cambiar a vista lateral"

#: src/pages/status.jsx:1346
msgid "Switch to Full view"
msgstr "Cambiar a vista completa"

#: src/pages/status.jsx:1364
msgid "Show all sensitive content"
msgstr "Mostrar todo el contenido sensible"

#: src/pages/status.jsx:1369
msgid "Experimental"
msgstr "Experimental"

#: src/pages/status.jsx:1378
msgid "Unable to switch"
msgstr "No se ha podido cambiar"

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1385
msgid "Switch to post's instance ({0})"
msgstr "Cambiar a la instancia de la publicación ({0})"

#: src/pages/status.jsx:1388
msgid "Switch to post's instance"
msgstr "Cambiar a la instancia de la publicación"

#: src/pages/status.jsx:1446
msgid "Unable to load post"
msgstr "No se ha podido cargar la publicación"

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1582
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, one {# respuesta} other {<0>{1}</0> respuestas}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1600
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, one {# comentario} other {<0>{0}</0> comentarios}}"

#: src/pages/status.jsx:1622
msgid "View post with its replies"
msgstr "Ver publicación con sus respuestas"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "Tendencias ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "Noticias populares"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr "Por {0}"

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "Volver a mostrar publicaciones en tendencia"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "Mostrando publicaciones que mencionan <0>{0}</0>"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "Publicaciones en tendencia"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "No hay publicaciones en tendencia."

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "Un cliente web minimalista para Mastodon que hace las cosas a su manera."

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Inicia sesión con Mastodon"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "Registrarse"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "Conecta tu cuenta Mastodon/Fediverse existente.<0/>Tus credenciales no se almacenan en este servidor."

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr "<0>Creado</0> por <1>@cheeaun</1>. <2>Política de privacidad</2>."

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "Captura de pantalla del carrusel de impulsos"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "Carrusel de publicaciones impulsadas"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "Separa visualmente las publicaciones originales de las compartidas (publicaciones impulsadas)."

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr "Captura de pantalla del hilo de comentarios agrupados"

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr "Hilo de comentarios agrupados"

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "Sigue las conversaciones sin esfuerzo. Respuestas semi-colapsables."

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr "Captura de pantalla de notificaciones agrupadas"

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "Notificaciones agrupadas"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr "Las notificaciones similares se agrupan y contraen para reducir el desorden."

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "Captura de pantalla de la interfaz de usuario con múltiples columnas"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "Una o varias columnas"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "Por defecto, una sola columna para los amantes del modo zen. Modo de múltiples columnas configurable para usuarios avanzados."

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "Captura de pantalla de la línea de tiempo multi-etiqueta con un formulario para añadir más etiquetas"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "Línea de tiempo con varias etiquetas"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "Hasta 5 etiquetas combinadas en una única línea de tiempo."

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "Parece que tu navegador está bloqueando ventanas emergentes."

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr "Un borrador de una publicación está minimizado. Publícalo o descártalo antes de crear uno nuevo."

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr "Una publicación está abierta en este momento. Publica o descarta antes de crear una nueva."

