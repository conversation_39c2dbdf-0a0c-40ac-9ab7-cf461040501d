msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: de\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-08-05 04:12\n"
"Last-Translator: \n"
"Language-Team: German\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: de\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:141
msgid "Locked"
msgstr "Gesperrt"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:163
msgid "Posts: {0}"
msgstr "Posts: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:168
msgid "Last posted: {0}"
msgstr "Zuletzt gepostet: {0}"

#: src/components/account-block.jsx:183
#: src/components/account-info.jsx:705
msgid "Automated"
msgstr "Automatisiert"

#: src/components/account-block.jsx:190
#: src/components/account-info.jsx:710
#: src/components/status.jsx:748
msgid "Group"
msgstr "Gruppe"

#: src/components/account-block.jsx:200
msgid "Mutual"
msgstr "Befreundet"

#: src/components/account-block.jsx:204
#: src/components/account-info.jsx:1907
msgid "Requested"
msgstr "Angefragt"

#: src/components/account-block.jsx:208
#: src/components/account-info.jsx:1898
msgid "Following"
msgstr "Folgt"

#: src/components/account-block.jsx:212
#: src/components/account-info.jsx:1212
msgid "Follows you"
msgstr "Folgt Ihnen"

#: src/components/account-block.jsx:220
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, one {# Folgender} other {# Folgende}}"

#: src/components/account-block.jsx:229
#: src/components/account-info.jsx:753
msgid "Verified"
msgstr "Verifiziert"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:244
#: src/components/account-info.jsx:892
msgid "Joined <0>{0}</0>"
msgstr "Beigetreten: <0>{0}</0>"

#: src/components/account-info.jsx:64
msgid "Forever"
msgstr "Für immer"

#: src/components/account-info.jsx:398
msgid "Unable to load account."
msgstr "Konto kann nicht geladen werden."

#: src/components/account-info.jsx:413
msgid "Go to account page"
msgstr "Zur Kontoseite gehen"

#: src/components/account-info.jsx:442
#: src/components/account-info.jsx:775
msgid "Followers"
msgstr "Folgende"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:446
#: src/components/account-info.jsx:830
msgid "following.stats"
msgstr ""

#: src/components/account-info.jsx:449
#: src/pages/account-statuses.jsx:495
#: src/pages/search.jsx:345
#: src/pages/search.jsx:492
msgid "Posts"
msgstr "Beiträge"

#: src/components/account-info.jsx:457
#: src/components/account-info.jsx:1268
#: src/components/compose.jsx:2819
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1998
#: src/components/status.jsx:2015
#: src/components/status.jsx:2140
#: src/components/status.jsx:2769
#: src/components/status.jsx:2772
#: src/pages/account-statuses.jsx:539
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1306
#: src/pages/trending.jsx:474
msgid "More"
msgstr "Mehr"

#: src/components/account-info.jsx:469
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> hat angegeben, dass sein/ihr neues Konto jetzt ist:"

#: src/components/account-info.jsx:614
#: src/components/account-info.jsx:1496
msgid "Handle copied"
msgstr "Handle kopiert"

#: src/components/account-info.jsx:617
#: src/components/account-info.jsx:1499
msgid "Unable to copy handle"
msgstr "Handle konnte nicht kopiert werden"

#: src/components/account-info.jsx:623
#: src/components/account-info.jsx:1505
msgid "Copy handle"
msgstr "Handle kopieren"

#: src/components/account-info.jsx:629
msgid "Go to original profile page"
msgstr "Zur ursprünglichen Profilseite gehen"

#: src/components/account-info.jsx:647
msgid "View profile image"
msgstr "Profilbild anzeigen"

#: src/components/account-info.jsx:665
msgid "View profile header"
msgstr "Profil-Header anzeigen"

#: src/components/account-info.jsx:681
#: src/components/account-info.jsx:1806
#: src/components/account-info.jsx:2332
msgid "Edit profile"
msgstr "Profil bearbeiten"

#: src/components/account-info.jsx:700
msgid "In Memoriam"
msgstr ""

#: src/components/account-info.jsx:782
#: src/components/account-info.jsx:840
msgid "This user has chosen to not make this information available."
msgstr "Dieser Benutzer hat sich entschieden, diese Informationen nicht verfügbar zu machen."

#. placeholder {0}: shortenNumber(followersCount)
#. placeholder {1}: shortenNumber(followersCount)
#: src/components/account-info.jsx:802
msgid "{followersCount, plural, one {<0>{0}</0> Follower} other {<1>{1}</1> Followers}}"
msgstr ""

#. placeholder {0}: shortenNumber(followingCount)
#: src/components/account-info.jsx:846
msgid "{followingCount, plural, other {<0>{0}</0> Following}}"
msgstr ""

#. placeholder {0}: shortenNumber(statusesCount)
#. placeholder {1}: shortenNumber(statusesCount)
#: src/components/account-info.jsx:870
msgid "{statusesCount, plural, one {<0>{0}</0> Post} other {<1>{1}</1> Posts}}"
msgstr ""

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:917
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0} Originalbeiträge, {1} Antworten, {2} Boosts"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:933
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural, one {{1, plural, one {Letzter Beitrag des vergangenen Tags} other {Letzter Beitrag der vergangenen {2} Tage}}} other {{3, plural, one {Letzte {4} Beiträge des vergangenen Tags} other {Letzte {5} Beiträge der vergangenen {6} Tage}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:949
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {Letzter Beitrag des vergangenen Jahres} other {Letzter Beitrag der vergangenen {1} Jahre}}"

#: src/components/account-info.jsx:974
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "Original"

#: src/components/account-info.jsx:978
#: src/components/status.jsx:2553
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1025
#: src/pages/status.jsx:1652
msgid "Replies"
msgstr "Antworten"

#: src/components/account-info.jsx:982
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "Boosts"

#: src/components/account-info.jsx:988
msgid "Post stats unavailable."
msgstr "Beitragsstatistiken nicht verfügbar."

#: src/components/account-info.jsx:1019
msgid "View post stats"
msgstr "Beitragsstatistiken anzeigen"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1216
msgid "Last post: <0>{0}</0>"
msgstr "Letzter Beitrag: <0>{0}</0>"

#: src/components/account-info.jsx:1230
msgid "Muted"
msgstr "Stumm geschaltet"

#: src/components/account-info.jsx:1235
msgid "Blocked"
msgstr "Blockiert"

#: src/components/account-info.jsx:1244
msgid "Private note"
msgstr "Private Notiz"

#: src/components/account-info.jsx:1301
msgid "Mention <0>@{username}</0>"
msgstr "<0>@{username}</0> erwähnen"

#: src/components/account-info.jsx:1313
msgid "Translate bio"
msgstr "Bio übersetzen"

#: src/components/account-info.jsx:1324
msgid "Edit private note"
msgstr "Private Notiz bearbeiten"

#: src/components/account-info.jsx:1324
msgid "Add private note"
msgstr "Private Notiz hinzufügen"

#: src/components/account-info.jsx:1344
msgid "Notifications enabled for @{username}'s posts."
msgstr "Benachrichtigungen für Beiträge von @{username} aktiviert."

#: src/components/account-info.jsx:1345
msgid " Notifications disabled for @{username}'s posts."
msgstr " Benachrichtigungen für Beiträge von @{username} deaktiviert."

#: src/components/account-info.jsx:1357
msgid "Disable notifications"
msgstr "Benachrichtigungen deaktivieren"

#: src/components/account-info.jsx:1358
msgid "Enable notifications"
msgstr "Benachrichtigungen aktivieren"

#: src/components/account-info.jsx:1375
msgid "Boosts from @{username} enabled."
msgstr "Boosts von @{username} aktiviert."

#: src/components/account-info.jsx:1376
msgid "Boosts from @{username} disabled."
msgstr "Boosts von @{username} deaktiviert."

#: src/components/account-info.jsx:1387
msgid "Disable boosts"
msgstr "Boosts deaktivieren"

#: src/components/account-info.jsx:1387
msgid "Enable boosts"
msgstr "Boosts aktivieren"

#: src/components/account-info.jsx:1406
msgid "@{username} is no longer featured on your profile."
msgstr ""

#: src/components/account-info.jsx:1416
msgid "@{username} is now featured on your profile."
msgstr ""

#: src/components/account-info.jsx:1424
msgid "Unable to unfeature @{username} on your profile."
msgstr ""

#: src/components/account-info.jsx:1428
msgid "Unable to feature @{username} on your profile."
msgstr ""

#: src/components/account-info.jsx:1437
msgid "Don't feature on profile"
msgstr ""

#: src/components/account-info.jsx:1438
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "Auf Profil vorstellen"

#: src/components/account-info.jsx:1447
msgid "Show featured profiles"
msgstr ""

#: src/components/account-info.jsx:1462
#: src/components/account-info.jsx:1472
#: src/components/account-info.jsx:2078
msgid "Add/Remove from Lists"
msgstr "Hinzufügen/Entfernen aus Listen"

#: src/components/account-info.jsx:1522
#: src/components/status.jsx:1420
msgid "Link copied"
msgstr "Link kopiert"

#: src/components/account-info.jsx:1525
#: src/components/status.jsx:1423
msgid "Unable to copy link"
msgstr "Link konnte nicht kopiert werden"

#: src/components/account-info.jsx:1531
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1429
#: src/components/status.jsx:3547
msgid "Copy"
msgstr "Kopieren"

#: src/components/account-info.jsx:1546
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1445
msgid "Sharing doesn't seem to work."
msgstr "Teilen scheint nicht zu funktionieren."

#: src/components/account-info.jsx:1552
#: src/components/status.jsx:1451
msgid "Share…"
msgstr "Teilen…"

#: src/components/account-info.jsx:1572
msgid "Unmuted @{username}"
msgstr "Stummschaltung von @{username} aufgehoben"

#: src/components/account-info.jsx:1584
msgid "Unmute <0>@{username}</0>"
msgstr "<0>@{username}</0> entstummen"

#: src/components/account-info.jsx:1600
msgid "Mute <0>@{username}</0>…"
msgstr "<0>@{username}</0> stumm schalten…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1632
msgid "Muted @{username} for {0}"
msgstr "@{username} für {0} stumm geschaltet"

#: src/components/account-info.jsx:1644
msgid "Unable to mute @{username}"
msgstr "Stummschalten von @{username} nicht möglich"

#: src/components/account-info.jsx:1665
msgid "Remove <0>@{username}</0> from followers?"
msgstr "<0>@{username}</0> als Follower entfernen?"

#: src/components/account-info.jsx:1685
msgid "@{username} removed from followers"
msgstr "@{username} als Follower entfernt"

#: src/components/account-info.jsx:1697
msgid "Remove follower…"
msgstr "Folgenden entfernen…"

#: src/components/account-info.jsx:1708
msgid "Block <0>@{username}</0>?"
msgstr "<0>@{username}</0> blockieren?"

#: src/components/account-info.jsx:1732
msgid "Unblocked @{username}"
msgstr "@{username} entsperrt"

#: src/components/account-info.jsx:1740
msgid "Blocked @{username}"
msgstr "@{username} blockiert"

#: src/components/account-info.jsx:1748
msgid "Unable to unblock @{username}"
msgstr "@{username} kann nicht entsperrt werden"

#: src/components/account-info.jsx:1750
msgid "Unable to block @{username}"
msgstr "@{username} konnte nicht blockiert werden"

#: src/components/account-info.jsx:1760
msgid "Unblock <0>@{username}</0>"
msgstr "<0>@{username}</0> entsperren"

#: src/components/account-info.jsx:1769
msgid "Block <0>@{username}</0>…"
msgstr "<0>@{username}</0> blockieren…"

#: src/components/account-info.jsx:1786
msgid "Report <0>@{username}</0>…"
msgstr "<0>@{username}</0> melden…"

#: src/components/account-info.jsx:1842
msgid "Withdraw follow request?"
msgstr "Folgeanfrage zurückziehen?"

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1843
msgid "Unfollow @{0}?"
msgstr ""

#: src/components/account-info.jsx:1901
msgid "Unfollow…"
msgstr "Entfolgen…"

#: src/components/account-info.jsx:1910
msgid "Withdraw…"
msgstr "Zurückziehen…"

#: src/components/account-info.jsx:1917
#: src/components/account-info.jsx:1921
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "Folgen"

#: src/components/account-info.jsx:2018
#: src/components/account-info.jsx:2073
#: src/components/account-info.jsx:2207
#: src/components/account-info.jsx:2327
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:892
#: src/components/compose.jsx:2775
#: src/components/compose.jsx:3255
#: src/components/compose.jsx:3464
#: src/components/compose.jsx:3694
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:118
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3271
#: src/components/status.jsx:3511
#: src/components/status.jsx:4020
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1393
msgid "Close"
msgstr "Schließen"

#: src/components/account-info.jsx:2023
msgid "Translated Bio"
msgstr "Übersetzte Bio"

#: src/components/account-info.jsx:2118
msgid "Unable to remove from list."
msgstr "Entfernen von der Liste nicht möglich."

#: src/components/account-info.jsx:2119
msgid "Unable to add to list."
msgstr "Hinzufügen zur Liste nicht möglich."

#: src/components/account-info.jsx:2138
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "Listen konnten nicht geladen werden."

#: src/components/account-info.jsx:2142
msgid "No lists."
msgstr "Keine Listen."

#: src/components/account-info.jsx:2153
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "Neue Liste"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2212
msgid "Private note about <0>@{0}</0>"
msgstr "Private Notiz über <0>@{0}</0>"

#: src/components/account-info.jsx:2242
msgid "Unable to update private note."
msgstr "Private Notiz konnte nicht aktualisiert werden."

#: src/components/account-info.jsx:2265
#: src/components/account-info.jsx:2563
msgid "Cancel"
msgstr "Abbrechen"

#: src/components/account-info.jsx:2270
msgid "Save & close"
msgstr "Speichern & schließen"

#: src/components/account-info.jsx:2387
msgid "Unable to update profile."
msgstr "Profil konnte nicht aktualisiert werden."

#: src/components/account-info.jsx:2394
msgid "Header picture"
msgstr ""

#: src/components/account-info.jsx:2446
msgid "Profile picture"
msgstr ""

#: src/components/account-info.jsx:2498
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "Name"

#: src/components/account-info.jsx:2511
msgid "Bio"
msgstr "Bio"

#: src/components/account-info.jsx:2524
msgid "Extra fields"
msgstr "Zusätzliche Felder"

#: src/components/account-info.jsx:2530
msgid "Label"
msgstr "Label"

#: src/components/account-info.jsx:2533
msgid "Content"
msgstr "Inhalt"

#: src/components/account-info.jsx:2566
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "Speichern"

#: src/components/account-info.jsx:2620
msgid "username"
msgstr "Benutzername"

#: src/components/account-info.jsx:2624
msgid "server domain name"
msgstr "Server-Domainname"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2690
msgid "Profiles featured by @{0}"
msgstr ""

#: src/components/account-info.jsx:2716
msgid "No featured profiles."
msgstr ""

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "Cloak-Modus deaktiviert"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "Cloak-Modus aktiviert"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "Startseite"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "Verfassen"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr ""

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr ""

#: src/components/compose.jsx:212
msgid "Take photo or video"
msgstr ""

#: src/components/compose.jsx:213
msgid "Add media"
msgstr "Medien hinzufügen"

#: src/components/compose.jsx:214
msgid "Add custom emoji"
msgstr "Benutzerdefinierte Emoji hinzufügen"

#: src/components/compose.jsx:215
msgid "Add GIF"
msgstr "GIF hinzufügen"

#: src/components/compose.jsx:216
msgid "Add poll"
msgstr "Umfrage hinzufügen"

#: src/components/compose.jsx:217
msgid "Schedule post"
msgstr ""

#: src/components/compose.jsx:417
msgid "You have unsaved changes. Discard this post?"
msgstr "Sie haben ungespeicherte Änderungen. Diesen Beitrag verwerfen?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:655
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr "{0, plural, one {Datei {1} wird nicht unterstützt.} other {Dateien {2} werden nicht unterstützt.}}"

#: src/components/compose.jsx:665
#: src/components/compose.jsx:683
#: src/components/compose.jsx:1795
#: src/components/compose.jsx:1930
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, one {Du kannst höchstens eine Datei anhängen.} other {Du kannst höchstens # Dateien anhängen.}}"

#: src/components/compose.jsx:873
msgid "Pop out"
msgstr "Herauslösen"

#: src/components/compose.jsx:880
msgid "Minimize"
msgstr "Minimieren"

#: src/components/compose.jsx:916
msgid "Looks like you closed the parent window."
msgstr "Es sieht so aus, als ob Sie das übergeordnete Fenster geschlossen hätten."

#: src/components/compose.jsx:923
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Es sieht so aus, als ob Sie bereits ein Verfassen-Feld im übergeordneten Fenster geöffnet haben und gerade veröffentlichen. Bitte warten Sie, bis es fertig ist, und versuchen Sie es später erneut."

#: src/components/compose.jsx:928
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Es sieht so aus, als hätten Sie bereits ein Verfassen-Feld im übergeordneten Fenster geöffnet. Wenn Sie dieses Fenster einblenden, werden die Änderungen, die Sie im übergeordneten Fenster vorgenommen haben, verworfen. Fortfahren?"

#: src/components/compose.jsx:971
msgid "Pop in"
msgstr "Einblenden"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:981
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "Als Antwort auf den Beitrag von @{0} (<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:991
msgid "Replying to @{0}’s post"
msgstr "Als Antwort auf den Beitrag von @{0}"

#: src/components/compose.jsx:1004
msgid "Editing source post"
msgstr "Quellbeitrag bearbeiten"

#: src/components/compose.jsx:1057
msgid "Poll must have at least 2 options"
msgstr "Umfrage muss mindestens 2 Optionen enthalten"

#: src/components/compose.jsx:1061
msgid "Some poll choices are empty"
msgstr "Einige Umfrageoptionen sind leer"

#: src/components/compose.jsx:1074
msgid "Some media have no descriptions. Continue?"
msgstr "Einige Medien haben keine Beschreibungen. Fortfahren?"

#: src/components/compose.jsx:1126
msgid "Attachment #{i} failed"
msgstr "Anhang #{i} fehlgeschlagen"

#: src/components/compose.jsx:1222
#: src/components/status.jsx:2328
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "Inhaltswarnung"

#: src/components/compose.jsx:1238
msgid "Content warning or sensitive media"
msgstr "Inhaltswarnung oder sensible Medien"

#: src/components/compose.jsx:1274
#: src/components/status.jsx:99
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "Öffentlich"

#: src/components/compose.jsx:1279
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:100
msgid "Local"
msgstr "Lokal"

#: src/components/compose.jsx:1283
#: src/components/status.jsx:101
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "Nicht gelistet"

#: src/components/compose.jsx:1286
#: src/components/status.jsx:102
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "Nur für Folgende"

#: src/components/compose.jsx:1289
#: src/components/status.jsx:103
#: src/components/status.jsx:2204
msgid "Private mention"
msgstr "Private Erwähnung"

#: src/components/compose.jsx:1298
msgid "Post your reply"
msgstr "Antwort veröffentlichen"

#: src/components/compose.jsx:1300
msgid "Edit your post"
msgstr "Post bearbeiten"

#: src/components/compose.jsx:1301
msgid "What are you doing?"
msgstr "Was machen Sie?"

#: src/components/compose.jsx:1380
msgid "Mark media as sensitive"
msgstr "Medien als sensibel markieren"

#: src/components/compose.jsx:1417
msgid "Posting on <0/>"
msgstr ""

#: src/components/compose.jsx:1448
#: src/components/compose.jsx:3313
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "Hinzufügen"

#: src/components/compose.jsx:1676
msgid "Schedule"
msgstr ""

#: src/components/compose.jsx:1678
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1192
#: src/components/status.jsx:1978
#: src/components/status.jsx:1979
#: src/components/status.jsx:2673
msgid "Reply"
msgstr "Antworten"

#: src/components/compose.jsx:1680
msgid "Update"
msgstr "Aktualisieren"

#: src/components/compose.jsx:1681
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Senden"

#: src/components/compose.jsx:1807
msgid "Downloading GIF…"
msgstr "GIF wird heruntergeladen…"

#: src/components/compose.jsx:1835
msgid "Failed to download GIF"
msgstr "GIF konnte nicht heruntergeladen werden"

#: src/components/compose.jsx:2060
#: src/components/compose.jsx:2153
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "Mehr…"

#: src/components/compose.jsx:2589
msgid "Uploaded"
msgstr "Hochgeladen"

#: src/components/compose.jsx:2602
msgid "Image description"
msgstr "Bildbeschreibung"

#: src/components/compose.jsx:2603
msgid "Video description"
msgstr "Videobeschreibung"

#: src/components/compose.jsx:2604
msgid "Audio description"
msgstr "Audiobeschreibung"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2639
#: src/components/compose.jsx:2659
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "Datei zu groß. Das Hochladen kann Probleme verursachen. Versuche, die Dateigröße von {0} auf {1} oder weniger zu reduzieren."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2651
#: src/components/compose.jsx:2671
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr ""

#: src/components/compose.jsx:2679
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "Bildrate zu hoch. Das Hochladen könnte Probleme verursachen."

#: src/components/compose.jsx:2739
#: src/components/compose.jsx:2989
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "Entfernen"

#: src/components/compose.jsx:2756
#: src/compose.jsx:84
msgid "Error"
msgstr "Fehler"

#: src/components/compose.jsx:2781
msgid "Edit image description"
msgstr "Bildbeschreibung bearbeiten"

#: src/components/compose.jsx:2782
msgid "Edit video description"
msgstr "Videobeschreibung bearbeiten"

#: src/components/compose.jsx:2783
msgid "Edit audio description"
msgstr "Audiobeschreibung bearbeiten"

#: src/components/compose.jsx:2828
#: src/components/compose.jsx:2877
msgid "Generating description. Please wait…"
msgstr "Erzeuge Beschreibung. Bitte warten Sie…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2848
msgid "Failed to generate description: {0}"
msgstr ""

#: src/components/compose.jsx:2849
msgid "Failed to generate description"
msgstr "Beschreibung konnte nicht erzeugt werden"

#: src/components/compose.jsx:2861
#: src/components/compose.jsx:2867
#: src/components/compose.jsx:2913
msgid "Generate description…"
msgstr "Beschreibung erzeugen…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2900
msgid "Failed to generate description{0}"
msgstr ""

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2915
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>– experimentell</0>"

#: src/components/compose.jsx:2934
msgid "Done"
msgstr "Fertig"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2970
msgid "Choice {0}"
msgstr "Auswahl {0}"

#: src/components/compose.jsx:3017
msgid "Multiple choices"
msgstr "Mehrfache Auswahl"

#: src/components/compose.jsx:3020
msgid "Duration"
msgstr "Dauer"

#: src/components/compose.jsx:3051
msgid "Remove poll"
msgstr "Umfrage entfernen"

#: src/components/compose.jsx:3272
msgid "Search accounts"
msgstr "Konten durchsuchen"

#: src/components/compose.jsx:3326
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "Fehler beim Laden der Konten"

#: src/components/compose.jsx:3470
msgid "Custom emojis"
msgstr "Benutzerdefinierte Emojis"

#: src/components/compose.jsx:3490
msgid "Search emoji"
msgstr "Emojis durchsuchen"

#: src/components/compose.jsx:3521
msgid "Error loading custom emojis"
msgstr "Fehler beim Laden benutzerdefinierter Emojis"

#: src/components/compose.jsx:3532
msgid "Recently used"
msgstr "Kürzlich verwendet"

#: src/components/compose.jsx:3533
msgid "Others"
msgstr "Andere"

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3571
msgid "{0} more…"
msgstr "{0} mehr…"

#: src/components/compose.jsx:3709
msgid "Search GIFs"
msgstr ""

#: src/components/compose.jsx:3724
msgid "Powered by GIPHY"
msgstr ""

#: src/components/compose.jsx:3732
msgid "Type to search GIFs"
msgstr "Eintippen, um GIFs zu suchen"

#: src/components/compose.jsx:3830
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "Zurück"

#: src/components/compose.jsx:3848
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "Weiter"

#: src/components/compose.jsx:3865
msgid "Error loading GIFs"
msgstr "Fehler beim Laden der GIFs"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "Nicht gesendete Entwürfe"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "Es sieht so aus, als hätten Sie noch nicht gesendete Entwürfe. Lass uns dort fortfahren, wo Sie aufgehört haben."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "Diesen Entwurf löschen?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "Fehler beim Löschen des Entwurfs! Bitte versuchen Sie es erneut."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1595
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "Löschen…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "Fehler beim Abrufen des Antwort-zu-Statuses!"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "Alle Entwürfe löschen?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "Fehler beim Löschen der Entwürfe! Bitte versuchen Sie es erneut."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "Alle löschen…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "Keine Entwürfe gefunden."

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "Umfrage"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:375
msgid "Media"
msgstr "Medien"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "In neuem Fenster öffnen"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "Akzeptieren"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "Ablehnen"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "Akzeptiert"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "Abgelehnt"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:335
#: src/pages/search.jsx:368
msgid "Accounts"
msgstr "Konten"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:562
#: src/pages/status.jsx:1426
msgid "Show more…"
msgstr "Mehr anzeigen…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:567
msgid "The end."
msgstr "Das Ende."

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "Nichts anzuzeigen"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "Tastenkombinationen"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "Hilfe zu Tastenkombinationen"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "Nächster Post"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "Vorheriger Post"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "Karussell zum nächsten Post überspringen"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Umschalt</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "Karussell zum vorherigen Post überspringen"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Umschalt</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "Neue Posts laden"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "Postdetails öffnen"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Eingabe</0> oder <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "Inhaltswarnung ausklappen oder<0/>aus-/eingeklappte Unterhaltung umschalten"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "Post oder Dialoge schließen"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc</0> oder <1>Löschtaste</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "Spalte im mehrspaltigen Modus fokussieren"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> bis <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "Neuen Post erstellen"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "Neuen Post erstellen (neues Fenster)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Umschalt</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "Post senden"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Strg</0> + <1>Eingabe</1> oder <2>⌘</2> + <3>Eingabe</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:204
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:47
#: src/pages/search.jsx:317
msgid "Search"
msgstr "Suchen"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "Antworten (neues Fenster)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Umschalt</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "Liken (favorisieren)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> oder <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1200
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
#: src/components/status.jsx:2724
msgid "Boost"
msgstr "Boosten"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Umschalt</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
#: src/components/status.jsx:2749
msgid "Bookmark"
msgstr "Lesezeichen"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "Cloak Modus ein/aus"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Umschalt</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "Liste bearbeiten"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "Liste konnte nicht bearbeitet werden."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "Liste konnte nicht erstellt werden."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "Antworten auf Listenmitglieder anzeigen"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "Antworten auf Personen denen ich folge anzeigen"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "Antworten nicht anzeigen"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr ""

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "Erstellen"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "Diese Liste löschen?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "Liste konnte nicht gelöscht werden."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr ""

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "Medienbeschreibung"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1306
#: src/components/status.jsx:1315
#: src/components/translation-block.jsx:239
msgid "Translate"
msgstr "Übersetzen"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1334
msgid "Speak"
msgstr "Sprechen"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "Originalmedien in neuem Fenster öffnen"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "Originalmedien öffnen"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "Es wird versucht, das Bild zu beschreiben. Bitte warten Sie…"

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "Fehler beim Beschreiben des Bildes"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "Beschreibe Bild…"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "Post anzeigen"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "Sensible Medien"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "Gefiltert: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3850
#: src/components/status.jsx:3946
#: src/components/status.jsx:4024
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "Gefiltert"

#: src/components/media.jsx:479
msgid "Open file"
msgstr ""

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr ""

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "Post veröffentlicht. Schauen Sie sich ihn an."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr ""

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "Antwort gepostet. Schauen Sie sich sie an."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "Post aktualisiert. Schauen Sie sich ihn an."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "Menü"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "Seite jetzt neu laden um zu aktualisieren?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "Neues Update verfügbar…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr ""

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "Aufholen"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "Erwähnungen"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "Benachrichtigungen"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "Neu"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "Profil"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "Lesezeichen"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "Gefällt mir"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "Gefolgte Hashtags"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:341
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "Filter"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "Stumm geschaltete Nutzer"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "Stumm geschaltete Nutzer…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "Blockierte Nutzer"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "Blockierte Nutzer…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "Konten…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:199
#: src/pages/status.jsx:925
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "Einloggen"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "Angesagt"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "Föderiert"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "Verknüpfungen / Spalten…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "Einstellungen…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "Listen"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "Alle Listen"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "Benachrichtigung"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "Diese Benachrichtigung stammt von Ihrem anderen Konto."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "Alle Benachrichtigungen anzeigen"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} hat auf Ihren Beitrag mit {emojiObject} reagiert"

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} hat einen Post veröffentlicht."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} hat deine Antwort geteilt.} other {{account} hat deinen Beitrag geteilt.}}} other {{account} hat {postsCount} Beiträge von dir geteilt.}}} other {{postType, select, reply {<0><1>{0}</1> Leute</0> haben deine Antwort geteilt.} other {<2><3>{1}</3> Leute</2> haben deinen Beitrag geteilt.}}}}"

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, =1 {{account} folgt dir jetzt.} other {<0><1>{0}</1> Leute</0> folgen dir jetzt.}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} hat darum gebeten, Ihnen zu folgen."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} hat deine Antwort favorisiert.} other {{account} hat deinen Beitrag favorisiert.}}} other {{account} hat {postsCount} Beiträge von dir favorisiert.}}} other {{postType, select, reply {<0><1>{0}</1> Leute</0> haben deine Antwort favorisiert.} other {<2><3>{1}</3> Leute</2> haben deinen Beitrag favorisiert.}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "Eine Umfrage hat geendet, an der du teilgenommen oder die du erstellt hast."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "Eine von Ihnen erstellte Umfrage wurde beendet."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "Eine Umfrage in der Sie teilgenommen haben wurde beendet."

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "Ein Post, mit dem Sie interagiert haben, wurde bearbeitet."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} hat deine Antwort geteilt & favorisiert.} other {{account} hat deinen Beitrag geteilt & favorisiert.}}} other {{account} hat {postsCount} deiner Beiträge geteilt & favorisiert.}}} other {{postType, select, reply {<0><1>{0}</1> Leute</0> haben deine Antwort geteilt & favorisiert.} other {<2><3>{1}</3> Leute</2> haben deinen Beitrag geteilt & favorisiert.}}}}"

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} registriert."

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} hat {targetAccount} gemeldet"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "Verbindungen mit <0>{name}</0> verloren."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "Moderationswarnung"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr ""

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "Ein Admin von <0>{from}</0> hat <1>{targetName}</1> gesperrt, weshalb du von diesem Profil nichts mehr wirst sehen und mit ihm nicht mehr wirst interagieren können."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "Ein Admin von <0>{from}</0> hat <1>{targetName}</1> gesperrt. Betroffene Follower: {followersCount}; Profile, denen er/sie folgt: {followingCount}."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr ""

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "Ihr Konto hat eine Moderationswarnung erhalten."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "Ihr Konto wurde deaktiviert."

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "Einige Ihrer Beiträge wurden als sensibel markiert."

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "Einige Ihrer Beiträge wurden gelöscht."

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "Ihre Beiträge werden von nun an als sensibel markiert."

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "Ihr Konto wurde eingeschränkt."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "Ihr Konto wurde gesperrt."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[Unbekannter Benachrichtigungstyp: {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1277
#: src/components/status.jsx:1287
msgid "Boosted/Liked by…"
msgstr "Geteilt/favorisiert von …"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "Favorisiert von …"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "Geboostet von…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "Gefolgt von…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "Erfahre mehr <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr ""

#: src/components/notification.jsx:801
#: src/components/status.jsx:486
msgid "Read more →"
msgstr "Mehr lesen →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "Abgestimmt"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr ""

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "Ergebnisse ausblenden"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "Abstimmen"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1295
#: src/pages/status.jsx:1318
msgid "Refresh"
msgstr "Aktualisieren"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "Ergebnisse anzeigen"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> Stimme} other {<1>{1}</1> Stimmen}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, one {<0>{0}</0> Abstimmender} other {<1>{1}</1> Abstimmende}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "Beendet <0/>"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "Beendet"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr ""

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "Endet"

#: src/components/recent-searches.jsx:27
msgid "Cleared recent searches"
msgstr ""

#: src/components/recent-searches.jsx:49
#: src/components/search-form.jsx:328
msgid "Recent searches"
msgstr ""

#: src/components/recent-searches.jsx:59
msgid "Clear all"
msgstr ""

#: src/components/recent-searches.jsx:94
#: src/pages/account-statuses.jsx:334
msgid "Clear"
msgstr "Leeren"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0}s"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0}m"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0}h"

#: src/components/report-modal.jsx:30
msgid "Spam"
msgstr "Spam"

#: src/components/report-modal.jsx:31
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "Bösartige Links, gefälschtes Engagement oder wiederholte Antworten"

#: src/components/report-modal.jsx:34
msgid "Illegal"
msgstr "Illegal"

#: src/components/report-modal.jsx:35
msgid "Violates the law of your or the server's country"
msgstr "Verstößt gegen das Gesetz Ihres oder des Serverlandes"

#: src/components/report-modal.jsx:38
msgid "Server rule violation"
msgstr "Verstoß gegen Server-Regeln"

#: src/components/report-modal.jsx:39
msgid "Breaks specific server rules"
msgstr "Verstößt gegen bestimmte Server-Regel"

#: src/components/report-modal.jsx:40
msgid "Violation"
msgstr "Verstoß"

#: src/components/report-modal.jsx:43
msgid "Other"
msgstr "Andere"

#: src/components/report-modal.jsx:44
msgid "Issue doesn't fit other categories"
msgstr "Problem passt nicht zu anderen Kategorien"

#: src/components/report-modal.jsx:111
msgid "Report Post"
msgstr "Post melden"

#: src/components/report-modal.jsx:111
msgid "Report @{username}"
msgstr "Melde @{username}"

#: src/components/report-modal.jsx:147
msgid "Pending review"
msgstr "Ausstehende Prüfung"

#: src/components/report-modal.jsx:189
msgid "Post reported"
msgstr "Post gemeldet"

#: src/components/report-modal.jsx:189
msgid "Profile reported"
msgstr "Profil gemeldet"

#: src/components/report-modal.jsx:197
msgid "Unable to report post"
msgstr "Post konnte nicht gemeldet werden"

#: src/components/report-modal.jsx:198
msgid "Unable to report profile"
msgstr "Profil konnte nicht gemeldet werden"

#: src/components/report-modal.jsx:206
msgid "What's the issue with this post?"
msgstr "Was ist das Problem mit diesem Post?"

#: src/components/report-modal.jsx:207
msgid "What's the issue with this profile?"
msgstr "Was ist das Problem mit diesem Profil?"

#: src/components/report-modal.jsx:276
msgid "Additional info"
msgstr "Zusätzliche Infos"

#: src/components/report-modal.jsx:299
msgid "Forward to <0>{domain}</0>"
msgstr "An <0>{domain}</0> weiterleiten"

#: src/components/report-modal.jsx:309
msgid "Send Report"
msgstr "Meldung senden"

#: src/components/report-modal.jsx:318
msgid "Muted {username}"
msgstr "@{username} Stumm geschaltet"

#: src/components/report-modal.jsx:321
msgid "Unable to mute {username}"
msgstr "Konnte {username} nicht Stumm schalten"

#: src/components/report-modal.jsx:326
msgid "Send Report <0>+ Mute profile</0>"
msgstr "Meldung absenden <0>+ Profil stumm schalten</0>"

#: src/components/report-modal.jsx:337
msgid "Blocked {username}"
msgstr "{username} blockiert"

#: src/components/report-modal.jsx:340
msgid "Unable to block {username}"
msgstr "Konnte {username} nicht blockieren"

#: src/components/report-modal.jsx:345
msgid "Send Report <0>+ Block profile</0>"
msgstr "Meldung absenden <0>+ Profil blockieren</0>"

#: src/components/search-form.jsx:18
msgid "Posts with <0>{query}</0>"
msgstr "Beiträge mit <0>{query}</0>"

#: src/components/search-form.jsx:26
msgid "Accounts with <0>{query}</0>"
msgstr "Konten mit <0>{query}</0>"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:34
msgid "Posts tagged with <0>#{0}</0>"
msgstr "Beiträge mit dem Hashtag <0>#{0}</0>"

#: src/components/search-form.jsx:43
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>– Konten, Hashtags & Beiträge</0>"

#: src/components/search-form.jsx:135
msgid "Look up <0>{query}</0>"
msgstr "<0>{query}</0> nachschlagen"

#: src/components/search-form.jsx:361
#: src/pages/home.jsx:251
msgid "See all"
msgstr "Alle anzeigen"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "Startseite / Folgen"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "Öffentlich (Lokal / Föderiert)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "Konto"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "Hashtag"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "Listen-ID"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "Nur Lokal"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:203
msgid "Instance"
msgstr "Instanz"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "Optional, z.B. mastodon.social"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "Suchbegriff"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "Optional, außer für Mehrspalten-Modus"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "z.B. PixelArt (max. 5, durch Leerzeichen getrennt)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "Nur Medien"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "Tastenkürzel"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "Beta"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "Gib eine Liste an mit Verknüpfungen, die erscheinen werden als:"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "Schwebender Button"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "Tab/Menüleiste"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "Mehrere Spalten"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "Im aktuellen Ansichtsmodus nicht verfügbar"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "Nach oben"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "Nach unten"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1557
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "Bearbeiten"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "Fügen Sie mehr als eine Verknüpfung/Spalte hinzu, damit dies funktioniert."

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "Noch keine Spalten. Tippen Sie auf die Hinzufügen Schaltfläche."

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "Noch keine Verknüpfungen. Tippen Sie auf die Hinzufügen Schaltfläche."

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "Unentschlossen, was du hinzufügen sollst?<0/>Probier’ es zuerst mit <1>Start / Folgend und Benachrichtigungen</1>."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "Höchstens {SHORTCUTS_LIMIT} Spalten"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "Höchstens {SHORTCUTS_LIMIT} Verknüpfungen"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "Import/Export"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "Spalte hinzufügen…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "Verknüpfung hinzufügen…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "Bestimmte Liste ist optional. Im Mehrspalten-Modus ist eine Liste erforderlich, sonst wird die Spalte nicht angezeigt."

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "Im Mehrspalten-Modus ist ein Suchbegriff erforderlich, sonst wird die Spalte nicht angezeigt."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "Es werden mehrere Hashtags gleichzeitig unterstützt. Separiert mit Leerzeichen."

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "Verknüpfung bearbeiten"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "Verknüpfung hinzufügen"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "Timeline"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "Liste"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "<0>Verknüpfungen</0> importierten/exportieren"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "Importieren"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "Verknüpfung hier einfügen"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "Lade gespeicherte Verknüpfungen von Instanz-Server herunter…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "Verknüpfungen konnten nicht heruntergeladen werden"

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "Verknüpfungen vom Instanz-Server herunterladen"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "* Existiert in den aktuellen Verknüpfungen"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "Liste funktioniert möglicherweise nicht, wenn sie von einem anderen Konto stammt."

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "Ungültiges Einstellungsformat"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "An aktuelle Verknüpfungen anhängen?"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr "Es werden nur Verknüpfungen angehängt, die in den aktuellen Verknüpfungen nicht vorhanden sind."

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "Keine neuen Verknüpfungen zum Importieren"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "Limit {SHORTCUTS_LIMIT} wurde überschritten, es wurde nur ein Teil der Verknüpfungen importiert."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "Verknüpfungen importiert"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "Importieren & Anhängen…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "Aktuelle Verknüpfungen überschreiben?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "Verknüpfungen importieren?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "oder überschreiben…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "Importieren…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "Exportieren"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "Verknüpfungen kopiert"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "Konnte Verknüpfungen nicht kopieren"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "Verknüpfungseinstellungen kopiert"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "Konnte Verknüpfungseinstellungen nicht kopieren"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "Teilen"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "Verknüpfungen werden auf Instanz-Server gespeichert …"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "Verknüpfungen gespeichert"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "Verknüpfungen konnten nicht gespeichert werden"

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "Mit Instanzserver synchronisieren"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0, plural, one {# Zeichen} other {# Zeichen}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "Rohes Verknüpfungs-JSON"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "Einstellungen vom/zum Instanzserver importieren/exportieren (hochgradig experimentell)"

#: src/components/status.jsx:351
msgid "Unable to format math"
msgstr ""

#: src/components/status.jsx:365
msgid "Math expressions found."
msgstr ""

#. Action to switch from rendered math back to raw (LaTeX) markup
#: src/components/status.jsx:368
msgid "Show markup"
msgstr ""

#. Action to render math expressions from raw (LaTeX) markup
#: src/components/status.jsx:373
msgid "Format math"
msgstr ""

#: src/components/status.jsx:772
msgid "<0/> <1>boosted</1>"
msgstr "<0/> <1>geteilt</1>"

#: src/components/status.jsx:875
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "Entschuldigung, deine aktuell verwendete Instanz kann nicht mit diesem von einer anderen Instanz stammenden Beitrag interagieren."

#. placeholder {0}: username || acct
#: src/components/status.jsx:1029
msgid "Unliked @{0}'s post"
msgstr "Beitrag von @{0} entfavorisiert"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1030
msgid "Liked @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1069
msgid "Unbookmarked @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1070
msgid "Bookmarked @{0}'s post"
msgstr ""

#: src/components/status.jsx:1169
msgid "Some media have no descriptions."
msgstr "Einige Medien haben keine Beschreibungen."

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1176
msgid "Old post (<0>{0}</0>)"
msgstr "Alter Beitrag (<0>{0}</0>)"

#: src/components/status.jsx:1200
#: src/components/status.jsx:1240
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
msgid "Unboost"
msgstr "Nicht mehr teilen"

#: src/components/status.jsx:1216
#: src/components/status.jsx:2715
msgid "Quote"
msgstr "Zitieren"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1228
#: src/components/status.jsx:1694
msgid "Unboosted @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1229
#: src/components/status.jsx:1695
msgid "Boosted @{0}'s post"
msgstr ""

#: src/components/status.jsx:1241
msgid "Boost…"
msgstr "Boost…"

#: src/components/status.jsx:1253
#: src/components/status.jsx:1988
#: src/components/status.jsx:2736
msgid "Unlike"
msgstr "Entfavorisieren"

#: src/components/status.jsx:1254
#: src/components/status.jsx:1988
#: src/components/status.jsx:1989
#: src/components/status.jsx:2736
#: src/components/status.jsx:2737
msgid "Like"
msgstr "Favorisieren"

#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
msgid "Unbookmark"
msgstr "Lesezeichen entfernen"

#: src/components/status.jsx:1346
msgid "Post text copied"
msgstr ""

#: src/components/status.jsx:1349
msgid "Unable to copy post text"
msgstr ""

#: src/components/status.jsx:1355
msgid "Copy post text"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1373
msgid "View post by <0>@{0}</0>"
msgstr ""

#: src/components/status.jsx:1394
msgid "Show Edit History"
msgstr "Bearbeitungsverlauf anzeigen"

#: src/components/status.jsx:1397
msgid "Edited: {editedDateText}"
msgstr "Bearbeitet: {editedDateText}"

#: src/components/status.jsx:1464
#: src/components/status.jsx:3516
msgid "Embed post"
msgstr "Beitrag einbetten"

#: src/components/status.jsx:1478
msgid "Conversation unmuted"
msgstr "Unterhaltung entstummt"

#: src/components/status.jsx:1478
msgid "Conversation muted"
msgstr "Unterhaltung stumm geschaltet"

#: src/components/status.jsx:1484
msgid "Unable to unmute conversation"
msgstr "Unterhaltung kann nicht entstummt werden"

#: src/components/status.jsx:1485
msgid "Unable to mute conversation"
msgstr "Unterhaltung kann nicht stumm geschaltet werden"

#: src/components/status.jsx:1494
msgid "Unmute conversation"
msgstr "Unterhaltung entstummen"

#: src/components/status.jsx:1501
msgid "Mute conversation"
msgstr "Unterhaltung stumm schalten"

#: src/components/status.jsx:1517
msgid "Post unpinned from profile"
msgstr "Beitrag vom Profil gelöst"

#: src/components/status.jsx:1518
msgid "Post pinned to profile"
msgstr "Beitrag ans Profil angeheftet"

#: src/components/status.jsx:1523
msgid "Unable to unpin post"
msgstr "Beitrag kann nicht gelöst werden"

#: src/components/status.jsx:1523
msgid "Unable to pin post"
msgstr "Beitrag kann nicht angeheftet werden"

#: src/components/status.jsx:1532
msgid "Unpin from profile"
msgstr "Vom Profil lösen"

#: src/components/status.jsx:1539
msgid "Pin to profile"
msgstr "Ans Profil anheften"

#: src/components/status.jsx:1568
msgid "Delete this post?"
msgstr "Diesen Post löschen?"

#: src/components/status.jsx:1584
msgid "Post deleted"
msgstr "Post gelöscht"

#: src/components/status.jsx:1587
msgid "Unable to delete post"
msgstr "Post konnte nicht gelöscht werden"

#: src/components/status.jsx:1615
msgid "Report post…"
msgstr "Post melden…"

#: src/components/status.jsx:1989
#: src/components/status.jsx:2025
#: src/components/status.jsx:2737
msgid "Liked"
msgstr "Favorisiert"

#: src/components/status.jsx:2022
#: src/components/status.jsx:2724
msgid "Boosted"
msgstr "Geboostet"

#: src/components/status.jsx:2032
#: src/components/status.jsx:2749
msgid "Bookmarked"
msgstr "Lesezeichen hinzugefügt"

#: src/components/status.jsx:2036
msgid "Pinned"
msgstr "Angeheftet"

#: src/components/status.jsx:2082
#: src/components/status.jsx:2561
msgid "Deleted"
msgstr "Gelöscht"

#: src/components/status.jsx:2123
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {# Antwort} other {# Antworten}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2213
msgid "Thread{0}"
msgstr "Thread{0}"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
#: src/components/status.jsx:2457
msgid "Show less"
msgstr "Weniger anzeigen"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
msgid "Show content"
msgstr "Inhalt anzeigen"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2453
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Gefiltert: {0}"

#: src/components/status.jsx:2457
msgid "Show media"
msgstr "Medien anzeigen"

#: src/components/status.jsx:2597
msgid "Edited"
msgstr "Bearbeitet"

#: src/components/status.jsx:2674
msgid "Comments"
msgstr "Kommentare"

#. More from [Author]
#: src/components/status.jsx:2974
msgid "More from <0/>"
msgstr ""

#: src/components/status.jsx:3276
msgid "Edit History"
msgstr "Bearbeitungsverlauf"

#: src/components/status.jsx:3280
msgid "Failed to load history"
msgstr "Fehler beim laden des Verlaufs"

#: src/components/status.jsx:3285
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "Laden…"

#: src/components/status.jsx:3521
msgid "HTML Code"
msgstr "HTML Code"

#: src/components/status.jsx:3538
msgid "HTML code copied"
msgstr "HTML Code kopiert"

#: src/components/status.jsx:3541
msgid "Unable to copy HTML code"
msgstr "HTML-Code konnte nicht kopiert werden"

#: src/components/status.jsx:3553
msgid "Media attachments:"
msgstr "Medienanhänge:"

#: src/components/status.jsx:3575
msgid "Account Emojis:"
msgstr "Account-Emojis:"

#: src/components/status.jsx:3606
#: src/components/status.jsx:3651
msgid "static URL"
msgstr "Statische URL"

#: src/components/status.jsx:3620
msgid "Emojis:"
msgstr "Emojis:"

#: src/components/status.jsx:3665
msgid "Notes:"
msgstr "Notizen:"

#: src/components/status.jsx:3669
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "Dies ist statisch, ungestylt und ohne Skript. Du kannst nach Belieben deine eigenen Styles anwenden und bearbeiten."

#: src/components/status.jsx:3675
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "Umfragen sind nicht interaktiv, es wird eine Liste mit Stimmanzahlen."

#: src/components/status.jsx:3680
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "Medienanhänge können Bilder, Videos, Audiodateien oder andere Dateitypen sein."

#: src/components/status.jsx:3686
msgid "Post could be edited or deleted later."
msgstr "Beitrag konnte später geändert oder gelöscht werden."

#: src/components/status.jsx:3692
msgid "Preview"
msgstr "Vorschau"

#: src/components/status.jsx:3701
msgid "Note: This preview is lightly styled."
msgstr "Notiz: Diese Vorschau ist leicht formiert."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3954
msgid "<0/> <1/> boosted"
msgstr "<0/> <1/> geteilt"

#: src/components/status.jsx:4056
msgid "Post hidden by your filters"
msgstr ""

#: src/components/status.jsx:4057
msgid "Post pending"
msgstr ""

#: src/components/status.jsx:4058
#: src/components/status.jsx:4059
#: src/components/status.jsx:4060
#: src/components/status.jsx:4061
msgid "Post unavailable"
msgstr ""

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "Neue Posts"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1078
#: src/pages/status.jsx:1455
msgid "Try again"
msgstr "Erneut versuchen"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr ""

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr ""

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "Thread"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>Herausgefiltert</0>: <1>{0}</1>"

#: src/components/translation-block.jsx:196
msgid "Auto-translated from {sourceLangText}"
msgstr "Automatisch von {sourceLangText} übersetzt"

#: src/components/translation-block.jsx:234
msgid "Translating…"
msgstr "Übersetze…"

#: src/components/translation-block.jsx:237
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "Von {sourceLangText} übersetzen (automatisch erkannt)"

#: src/components/translation-block.jsx:238
msgid "Translate from {sourceLangText}"
msgstr "Von {sourceLangText} übersetzen"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:266
msgid "Auto ({0})"
msgstr "Auto ({0})"

#: src/components/translation-block.jsx:279
msgid "Failed to translate"
msgstr "Übersetzung fehlgeschlagen"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "Quell-Status bearbeiten"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "Antworten auf @{0}"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "Sie können diese Seite jetzt schließen."

#: src/compose.jsx:71
msgid "Close window"
msgstr "Fenster schließen"

#: src/compose.jsx:87
msgid "Login required."
msgstr ""

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:280
msgid "Go home"
msgstr "Zur Startseite gehen"

#: src/pages/account-statuses.jsx:241
msgid "Account posts"
msgstr "Account-Posts"

#: src/pages/account-statuses.jsx:248
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ Antworten)"

#: src/pages/account-statuses.jsx:250
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- Boosts)"

#: src/pages/account-statuses.jsx:252
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:254
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (Medien)"

#: src/pages/account-statuses.jsx:260
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:331
msgid "Clear filters"
msgstr "Filter leeren"

#: src/pages/account-statuses.jsx:348
msgid "Showing post with replies"
msgstr "Beitrag mit Antworten anzeigen"

#: src/pages/account-statuses.jsx:353
msgid "+ Replies"
msgstr "+ Antworten"

#: src/pages/account-statuses.jsx:359
msgid "Showing posts without boosts"
msgstr "Zeige Posts ohne Boosts"

#: src/pages/account-statuses.jsx:364
msgid "- Boosts"
msgstr "- Boosts"

#: src/pages/account-statuses.jsx:370
msgid "Showing posts with media"
msgstr "Zeige Posts mit Medien"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:387
msgid "Showing posts tagged with #{0}"
msgstr "Zeige Posts mit #{0}"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:426
msgid "Showing posts in {0}"
msgstr ""

#: src/pages/account-statuses.jsx:516
msgid "Nothing to see here yet."
msgstr "Noch nichts zu sehen."

#: src/pages/account-statuses.jsx:517
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "Konnte Posts nicht laden"

#: src/pages/account-statuses.jsx:558
#: src/pages/account-statuses.jsx:588
msgid "Unable to fetch account info"
msgstr "Kontoinformationen konnten nicht abgerufen werden"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:565
msgid "Switch to account's instance {0}"
msgstr "Zur Kontoinstanz {0} wechseln"

#: src/pages/account-statuses.jsx:595
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "Zu meiner Instanz wechseln (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:668
msgid "Month"
msgstr "Monat"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "Aktuell"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "Standard"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr ""

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr ""

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "Profil anzeigen…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "Als Standard festlegen"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr ""

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "Abmelden…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr ""

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "Bestehendes Konto hinzufügen"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "Hinweis: <0>Standard</0>-Konten werden immer zum erstmaligen Laden verwendet werden. Zweitkonten werden während der Sitzung beibehalten werden."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr ""

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "Lesezeichen konnten nicht geladen werden."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "Letzte Stunde"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "Letzte 2 Stunden"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "Letzte 3 Stunden"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "Letzte 4 Stunden"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "Letzte 5 Stunden"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "Letzte 6 Stunden"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "Letzte 7 Stunden"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "Letzte 8 Stunden"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "Letzte 9 Stunden"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "Letzte 10 Stunden"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "Letzte 11 Stunden"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "Letzte 12 Stunden"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "Älter als 12 Stunden"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "Gefolgte Hashtags"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "Gruppen"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "Zeige {selectedFilterCategory, select, all {alle Beiträge} original {Originalbeiträge} replies {Antworten} boosts {geteilte Beiträge} followedTags {beobachtete Hashtags} groups {Gruppen} filtered {herausgefilterte Beiträge}},{sortBy, select, createdAt {{sortOrder, select, asc {älteste Beiträge} desc {neueste Beiträge}}} reblogsCount {{sortOrder, select, asc {am wenigsten geteilte Beiträge} desc {am meisten geteilte Beiträge}}} favouritesCount {{sortOrder, select, asc {wenigste Favoriten} desc {meiste Favoriten}}} repliesCount {{sortOrder, select, asc {wenigste Antworten} desc {meiste Antworten}}} density {{sortOrder, select, asc {am wenigsten dichte Beiträge} desc {dichteste Beiträge}}}} zuerst{groupBy, select, account {, nach Verfassern gruppiert} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr ""

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "Hilfe"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "Was ist das?"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "„Aufholen“ ist eine separate Timeline der Leute, denen du folgst. Sie ermöglicht es dir, auf einen Blick zu sehen, was du verpasst hast, mit einer einfachen, an E-Mails erinnernden Oberfläche, um mühelos Beiträge zu sortieren und zu filtern."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Vorschau der Aufholen-Oberfläche"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "Jetzt aufholen"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "Lass’ uns mit Beiträgen aufholen, deren Verfassern du folgst."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "Alle Posts von… anzeigen"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "bis zum Maximum"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "Aufholen"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "Überlappt mit deinem letzten Aufholen"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "Bis zum letzten Aufholen ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "Hinweis: Deine Instanz zeigt ggf. höchstens 800 Beiträge in deiner Home-Timeline an, unabhängig vom Zeitrahmen. Könnte variieren."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "Zuvor…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, one {# Beitrag} other {# Beiträge}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "Dieses Aufholen entfernen?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr ""

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr ""

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "Hinweis: Höchstens 3 werden gespeichert werden. Der Rest wird automatisch entfernt werden."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "Lade Posts…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "Dies könnte eine Weile dauern."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "Filter zurücksetzen"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "Häufigste Links"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "Geteilt von {0}"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:330
msgid "All"
msgstr "Alle"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, one {# Autor} other {# Autoren}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "Sortieren"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Datum"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Dichte"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr ""

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "Autoren"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "Keine"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "Alle Autoren anzeigen"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "Sie müssen nicht alles lesen."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "Das ist alles."

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "Zurück zum Anfang"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "Links, die von Leuten geteilt wurden, denen du folgst; sortiert nach Häufigkeit sowie nach Boost- und Favoriten-Anzahlen."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "Sortieren: Dichte"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "Beiträge werden nach Parametern wie Dichte oder Tiefe sortiert. Kürzere Beiträge sind „leichter“, während längere Beiträge „schwerer“ sind. Außerdem sind Beiträge mit Fotos „schwerer“ als solche ohne Fotos."

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "Gruppieren: Autoren"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "Beiträge werden nach Autoren gruppiert, sortiert nach Anzahl der Beiträge pro Autor."

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "Nächster Autor"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "Vorheriger Autor"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "Zum Anfang scrollen"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr ""

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "Favoriten können nicht geladen werden."

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "Startseite und Listen"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "Öffentliche Timelines"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "Unterhaltungen"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "Profile"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "Nie"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "Neuer Filter"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0, plural, one {# Filter} other {# Filter}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "Filter konnten nicht geladen werden."

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "Noch keine Filter."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "Filter hinzufügen"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "Filter bearbeiten"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "Filter konnte nicht bearbeitet werden"

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "Filter konnte nicht erstellt werden"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "Titel"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "Ganzes Wort"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "Keine Schlüsselwörter. Füge eines hinzu."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "Schlüsselwort hinzufügen"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, one {# Schlüsselwort} other {# Schlüsselwörter}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "Filtern von…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* Noch nicht implementiert"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "Status: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "Ablaufdatum ändern"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "Ablaufdatum"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "Gefilterter Post wird…"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr ""

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "minimiert"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "ausgeblendet"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "Diesen Filter löschen?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "Filter konnte nicht gelöscht werden."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "Abgelaufen"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "Läuft ab <0/>"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "Läuft nie ab"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural, one {# Hashtag} other {# Hashtags}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "Gefolgte Hashtags konnten nicht geladen werden."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "Noch keine Hashtags gefolgt."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "Hier gibt es nichts zu sehen."

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "Konnte Posts nicht laden."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{hashtagTitle} (nur Medien) auf {instance}"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{hashtagTitle} auf {instance}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (nur Medien)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "Noch niemand hat etwas mit diesem Tag gepostet."

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "Konnte Posts mit diesem Tag nicht laden"

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr ""

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "#{hashtag} entfolgt"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "#{hashtag} gefolgt"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "Folgt…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "Nicht mehr auf Profil vorgestellt"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "Nicht möglich, das Vorstellen auf dem Profil rückgängig zu machen"

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "Auf Profil vorgestellt"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr "{TOTAL_TAGS_LIMIT, plural, one {}other {Höchstens # Hashtags}}"

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "Hashtag hinzufügen"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "Hashtag entfernen"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr "{SHORTCUTS_LIMIT, plural, one {Höchstanzahl (#) an Verknüpfungen erreicht.} other {Höchstanzahl (#) an Verknüpfungen erreicht. Weitere Verknüpfung kann nicht hinzugefügt werden.}}"

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "Diese Verknüpfung existiert bereits"

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr "Hashtag-Verknüpfung hinzugefügt"

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "Zu Verknüpfungen hinzufügen"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "Geben Sie eine neue Instanz ein, z.B. \"mastodon.social\""

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "Ungültige Instanz"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "Gehe zu einer anderen Instanz…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "Zu meiner Instanz (<0>{currentInstance}</0>) gehen"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "Benachrichtigungen konnten nicht geladen werden."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<0>Neue</0> <1>Folge-Anfragen</1>"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "Auflösen…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "URL konnte nicht aufgelöst werden"

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "Noch nichts."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "Mitglieder verwalten"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr ""

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "Entferne…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, one {# Filter} other {# Filter}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "Noch keine Listen."

#: src/pages/login.jsx:122
#: src/pages/login.jsx:132
msgid "Failed to register application"
msgstr ""

#: src/pages/login.jsx:218
msgid "instance domain"
msgstr ""

#: src/pages/login.jsx:242
msgid "e.g. “mastodon.social”"
msgstr "z.B. “mastodon.social”"

#: src/pages/login.jsx:253
msgid "Failed to log in. Please try again or try another instance."
msgstr ""

#: src/pages/login.jsx:265
msgid "Continue with {selectedInstanceText}"
msgstr "Mit {selectedInstanceText} fortfahren"

#: src/pages/login.jsx:266
msgid "Continue"
msgstr "Weiter"

#: src/pages/login.jsx:274
msgid "Don't have an account? Create one!"
msgstr "Sie haben noch kein Konto? Erstellen Sie eines!"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "Private Erwähnungen"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "Privat"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "Niemand hat Sie erwähnt :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "Erwähnungen konnten nicht geladen werden."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "Sie folgen nicht"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "Wer Ihnen nicht folgt"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "Mit einem neuen Konto"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr "Wer dich unaufgefordert privat anschreibt"

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr "Wer von Servermoderatoren eingeschränkt wurde"

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "Benachrichtigungseinstellungen"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "Neue Benachrichtigungen"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, one {Bekanntmachung} other {Bekanntmachungen}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "Folgeanfragen"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, one {# Folge-Anfrage} other {# Folge-Anfragen}}"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr "{0, plural, one {Benachrichtigungen von # Person herausgefiltert} other {Benachrichtigungen von # Leuten herausgefiltert}}"

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "Nur Erwähnungen"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "Heute"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "Sie sind auf dem neuesten Stand."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "Gestern"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "Benachrichtigungen konnten nicht geladen werden"

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "Benachrichtigungseinstellungen aktualisiert"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "Benachrichtigungen von Personen ausfiltern:"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "Filtern"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "Ignorieren"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "Aktualisiert <0>{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr ""

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr ""

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr "Benachrichtigungen von @{0} werden von jetzt an nicht herausgefiltert."

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr "Unmöglich, die Benachrichtigungsanfrage anzunehmen"

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "Zulassen"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr ""

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr "Benachrichtigungsanfrage konnte nicht ausgeblendet werden"

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "Verwerfen"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "Verworfen"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "Lokale Zeitleiste ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "Föderierte Zeitleiste ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "Lokale Zeitleiste"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "Föderierte Zeitleiste"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "Noch niemand hat etwas gepostet."

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "Zu Föderiert wechseln"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "Zu Lokal wechseln"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr ""

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr ""

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr ""

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr ""

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr ""

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr ""

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr ""

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr ""

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr ""

#: src/pages/search.jsx:51
msgid "Search: {q} (Posts)"
msgstr "Suche: {q} (Posts)"

#: src/pages/search.jsx:54
msgid "Search: {q} (Accounts)"
msgstr "Suche: {q} (Konten)"

#: src/pages/search.jsx:57
msgid "Search: {q} (Hashtags)"
msgstr "Suche: {q} (Hashtags)"

#: src/pages/search.jsx:60
msgid "Search: {q}"
msgstr "Suche: {q}"

#: src/pages/search.jsx:340
#: src/pages/search.jsx:422
msgid "Hashtags"
msgstr "Hashtags"

#: src/pages/search.jsx:372
#: src/pages/search.jsx:426
#: src/pages/search.jsx:496
msgid "See more"
msgstr "Mehr anzeigen"

#: src/pages/search.jsx:398
msgid "See more accounts"
msgstr "Weitere Konten anzeigen"

#: src/pages/search.jsx:412
msgid "No accounts found."
msgstr "Keine Konten gefunden."

#: src/pages/search.jsx:468
msgid "See more hashtags"
msgstr "Mehr Hashtags anzeigen"

#: src/pages/search.jsx:482
msgid "No hashtags found."
msgstr "Keine Hashtags gefunden."

#: src/pages/search.jsx:526
msgid "See more posts"
msgstr "Mehr Posts anzeigen"

#: src/pages/search.jsx:540
msgid "No posts found."
msgstr "Keine Posts gefunden."

#: src/pages/search.jsx:585
msgid "Enter your search term or paste a URL above to get started."
msgstr "Geben Sie Ihren Suchbegriff ein oder fügen Sie oben eine URL ein, um zu beginnen."

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "Einstellungen"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "Erscheinungsbild"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "Hell"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "Dunkel"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "Automatisch"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "Textgröße"

#. Preview of one character, in largest size
#. Preview of one character, in smallest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "A"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "Anzeigesprache"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr ""

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "Posten"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "Standardsichtbarkeit"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "Synchronisiert"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "Aktualisieren der Beitragssichtbarkeit fehlgeschlagen"

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "Mit den Einstellungen deines Instanzservers synchronisiert. <0>Gehe zur Instanz ({instance}) für weitere Einstellungen.</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "Experimentelle Funktionen"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "Zeitleiste automatisch aktualisieren"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "Boost Karussell"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "Post-Übersetzung"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr ""

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "Systemsprache ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr "{0, plural, =0 {Verstecke „Übersetzen“-Button für:} other {Verstecke „Übersetzen“-Button für (#):}}"

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr ""

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "Automatische Inline-Übersetzung"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr "Übersetzung für Beiträge automatisch in der Zeitleiste anzeigen. Funktioniert nur für <0>kurze</0> Beiträge ohne Inhaltswarnung, Medien und Umfragen."

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "GIF-Wähler für Verfassen-Fenster"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "Hinweis: Diese Funktion verwendet einen externen GIF-Suchdienst, ermöglicht durch <0>GIPHY</0>. Angemessen für alle Altersgruppen, Tracking-Parameter werden entfernt, Referrer-Informationen werden bei Anfragen ausgelassen, aber Suchbegriffe und die IP-Adresse werden an deren Server übertragen."

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "Bildbeschreibungsgenerator"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr "Nur für neue Bilder beim Erstellen neuer Posts."

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "Hinweis: Diese Funktion verwendet einen externen KI-Dienst, ermöglicht durch <0>img-alt-api</0>. Könnte durchwachsen funktionieren. Nur für Bilder und nur auf Englisch."

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr "Serverseitig gruppierte Benachrichtigungen"

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr "Alpha-Funktion. Möglicherweise verbessertes Gruppierungsfenster, aber nur grundlegende Gruppierungslogik."

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "„Cloud“-Import/-Export für Verknüpfungseinstellungen"

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr "⚠️⚠️⚠️ Hochgradig experimentell.<0/>Wird in deinen eigenen Profilnotizen gespeichert. (Private) Profilnotizen werden hauptsächlich für andere Profile verwendet und für das eigene Profil versteckt."

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "Hinweis: Diese Funktion verwendet die aktuell eingeloggte Instanz-API des Servers."

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr "Cloak Modus <0>(<1>Text</1> → <2>████</2>)</0>"

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr "Text durch Blöcke ersetzen, nützlich für Screenshots, aus Datenschutzgründen."

#: src/pages/settings.jsx:710
msgid "About"
msgstr "Über"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr "<0>Entwickelt</0> von <1>@cheeaun</1>"

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "Sponsor"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Spenden"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr ""

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "Datenschutzerklärung"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>Seite:</0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>Version:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "Version kopiert"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr "Version kann nicht kopiert werden"

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr "Fehler beim Aktualisieren des Abonnements. Bitte versuchen Sie es erneut."

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr "Fehler beim Entfernen des Abonnements. Bitte versuchen Sie es erneut."

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "Push-Benachrichtigungen (Beta)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr "Push-Benachrichtigungen sind blockiert. Bitte aktivieren Sie diese in Ihren Browsereinstellungen."

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "Von <0>{0}</0> erlauben"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "Jeder"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "Leuten, denen ich folge"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "folgende"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "Folgt"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "Umfragen"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "Post Bearbeitungen"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr "Push-Berechtigung wurde seit deinem letzten Login nicht erteilt. Sie müssen sich erneut <0><1>Anmelden</1>, um Push-Berechtigungen zu erteilen</0>."

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "HINWEIS: Push-Benachrichtigungen funktionieren nur für <0>ein Konto</0>."

#. js-lingui-explicit-id
#: src/pages/status.jsx:658
#: src/pages/status.jsx:1221
msgid "post.title"
msgstr ""

#: src/pages/status.jsx:912
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "Sie sind nicht eingeloggt. Interaktionen (Antworten, Boost usw.) sind nicht möglich."

#: src/pages/status.jsx:932
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "Dieser Post stammt von einer anderen Instanz (<0>{instance}</0>). Interaktionen (Antworten, Boost, usw.) sind nicht möglich."

#: src/pages/status.jsx:960
msgid "Error: {e}"
msgstr "Fehler: {e}"

#: src/pages/status.jsx:967
msgid "Switch to my instance to enable interactions"
msgstr "Zu meiner Instanz wechseln, um Interaktionen zu ermöglichen"

#: src/pages/status.jsx:1069
msgid "Unable to load replies."
msgstr "Antworten konnten nicht geladen werden."

#: src/pages/status.jsx:1181
msgid "Back"
msgstr "Zurück"

#: src/pages/status.jsx:1212
msgid "Go to main post"
msgstr "Zum Hauptbeitrag gehen"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1235
msgid "{0} posts above ‒ Go to top"
msgstr "{0} Posts oberhalb ‒ Gehe nach oben"

#: src/pages/status.jsx:1282
#: src/pages/status.jsx:1345
msgid "Switch to Side Peek view"
msgstr "In der seitlichen Ansicht linsen"

#: src/pages/status.jsx:1346
msgid "Switch to Full view"
msgstr "Zur Vollansicht wechseln"

#: src/pages/status.jsx:1364
msgid "Show all sensitive content"
msgstr "Alle sensiblen Inhalte anzeigen"

#: src/pages/status.jsx:1369
msgid "Experimental"
msgstr "Experimentell"

#: src/pages/status.jsx:1378
msgid "Unable to switch"
msgstr "Wechsel nicht möglich"

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1385
msgid "Switch to post's instance ({0})"
msgstr ""

#: src/pages/status.jsx:1388
msgid "Switch to post's instance"
msgstr "Zur Instanz des Posts wechseln"

#: src/pages/status.jsx:1446
msgid "Unable to load post"
msgstr "Post konnte nicht geladen werden"

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1582
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, one {# Antwort} other {<0>{1}</0> Antworten}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1600
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, one {# Kommentar} other {<0>{0}</0> Kommentare}}"

#: src/pages/status.jsx:1622
msgid "View post with its replies"
msgstr "Post mit Antworten anzeigen"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "Angesagt ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "Angesagte Nachrichten"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr ""

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "Zurück zur Anzeige angesagter Posts"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "Zeige Posts in denen <0>{0}</0> erwähnt wird"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "Angesagte Posts"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "Keine angesagten Posts."

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "Ein minimalistischer, dogmatischer Mastodon Web-Client."

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Mit Mastodon anmelden"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "Registrieren"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "Verbinden Sie Ihr bestehendes Mastodon/Fediverse Konto.<0/>Ihre Zugangsdaten werden nicht auf diesem Server gespeichert."

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr "<0>Entwickelt</0> von <1>@cheeaun</1>. <2>Datenschutzerklärung</2>."

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "Bildschirmfoto des Boosts-Karussells"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "Boost Karussell"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "Originelle Posts und erneut geteilte (geboostete) Posts visuell trennen."

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr "Screenshot von verschachtelten Kommentar-Threads"

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr "Verschachtelte Kommentar-Threads"

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "Folge Unterhaltungen mühelos. Halb einklappbare Antworten."

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr "Screenshot von gruppierten Benachrichtigungen"

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "Gruppierte Benachrichtigungen"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr "Ähnliche Benachrichtigungen werden gruppiert und für mehr Übersichtlichkeit eingeklappt."

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "Screenshot der mehrspaltigen Benutzeroberfläche"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "Einzelne oder mehrere Spalten"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "Standardmäßig eine einzelne Spalte für Zen-Modus-Enthusiasten. Konfigurierbares Mehrspalten-Layout für Power-Nutzer."

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "Bildschirmfoto einer Timeline mehrerer Hashtags mit einem Formular zum Hinzufügen weiterer Hashtags"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "Timeline mehrerer Hashtags"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "Bis zu 5 Hashtags, kombiniert in eine gemeinsame Timeline."

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "Anscheinend blockiert Ihr Browser Popups."

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr "Ein Beitragsentwurf ist derzeit minimiert. Posten oder verwerfen Sie ihn, bevor Sie einen neuen erstellen."

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr "Ein Beitrag ist derzeit offen. Posten oder verwerfen Sie ihn, bevor Sie einen neuen erstellen."

