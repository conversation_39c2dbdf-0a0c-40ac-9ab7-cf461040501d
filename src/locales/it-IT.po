msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: it\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-08-05 04:12\n"
"Last-Translator: \n"
"Language-Team: Italian\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: it\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:141
msgid "Locked"
msgstr "Privato"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:163
msgid "Posts: {0}"
msgstr "Post: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:168
msgid "Last posted: {0}"
msgstr "Ultimo post: {0}"

#: src/components/account-block.jsx:183
#: src/components/account-info.jsx:705
msgid "Automated"
msgstr "Automatizzato"

#: src/components/account-block.jsx:190
#: src/components/account-info.jsx:710
#: src/components/status.jsx:748
msgid "Group"
msgstr "Gruppo"

#: src/components/account-block.jsx:200
msgid "Mutual"
msgstr "Reciproci"

#: src/components/account-block.jsx:204
#: src/components/account-info.jsx:1907
msgid "Requested"
msgstr "Richiesta inviata"

#: src/components/account-block.jsx:208
#: src/components/account-info.jsx:1898
msgid "Following"
msgstr "Segui già"

#: src/components/account-block.jsx:212
#: src/components/account-info.jsx:1212
msgid "Follows you"
msgstr "Ti segue"

#: src/components/account-block.jsx:220
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, one {# seguace} other {# seguaci}}"

#: src/components/account-block.jsx:229
#: src/components/account-info.jsx:753
msgid "Verified"
msgstr "Verificato"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:244
#: src/components/account-info.jsx:892
msgid "Joined <0>{0}</0>"
msgstr "Iscrizione: <0>{0}</0>"

#: src/components/account-info.jsx:64
msgid "Forever"
msgstr "Sempre"

#: src/components/account-info.jsx:398
msgid "Unable to load account."
msgstr "Impossibile caricare account."

#: src/components/account-info.jsx:413
msgid "Go to account page"
msgstr "Vai alla pagina dell'account"

#: src/components/account-info.jsx:442
#: src/components/account-info.jsx:775
msgid "Followers"
msgstr "Seguaci"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:446
#: src/components/account-info.jsx:830
msgid "following.stats"
msgstr "Seguiti"

#: src/components/account-info.jsx:449
#: src/pages/account-statuses.jsx:495
#: src/pages/search.jsx:345
#: src/pages/search.jsx:492
msgid "Posts"
msgstr "Post"

#: src/components/account-info.jsx:457
#: src/components/account-info.jsx:1268
#: src/components/compose.jsx:2819
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1998
#: src/components/status.jsx:2015
#: src/components/status.jsx:2140
#: src/components/status.jsx:2769
#: src/components/status.jsx:2772
#: src/pages/account-statuses.jsx:539
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1306
#: src/pages/trending.jsx:474
msgid "More"
msgstr "Altro"

#: src/components/account-info.jsx:469
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> ha indicato che il suo nuovo account è ora:"

#: src/components/account-info.jsx:614
#: src/components/account-info.jsx:1496
msgid "Handle copied"
msgstr "Nome utente copiato"

#: src/components/account-info.jsx:617
#: src/components/account-info.jsx:1499
msgid "Unable to copy handle"
msgstr "Impossibile copiare nome utente"

#: src/components/account-info.jsx:623
#: src/components/account-info.jsx:1505
msgid "Copy handle"
msgstr "Copia nome utente"

#: src/components/account-info.jsx:629
msgid "Go to original profile page"
msgstr "Vai alla pagina originale del profilo"

#: src/components/account-info.jsx:647
msgid "View profile image"
msgstr "Visualizza immagine di profilo"

#: src/components/account-info.jsx:665
msgid "View profile header"
msgstr "Visualizza immagine di copertina"

#: src/components/account-info.jsx:681
#: src/components/account-info.jsx:1806
#: src/components/account-info.jsx:2332
msgid "Edit profile"
msgstr "Modifica profilo"

#: src/components/account-info.jsx:700
msgid "In Memoriam"
msgstr "In memoriam"

#: src/components/account-info.jsx:782
#: src/components/account-info.jsx:840
msgid "This user has chosen to not make this information available."
msgstr "L'utente ha scelto di non condividere questa informazione."

#. placeholder {0}: shortenNumber(followersCount)
#. placeholder {1}: shortenNumber(followersCount)
#: src/components/account-info.jsx:802
msgid "{followersCount, plural, one {<0>{0}</0> Follower} other {<1>{1}</1> Followers}}"
msgstr ""

#. placeholder {0}: shortenNumber(followingCount)
#: src/components/account-info.jsx:846
msgid "{followingCount, plural, other {<0>{0}</0> Following}}"
msgstr ""

#. placeholder {0}: shortenNumber(statusesCount)
#. placeholder {1}: shortenNumber(statusesCount)
#: src/components/account-info.jsx:870
msgid "{statusesCount, plural, one {<0>{0}</0> Post} other {<1>{1}</1> Posts}}"
msgstr ""

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:917
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0} post originali, {1} risposte, {2} reblog"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:933
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural,one {{1, plural,one {Ultimo post nell'ultimo giorno}other {Ultimo post negli ultimi {2} giorni}}} other {{3, plural,one {Ultimi {4} post nell'ultimo giorno} other {Ultimi {5} post negli ultimi {6} giorni}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:949
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {Ultimo post nell'ultimo anno(i)} other {Ultimi {1} post nell'ultimo anno(i)}}"

#: src/components/account-info.jsx:974
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "Originali"

#: src/components/account-info.jsx:978
#: src/components/status.jsx:2553
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1025
#: src/pages/status.jsx:1652
msgid "Replies"
msgstr "Risposte"

#: src/components/account-info.jsx:982
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "Reblog"

#: src/components/account-info.jsx:988
msgid "Post stats unavailable."
msgstr "Statistiche post non disponibili."

#: src/components/account-info.jsx:1019
msgid "View post stats"
msgstr "Visualizza statistiche post"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1216
msgid "Last post: <0>{0}</0>"
msgstr "Ultimo post: <0>{0}</0>"

#: src/components/account-info.jsx:1230
msgid "Muted"
msgstr "Silenziato"

#: src/components/account-info.jsx:1235
msgid "Blocked"
msgstr "Bloccato"

#: src/components/account-info.jsx:1244
msgid "Private note"
msgstr "Nota privata"

#: src/components/account-info.jsx:1301
msgid "Mention <0>@{username}</0>"
msgstr "Menziona  <0>@{username}</0>"

#: src/components/account-info.jsx:1313
msgid "Translate bio"
msgstr "Traduci bio"

#: src/components/account-info.jsx:1324
msgid "Edit private note"
msgstr "Modifica nota privata"

#: src/components/account-info.jsx:1324
msgid "Add private note"
msgstr "Aggiungi nota privata"

#: src/components/account-info.jsx:1344
msgid "Notifications enabled for @{username}'s posts."
msgstr "Notifiche attivate per i post di @{username}."

#: src/components/account-info.jsx:1345
msgid " Notifications disabled for @{username}'s posts."
msgstr "Notifiche disattivate per i post di {username}."

#: src/components/account-info.jsx:1357
msgid "Disable notifications"
msgstr "Disattiva notifiche"

#: src/components/account-info.jsx:1358
msgid "Enable notifications"
msgstr "Attiva notifiche"

#: src/components/account-info.jsx:1375
msgid "Boosts from @{username} enabled."
msgstr "Reblog da @{username} attivati."

#: src/components/account-info.jsx:1376
msgid "Boosts from @{username} disabled."
msgstr "Reblog da @{username} disattivati."

#: src/components/account-info.jsx:1387
msgid "Disable boosts"
msgstr "Disattiva reblog"

#: src/components/account-info.jsx:1387
msgid "Enable boosts"
msgstr "Attiva reblog"

#: src/components/account-info.jsx:1406
msgid "@{username} is no longer featured on your profile."
msgstr "@{username} non è più fra i consigliati sul tuo profilo."

#: src/components/account-info.jsx:1416
msgid "@{username} is now featured on your profile."
msgstr "@{username} è ora fra i consigliati sul tuo profilo."

#: src/components/account-info.jsx:1424
msgid "Unable to unfeature @{username} on your profile."
msgstr "Impossibile rimuovere @{username} dai consigliati sul tuo profilo."

#: src/components/account-info.jsx:1428
msgid "Unable to feature @{username} on your profile."
msgstr "Impossibile aggiungere @{username} ai consigliati sul tuo profilo."

#: src/components/account-info.jsx:1437
msgid "Don't feature on profile"
msgstr "Non consigliare sul profilo"

#: src/components/account-info.jsx:1438
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "Consiglia sul profilo"

#: src/components/account-info.jsx:1447
msgid "Show featured profiles"
msgstr "Mostra profili consigliati"

#: src/components/account-info.jsx:1462
#: src/components/account-info.jsx:1472
#: src/components/account-info.jsx:2078
msgid "Add/Remove from Lists"
msgstr "Aggiungi/rimuovi dalle liste"

#: src/components/account-info.jsx:1522
#: src/components/status.jsx:1420
msgid "Link copied"
msgstr "Link copiato"

#: src/components/account-info.jsx:1525
#: src/components/status.jsx:1423
msgid "Unable to copy link"
msgstr "Impossibile copiare link"

#: src/components/account-info.jsx:1531
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1429
#: src/components/status.jsx:3547
msgid "Copy"
msgstr "Copia"

#: src/components/account-info.jsx:1546
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1445
msgid "Sharing doesn't seem to work."
msgstr "La condivisione non sembra funzionare."

#: src/components/account-info.jsx:1552
#: src/components/status.jsx:1451
msgid "Share…"
msgstr "Condividi…"

#: src/components/account-info.jsx:1572
msgid "Unmuted @{username}"
msgstr "Hai riattivato @{username}"

#: src/components/account-info.jsx:1584
msgid "Unmute <0>@{username}</0>"
msgstr "Riattiva <0>@{username}</0>"

#: src/components/account-info.jsx:1600
msgid "Mute <0>@{username}</0>…"
msgstr "Silenzia <0>@{username}</0>…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1632
msgid "Muted @{username} for {0}"
msgstr "Hai silenziato @{username} per {0}"

#: src/components/account-info.jsx:1644
msgid "Unable to mute @{username}"
msgstr "Impossibile silenziare @{username}"

#: src/components/account-info.jsx:1665
msgid "Remove <0>@{username}</0> from followers?"
msgstr "Rimuovere <0>@{username}</0> dai seguaci?"

#: src/components/account-info.jsx:1685
msgid "@{username} removed from followers"
msgstr "Hai rimosso @{username} dai seguaci"

#: src/components/account-info.jsx:1697
msgid "Remove follower…"
msgstr "Rimuovi seguace…"

#: src/components/account-info.jsx:1708
msgid "Block <0>@{username}</0>?"
msgstr "Bloccare <0>@{username}</0>?"

#: src/components/account-info.jsx:1732
msgid "Unblocked @{username}"
msgstr "Hai sbloccato @{username}"

#: src/components/account-info.jsx:1740
msgid "Blocked @{username}"
msgstr "Hai bloccato @{username}"

#: src/components/account-info.jsx:1748
msgid "Unable to unblock @{username}"
msgstr "Impossibile sbloccare @{username}"

#: src/components/account-info.jsx:1750
msgid "Unable to block @{username}"
msgstr "Impossibile bloccare @{username}"

#: src/components/account-info.jsx:1760
msgid "Unblock <0>@{username}</0>"
msgstr "Sblocca <0>@{username}</0>"

#: src/components/account-info.jsx:1769
msgid "Block <0>@{username}</0>…"
msgstr "Blocca <0>@{username}</0>…"

#: src/components/account-info.jsx:1786
msgid "Report <0>@{username}</0>…"
msgstr "Segnala <0>@{username}</0>…"

#: src/components/account-info.jsx:1842
msgid "Withdraw follow request?"
msgstr "Ritirare richiesta di seguire?"

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1843
msgid "Unfollow @{0}?"
msgstr "Smettere di seguire @{0}?"

#: src/components/account-info.jsx:1901
msgid "Unfollow…"
msgstr "Smetti di seguire…"

#: src/components/account-info.jsx:1910
msgid "Withdraw…"
msgstr "Ritira…"

#: src/components/account-info.jsx:1917
#: src/components/account-info.jsx:1921
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "Segui"

#: src/components/account-info.jsx:2018
#: src/components/account-info.jsx:2073
#: src/components/account-info.jsx:2207
#: src/components/account-info.jsx:2327
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:892
#: src/components/compose.jsx:2775
#: src/components/compose.jsx:3255
#: src/components/compose.jsx:3464
#: src/components/compose.jsx:3694
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:118
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3271
#: src/components/status.jsx:3511
#: src/components/status.jsx:4020
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1393
msgid "Close"
msgstr "Chiudi"

#: src/components/account-info.jsx:2023
msgid "Translated Bio"
msgstr "Bio tradotta"

#: src/components/account-info.jsx:2118
msgid "Unable to remove from list."
msgstr "Impossibile rimuovere dalla lista."

#: src/components/account-info.jsx:2119
msgid "Unable to add to list."
msgstr "Impossibile aggiungere alla lista."

#: src/components/account-info.jsx:2138
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "Impossibile caricare liste."

#: src/components/account-info.jsx:2142
msgid "No lists."
msgstr "Nessuna lista."

#: src/components/account-info.jsx:2153
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "Nuova lista"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2212
msgid "Private note about <0>@{0}</0>"
msgstr "Nota privata su <0>@{0}</0>"

#: src/components/account-info.jsx:2242
msgid "Unable to update private note."
msgstr "Impossibile aggiornare nota privata."

#: src/components/account-info.jsx:2265
#: src/components/account-info.jsx:2563
msgid "Cancel"
msgstr "Annulla"

#: src/components/account-info.jsx:2270
msgid "Save & close"
msgstr "Salva e chiudi"

#: src/components/account-info.jsx:2387
msgid "Unable to update profile."
msgstr "Impossibile aggiornare profilo."

#: src/components/account-info.jsx:2394
msgid "Header picture"
msgstr "Foto di copertina"

#: src/components/account-info.jsx:2446
msgid "Profile picture"
msgstr "Foto profilo"

#: src/components/account-info.jsx:2498
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "Nome"

#: src/components/account-info.jsx:2511
msgid "Bio"
msgstr "Bio"

#: src/components/account-info.jsx:2524
msgid "Extra fields"
msgstr "Campi extra"

#: src/components/account-info.jsx:2530
msgid "Label"
msgstr "Etichetta"

#: src/components/account-info.jsx:2533
msgid "Content"
msgstr "Contenuto"

#: src/components/account-info.jsx:2566
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "Salva"

#: src/components/account-info.jsx:2620
msgid "username"
msgstr "nome utente"

#: src/components/account-info.jsx:2624
msgid "server domain name"
msgstr "nome dominio del server"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2690
msgid "Profiles featured by @{0}"
msgstr "Profili consigliati da @{0}"

#: src/components/account-info.jsx:2716
msgid "No featured profiles."
msgstr "Nessun profilo consigliato."

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "Modalità mantello disattivata"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "Modalità mantello attivata"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "Home"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "Componi"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr "Post programmati"

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr "Aggiungi a thread"

#: src/components/compose.jsx:212
msgid "Take photo or video"
msgstr "Scatta foto o video"

#: src/components/compose.jsx:213
msgid "Add media"
msgstr "Aggiungi contenuti"

#: src/components/compose.jsx:214
msgid "Add custom emoji"
msgstr "Aggiungi emoji personalizzata"

#: src/components/compose.jsx:215
msgid "Add GIF"
msgstr "Aggiungi GIF"

#: src/components/compose.jsx:216
msgid "Add poll"
msgstr "Aggiungi sondaggio"

#: src/components/compose.jsx:217
msgid "Schedule post"
msgstr "Programma post"

#: src/components/compose.jsx:417
msgid "You have unsaved changes. Discard this post?"
msgstr "Ci sono modifiche non salvate. Scartare il post?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:655
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr "{0, plural, one {Il file {1} non è supportato.} other {I file {2} non sono supportati.}}"

#: src/components/compose.jsx:665
#: src/components/compose.jsx:683
#: src/components/compose.jsx:1795
#: src/components/compose.jsx:1930
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, one {Puoi allegare fino a 1 documento.} other {Puoi allegare fino a # documenti.}}"

#: src/components/compose.jsx:873
msgid "Pop out"
msgstr "Scollega"

#: src/components/compose.jsx:880
msgid "Minimize"
msgstr "Contrai"

#: src/components/compose.jsx:916
msgid "Looks like you closed the parent window."
msgstr "Sembra tu abbia chiuso la finestra genitore."

#: src/components/compose.jsx:923
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Sembra tu abbia già un campo di composizione aperto nella finestra genitore ed esso sia in fase di pubblicazione. Attendi il completamento e riprova."

#: src/components/compose.jsx:928
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Sembra tu abbia già un campo di composizione aperto nella finestra genitore. Collegare questa finestra scarterà le modifiche effettuate nella finestra genitore. Proseguire?"

#: src/components/compose.jsx:971
msgid "Pop in"
msgstr "Collega"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:981
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "In risposta al post di @{0} (<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:991
msgid "Replying to @{0}’s post"
msgstr "In risposta al post di @{0}"

#: src/components/compose.jsx:1004
msgid "Editing source post"
msgstr "Modificando il post originale"

#: src/components/compose.jsx:1057
msgid "Poll must have at least 2 options"
msgstr "Il sondaggio deve avere almeno 2 opzioni"

#: src/components/compose.jsx:1061
msgid "Some poll choices are empty"
msgstr "Alcune scelte del sondaggio sono vuote"

#: src/components/compose.jsx:1074
msgid "Some media have no descriptions. Continue?"
msgstr "Alcuni contenuti multimediali sono senza descrizione. Continuare?"

#: src/components/compose.jsx:1126
msgid "Attachment #{i} failed"
msgstr "Allegato #{i} fallito"

#: src/components/compose.jsx:1222
#: src/components/status.jsx:2328
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "Avviso contenuto"

#: src/components/compose.jsx:1238
msgid "Content warning or sensitive media"
msgstr "Avviso contenuto o contenuti sensibili"

#: src/components/compose.jsx:1274
#: src/components/status.jsx:99
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "Pubblico"

#: src/components/compose.jsx:1279
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:100
msgid "Local"
msgstr "Locale"

#: src/components/compose.jsx:1283
#: src/components/status.jsx:101
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "Non in lista"

#: src/components/compose.jsx:1286
#: src/components/status.jsx:102
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "Solo seguaci"

#: src/components/compose.jsx:1289
#: src/components/status.jsx:103
#: src/components/status.jsx:2204
msgid "Private mention"
msgstr "Menzione privata"

#: src/components/compose.jsx:1298
msgid "Post your reply"
msgstr "Pubblica la tua risposta"

#: src/components/compose.jsx:1300
msgid "Edit your post"
msgstr "Modifica il tuo post"

#: src/components/compose.jsx:1301
msgid "What are you doing?"
msgstr "Che stai facendo?"

#: src/components/compose.jsx:1380
msgid "Mark media as sensitive"
msgstr "Contrassegna contenuti come sensibili"

#: src/components/compose.jsx:1417
msgid "Posting on <0/>"
msgstr "Data pubblicazione: <0/>"

#: src/components/compose.jsx:1448
#: src/components/compose.jsx:3313
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "Aggiungi"

#: src/components/compose.jsx:1676
msgid "Schedule"
msgstr "Programma"

#: src/components/compose.jsx:1678
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1192
#: src/components/status.jsx:1978
#: src/components/status.jsx:1979
#: src/components/status.jsx:2673
msgid "Reply"
msgstr "Rispondi"

#: src/components/compose.jsx:1680
msgid "Update"
msgstr "Aggiorna"

#: src/components/compose.jsx:1681
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Pubblica"

#: src/components/compose.jsx:1807
msgid "Downloading GIF…"
msgstr "Scarico GIF…"

#: src/components/compose.jsx:1835
msgid "Failed to download GIF"
msgstr "Download GIF fallito"

#: src/components/compose.jsx:2060
#: src/components/compose.jsx:2153
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "Altro…"

#: src/components/compose.jsx:2589
msgid "Uploaded"
msgstr "Caricato"

#: src/components/compose.jsx:2602
msgid "Image description"
msgstr "Descrizione immagine"

#: src/components/compose.jsx:2603
msgid "Video description"
msgstr "Descrizione video"

#: src/components/compose.jsx:2604
msgid "Audio description"
msgstr "Descrizione audio"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2639
#: src/components/compose.jsx:2659
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "File troppo pesante. Potrebbero verificarsi errori nel caricamento. Prova a ridurre le dimensioni del file da {0} a {1} o meno."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2651
#: src/components/compose.jsx:2671
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr "Dimensioni eccessive. Potrebbero verificarsi errori nel caricamento. Prova a ridurre le dimensioni da {0}×{1}px a {2}×{3}px."

#: src/components/compose.jsx:2679
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "Frequenza dei fotogrammi troppo alta. Potrebbero verificarsi errori nel caricamento."

#: src/components/compose.jsx:2739
#: src/components/compose.jsx:2989
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "Rimuovi"

#: src/components/compose.jsx:2756
#: src/compose.jsx:84
msgid "Error"
msgstr "Errore"

#: src/components/compose.jsx:2781
msgid "Edit image description"
msgstr "Modifica descrizione immagine"

#: src/components/compose.jsx:2782
msgid "Edit video description"
msgstr "Modifica descrizione video"

#: src/components/compose.jsx:2783
msgid "Edit audio description"
msgstr "Modifica descrizione audio"

#: src/components/compose.jsx:2828
#: src/components/compose.jsx:2877
msgid "Generating description. Please wait…"
msgstr "Genero descrizione. Attendere…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2848
msgid "Failed to generate description: {0}"
msgstr "Generazione descrizione fallita: {0}"

#: src/components/compose.jsx:2849
msgid "Failed to generate description"
msgstr "Generazione descrizione fallita"

#: src/components/compose.jsx:2861
#: src/components/compose.jsx:2867
#: src/components/compose.jsx:2913
msgid "Generate description…"
msgstr "Genera descrizione…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2900
msgid "Failed to generate description{0}"
msgstr "Generazione descrizione fallita{0}"

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2915
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>— sperimentale</0>"

#: src/components/compose.jsx:2934
msgid "Done"
msgstr "Fine"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2970
msgid "Choice {0}"
msgstr "Opzione {0}"

#: src/components/compose.jsx:3017
msgid "Multiple choices"
msgstr "Scelta multipla"

#: src/components/compose.jsx:3020
msgid "Duration"
msgstr "Durata"

#: src/components/compose.jsx:3051
msgid "Remove poll"
msgstr "Rimuovi sondaggio"

#: src/components/compose.jsx:3272
msgid "Search accounts"
msgstr "Cerca account"

#: src/components/compose.jsx:3326
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "Errore caricamento account"

#: src/components/compose.jsx:3470
msgid "Custom emojis"
msgstr "Emoji personalizzate"

#: src/components/compose.jsx:3490
msgid "Search emoji"
msgstr "Cerca emoji"

#: src/components/compose.jsx:3521
msgid "Error loading custom emojis"
msgstr "Errore caricamento emoji personalizzate"

#: src/components/compose.jsx:3532
msgid "Recently used"
msgstr "Usate di recente"

#: src/components/compose.jsx:3533
msgid "Others"
msgstr "Altre"

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3571
msgid "{0} more…"
msgstr "{0} altri…"

#: src/components/compose.jsx:3709
msgid "Search GIFs"
msgstr "Cerca GIF"

#: src/components/compose.jsx:3724
msgid "Powered by GIPHY"
msgstr "Fornito da GIPHY"

#: src/components/compose.jsx:3732
msgid "Type to search GIFs"
msgstr "Scrivi per cercare GIF"

#: src/components/compose.jsx:3830
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "Indietro"

#: src/components/compose.jsx:3848
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "Avanti"

#: src/components/compose.jsx:3865
msgid "Error loading GIFs"
msgstr "Errore caricamento GIF"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "Bozze in sospeso"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "Sembra tu abbia delle bozze in sospeso. Riprendiamo da lì."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "Eliminare questa bozza?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "Errore eliminazione bozza! Riprova."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1595
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "Elimina…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "Errore nel recupero dello stato in risposta!"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "Eliminare tutte le bozze?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "Errore eliminazione bozze! Riprova."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "Elimina tutte…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "Nessuna bozza."

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "Sondaggio"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:375
msgid "Media"
msgstr "Contenuti"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "Apri in nuova finestra"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "Accetta"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "Rifiuta"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "Accettata"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "Rifiutata"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:335
#: src/pages/search.jsx:368
msgid "Accounts"
msgstr "Account"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:562
#: src/pages/status.jsx:1426
msgid "Show more…"
msgstr "Mostra altro…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:567
msgid "The end."
msgstr "Fine."

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "Nulla da mostrare"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "Abbreviazioni da tastiera"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "Aiuto abbreviazioni da tastiera"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "Post successivo"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "Post precedente"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "Salta il carosello e vai al post successivo"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Maiusc</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "Salta il carosello e vai al post precedente"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Maiusc</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "Carica nuovi post"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "Apri dettagli post"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Invio</0> o <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "Espandi avviso contenuto o<0/>espandi/comprimi thread"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "Chiudi post o finestre"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc</0> o <1>Backspace</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "Evidenzia colonna in modalità multi-colonna"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "Da <0>1</0> a <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr "Evidenzia colonna successiva in modalità multi-colonna"

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr "Evidenzia colonna precedente in modalità multi-colonna"

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "Componi post"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "Componi post (nuova finestra)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Maiusc</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "Invia post"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Ctrl</0> + <1>Invio</1> o <2>⌘</2> + <3>Invio</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:204
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:47
#: src/pages/search.jsx:317
msgid "Search"
msgstr "Cerca"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "Rispondi (nuova finestra)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Maiusc</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "Mi piace (preferiti)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> o <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1200
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
#: src/components/status.jsx:2724
msgid "Boost"
msgstr "Reblogga"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Maiusc</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
#: src/components/status.jsx:2749
msgid "Bookmark"
msgstr "Aggiungi ai segnalibri"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "Imposta modalità mantello"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Maiusc</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "Modifica lista"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "Impossibile modificare lista."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "Impossibile creare lista."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "Mostra risposte ai membri della lista"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "Mostra risposte alle persone che seguo"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "Non mostrare risposte"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "Nascondi i post di questa lista da Home/Seguiti"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "Crea"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "Eliminare questa lista?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "Impossibile eliminare lista."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr "I post in questa lista sono nascosti da Home/Seguiti"

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "Descrizione contenuti"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1306
#: src/components/status.jsx:1315
#: src/components/translation-block.jsx:239
msgid "Translate"
msgstr "Traduci"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1334
msgid "Speak"
msgstr "Leggi"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "Apri contenuti originali in una nuova finestra"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "Apri contenuti originali"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "Cerco di descrivere l'immagine. Attendere…"

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "Descrizione immagine fallita"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "Descrivi immagine…"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "Visualizza post"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "Contenuti sensibili"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "Filtrato: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3850
#: src/components/status.jsx:3946
#: src/components/status.jsx:4024
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "Filtrato"

#: src/components/media.jsx:479
msgid "Open file"
msgstr "Apri file"

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr "Post programmato"

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "Post pubblicato. Dai un'occhiata."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr "Risposta programmata"

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "Risposta pubblicata. Dai un'occhiata."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "Post aggiornato. Dai un'occhiata."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "Menu"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "Ricaricare pagina ora per aggiornare?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "Nuovo aggiornamento disponibile…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr "Seguiti"

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "Recupera"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "Menzioni"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "Notifiche"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "Nuovo"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "Profilo"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "Segnalibri"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "Mi piace"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "Hashtag seguiti"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:341
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "FIltri"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "Utenti silenziati"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "Utenti silenziati…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "Utenti bloccati"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "Utenti bloccati…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "Account…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:199
#: src/pages/status.jsx:925
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "Accedi"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "In tendenza"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "Federata"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "Scorciatoie / Colonne…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "Impostazioni…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "Liste"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "Tutte le liste"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "Notifica"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "Questa notifica viene da un altro account."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "Visualizza tutte le notifiche"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} ha reagito al tuo post con {emojiObject}"

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} ha pubblicato un post."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr "{count, plural,=1 {{postsCount, plural,=1 {{postType, select, reply {{account} ha rebloggato la tua risposta.} other {{account} ha rebloggato il tuo post.}}} other {{account} ha rebloggato {postsCount} dei tuoi post.}}}other {{postType, select, reply {<0><1>{0}</1> persone</0> hanno rebloggato la tua risposta.}other {<2><3>{1}</3> persone</2> hanno rebloggato il tuo post.}}}}"

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, =1 {{account} ti ha iniziato a seguire.} other {<0><1>{0}</1> persone</0> ti hanno iniziato a seguire.}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} ha richiesto di seguirti."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} ha messo Mi piace alla tua risposta.} other {{account} ha messo Mi piace al tuo post.}}} other {{account} ha messo {postsCount} Mi piace ai tuoi post.}}} other {{postType, select, reply {<0><1>{0}</1> persone</0> hanno messo Mi piace alla tua risposta.} other {<2><3>{1}</3> persone</2> hanno messo mi piace al tuo post.}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "Un sondaggio in cui hai votato o che hai creato è terminato."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "Un sondaggio che hai creato è terminato."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "Un sondaggio in cui hai votato è terminato."

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "Un post con cui hai interagito è stato modificato."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} ha rebloggato e ha messo Mi piace alla tua risposta.} other {{account} ha rebloggato e messo Mi piace al tuo post.}}} other {{account} ha rebloggato e messo Mi piace a {postsCount} dei tuoi post.}}} other {{postType, select, reply {<0><1>{0}</1> persone</0> hanno rebloggato e messo Mi piace alla tua risposta.} other {<2><3>{1}</3> persone</2> hanno rebloggato e messo Mi piace al tuo post.}}}}"

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} ha effettuato l'iscrizione."

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} ha segnalato {targetAccount}"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "Connessione con <0>{name}</0> persa."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "Avviso di moderazione"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr "Il tuo #Wrapstodon del {year} è qui!"

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "Un amministratore da <0>{from}</0> ha sospeso <1>{targetName}</1>, il che significa che non puoi più riceverne gli aggiornamenti o interagirvi."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "Un amministratore da <0>{from}</0> ha bloccato <1>{targetName}</1>. Seguaci interessati: {followersCount}, seguiti: {followingCount}."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "Hai bloccato <0>{targetName}</0>. Seguaci rimossi: {followersCount}, seguiti: {followingCount}."

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "Il tuo account ha ricevuto un avvertimento di moderazione."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "Il tuo account è stato disabilitato."

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "Alcuni dei tuoi post sono stati contrassegnati come sensibili."

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "Alcuni dei tuoi post sono stati eliminati."

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "Da ora in poi i tuoi post verranno contrassegnati come sensibili."

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "Il tuo account è stato limitato."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "Il tuo account è stato sospeso."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[Tipo notifica sconosciuto: {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1277
#: src/components/status.jsx:1287
msgid "Boosted/Liked by…"
msgstr "Rebloggato da/Piace a…"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "Piace a…"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "Rebloggato da…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "Seguito da…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "Scopri di più <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr "Visualizza #Wrapstodon"

#: src/components/notification.jsx:801
#: src/components/status.jsx:486
msgid "Read more →"
msgstr "Leggi di più →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "Votato"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr "{optionVotesCount, plural, one {# voto} other {# voti}}"

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "Nascondi risultati"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "Vota"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1295
#: src/pages/status.jsx:1318
msgid "Refresh"
msgstr "Ricarica"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "Mostra risultati"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> voto} other {<1>{1}</1> voti}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, one {<0>{0}</0> votante} other {<1>{1}</1> votanti}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "Terminato <0/>"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "Terminato"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "Termina <0/>"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "Terminando"

#: src/components/recent-searches.jsx:27
msgid "Cleared recent searches"
msgstr "Ricerche recenti cancellate"

#: src/components/recent-searches.jsx:49
#: src/components/search-form.jsx:328
msgid "Recent searches"
msgstr "Ricerche recenti"

#: src/components/recent-searches.jsx:59
msgid "Clear all"
msgstr "Cancella tutte"

#: src/components/recent-searches.jsx:94
#: src/pages/account-statuses.jsx:334
msgid "Clear"
msgstr "Cancella"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0}s"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0}m"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0}h"

#: src/components/report-modal.jsx:30
msgid "Spam"
msgstr "Spam"

#: src/components/report-modal.jsx:31
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "Link dannosi, interazioni false, o risposte ripetitive"

#: src/components/report-modal.jsx:34
msgid "Illegal"
msgstr "Illegale"

#: src/components/report-modal.jsx:35
msgid "Violates the law of your or the server's country"
msgstr "Viola la legge del tuo paese o di quello del server"

#: src/components/report-modal.jsx:38
msgid "Server rule violation"
msgstr "Violazione regole server"

#: src/components/report-modal.jsx:39
msgid "Breaks specific server rules"
msgstr "Viola regole specifiche del server"

#: src/components/report-modal.jsx:40
msgid "Violation"
msgstr "Violazione"

#: src/components/report-modal.jsx:43
msgid "Other"
msgstr "Altro"

#: src/components/report-modal.jsx:44
msgid "Issue doesn't fit other categories"
msgstr "Il problema non rientra nelle altre categorie"

#: src/components/report-modal.jsx:111
msgid "Report Post"
msgstr "Segnala post"

#: src/components/report-modal.jsx:111
msgid "Report @{username}"
msgstr "Segnala @{username}"

#: src/components/report-modal.jsx:147
msgid "Pending review"
msgstr "Valutazione in sospeso"

#: src/components/report-modal.jsx:189
msgid "Post reported"
msgstr "Post segnalato"

#: src/components/report-modal.jsx:189
msgid "Profile reported"
msgstr "Profilo segnalato"

#: src/components/report-modal.jsx:197
msgid "Unable to report post"
msgstr "Impossibile segnalare post"

#: src/components/report-modal.jsx:198
msgid "Unable to report profile"
msgstr "Impossibile segnalare profilo"

#: src/components/report-modal.jsx:206
msgid "What's the issue with this post?"
msgstr "Qual è il problema con questo post?"

#: src/components/report-modal.jsx:207
msgid "What's the issue with this profile?"
msgstr "Qual è il problema con questo profilo?"

#: src/components/report-modal.jsx:276
msgid "Additional info"
msgstr "Informazioni aggiuntive"

#: src/components/report-modal.jsx:299
msgid "Forward to <0>{domain}</0>"
msgstr "Inoltra a <0>{domain}</0>"

#: src/components/report-modal.jsx:309
msgid "Send Report"
msgstr "Invia segnalazione"

#: src/components/report-modal.jsx:318
msgid "Muted {username}"
msgstr "Hai silenziato {username}"

#: src/components/report-modal.jsx:321
msgid "Unable to mute {username}"
msgstr "Impossibile silenziare {username}"

#: src/components/report-modal.jsx:326
msgid "Send Report <0>+ Mute profile</0>"
msgstr "Invia segnalazione <0>+ Silenzia profilo</0>"

#: src/components/report-modal.jsx:337
msgid "Blocked {username}"
msgstr "Hai bloccato {username}"

#: src/components/report-modal.jsx:340
msgid "Unable to block {username}"
msgstr "Impossibile bloccare {username}"

#: src/components/report-modal.jsx:345
msgid "Send Report <0>+ Block profile</0>"
msgstr "Invia segnalazione <0>+ Blocca profilo</0>"

#: src/components/search-form.jsx:18
msgid "Posts with <0>{query}</0>"
msgstr "Post con <0>{query}</0>"

#: src/components/search-form.jsx:26
msgid "Accounts with <0>{query}</0>"
msgstr "Account con <0>{query}</0>"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:34
msgid "Posts tagged with <0>#{0}</0>"
msgstr "Post taggati con <0>#{0}</0>"

#: src/components/search-form.jsx:43
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>- account, hashtag e post</0>"

#: src/components/search-form.jsx:135
msgid "Look up <0>{query}</0>"
msgstr "Cerca <0>{query}</0>"

#: src/components/search-form.jsx:361
#: src/pages/home.jsx:251
msgid "See all"
msgstr "Mostra tutto"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "Home / Seguiti"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "Pubblica (Locale / Federata)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "Account"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "Hashtag"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "ID lista"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "Solo locale"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:203
msgid "Instance"
msgstr "Istanza"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "Opzionale, es. mastodon.social"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "Termine di ricerca"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "Opzionale, tranne per la modalità multi-colonna"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "es. PixelArt (max 5, separati da spazi)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "Solo media"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "Scorciatoie"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "beta"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "Specifica una lista di scorciatoie che apparirà come:"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "Tasto fluttuante"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "Barra dei pannelli/menu"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "Multi-colonna"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "Non disponibile nell'attuale modalità di visualizzazione"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "Sposta su"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "Sposta giù"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1557
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "Modifica"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "Aggiungi più di una scorciatoia/colonna per far sì che funzioni."

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "Nessuna colonna. Usa il tasto Aggiungi colonna."

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "Nessuna scorciatoia. Usa il tasto Aggiungi scorciatoia."

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "Non sai cosa aggiungere?<0/>Prova prima con <1>Home / Seguiti e Notifiche</1>."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "Massimo {SHORTCUTS_LIMIT} colonne"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "Massimo {SHORTCUTS_LIMIT} scorciatoie"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "Importa/esporta"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "Aggiungi colonna…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "Aggiungi scorciatoia…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "Una lista specifica è opzionale. Per la modalità multi-colonna è richiesta una lista, altrimenti la colonna non verrà mostrata."

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "Per la modalità multi-colonna è richiesto un termine di ricerca, altrimenti la colonna non verrà mostrata."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "Sono supportati più hashtag, separati da uno spazio."

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "Modifica scorciatoia"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "Aggiungi scorciatoia"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "Timeline"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "Lista"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "Importa/Esporta <0>Scorciatoie</0>"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "Importa"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "Incolla scorciatoie qui"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "Scarico le scorciatoie salvate dal server dell'istanza…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "Impossibile scaricare scorciatoie"

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "Scarica scorciatoie dal server dell'istanza"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "* Esiste nelle attuali scorciatoie"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "La lista potrebbe non funzionare se proviene da un altro account."

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "Formato impostazioni non valido"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "Apporre alle scorciatoie attuali?"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr "Verranno apposte solo le scorciatoie non presenti fra quelle attuali."

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "Nessuna nuova scorciatoia da importare"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "Scorciatoie importate. È stato superato il limite di {SHORTCUTS_LIMIT}, quindi il resto non verrà importato."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "Scorciatoie importate"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "Importa e apponi…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "Sovrascrivere le scorciatoie attuali?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "Importare scorciatoie?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "o sovrascrivi…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "Importa…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "Esporta"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "Scorciatoie copiate"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "Impossibile copiare scorciatoie"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "Impostazioni scorciatoie copiate"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "Impossibile copiare impostazioni scorciatoie"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "Condividi"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "Salvo scorciatoie sul server dell'istanza…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "Scorciatoie salvate"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "Impossibile salvare scorciatoie"

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "Sincronizza con server istanza"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0, plural, one {# carattere} other {# caratteri}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "JSON scorciatoie originale"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "Importa/esporta impostazioni da/su un server istanza (molto sperimentale)"

#: src/components/status.jsx:351
msgid "Unable to format math"
msgstr "Impossibile formattare matematica"

#: src/components/status.jsx:365
msgid "Math expressions found."
msgstr "Espressione matematica trovata."

#. Action to switch from rendered math back to raw (LaTeX) markup
#: src/components/status.jsx:368
msgid "Show markup"
msgstr "Mostra markup"

#. Action to render math expressions from raw (LaTeX) markup
#: src/components/status.jsx:373
msgid "Format math"
msgstr "Formatta matematica"

#: src/components/status.jsx:772
msgid "<0/> <1>boosted</1>"
msgstr "<0/> <1>ha rebloggato</1>"

#: src/components/status.jsx:875
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "Spiacenti, l'istanza a cui sei connesso non può interagire coi post di un'altra istanza."

#. placeholder {0}: username || acct
#: src/components/status.jsx:1029
msgid "Unliked @{0}'s post"
msgstr "Hai annullato il Mi piace al post di @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1030
msgid "Liked @{0}'s post"
msgstr "Hai messo Mi piace al post di @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1069
msgid "Unbookmarked @{0}'s post"
msgstr "Post di @{0} rimosso dai segnalibri"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1070
msgid "Bookmarked @{0}'s post"
msgstr "Post di @{0} aggiunto ai segnalibri"

#: src/components/status.jsx:1169
msgid "Some media have no descriptions."
msgstr "Alcuni contenuti non hanno una descrizione."

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1176
msgid "Old post (<0>{0}</0>)"
msgstr "Post vecchio (<0>{0}</0>)"

#: src/components/status.jsx:1200
#: src/components/status.jsx:1240
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
msgid "Unboost"
msgstr "Annulla reblog"

#: src/components/status.jsx:1216
#: src/components/status.jsx:2715
msgid "Quote"
msgstr "Cita"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1228
#: src/components/status.jsx:1694
msgid "Unboosted @{0}'s post"
msgstr "Hai annullato il reblog del post di @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1229
#: src/components/status.jsx:1695
msgid "Boosted @{0}'s post"
msgstr "Hai rebloggato il post di @{0}"

#: src/components/status.jsx:1241
msgid "Boost…"
msgstr "Reblogga…"

#: src/components/status.jsx:1253
#: src/components/status.jsx:1988
#: src/components/status.jsx:2736
msgid "Unlike"
msgstr "Non mi piace più"

#: src/components/status.jsx:1254
#: src/components/status.jsx:1988
#: src/components/status.jsx:1989
#: src/components/status.jsx:2736
#: src/components/status.jsx:2737
msgid "Like"
msgstr "Mi piace"

#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
msgid "Unbookmark"
msgstr "Rimuovi dai segnalibri"

#: src/components/status.jsx:1346
msgid "Post text copied"
msgstr "Testo del post copiato"

#: src/components/status.jsx:1349
msgid "Unable to copy post text"
msgstr "Impossibile copiare il testo del post"

#: src/components/status.jsx:1355
msgid "Copy post text"
msgstr "Copia testo del post"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1373
msgid "View post by <0>@{0}</0>"
msgstr "Visualizza post di <0>@{0}</0>"

#: src/components/status.jsx:1394
msgid "Show Edit History"
msgstr "Mostra cronologia modifiche"

#: src/components/status.jsx:1397
msgid "Edited: {editedDateText}"
msgstr "Modificato: {editedDateText}"

#: src/components/status.jsx:1464
#: src/components/status.jsx:3516
msgid "Embed post"
msgstr "Incorpora post"

#: src/components/status.jsx:1478
msgid "Conversation unmuted"
msgstr "Conversazione riattivata"

#: src/components/status.jsx:1478
msgid "Conversation muted"
msgstr "Conversazione silenziata"

#: src/components/status.jsx:1484
msgid "Unable to unmute conversation"
msgstr "Impossibile riattivare conversazione"

#: src/components/status.jsx:1485
msgid "Unable to mute conversation"
msgstr "Impossibile silenziare conversazione"

#: src/components/status.jsx:1494
msgid "Unmute conversation"
msgstr "Riattiva conversazione"

#: src/components/status.jsx:1501
msgid "Mute conversation"
msgstr "Silenzia conversazione"

#: src/components/status.jsx:1517
msgid "Post unpinned from profile"
msgstr "Post tolto dai fissati del profilo"

#: src/components/status.jsx:1518
msgid "Post pinned to profile"
msgstr "Post fissato sul profilo"

#: src/components/status.jsx:1523
msgid "Unable to unpin post"
msgstr "Impossibile togliere post dai fissati"

#: src/components/status.jsx:1523
msgid "Unable to pin post"
msgstr "Impossibile fissare post"

#: src/components/status.jsx:1532
msgid "Unpin from profile"
msgstr "Togli dai fissati"

#: src/components/status.jsx:1539
msgid "Pin to profile"
msgstr "Fissa sul profilo"

#: src/components/status.jsx:1568
msgid "Delete this post?"
msgstr "Eliminare questo post?"

#: src/components/status.jsx:1584
msgid "Post deleted"
msgstr "Post eliminato"

#: src/components/status.jsx:1587
msgid "Unable to delete post"
msgstr "Impossibile eliminare post"

#: src/components/status.jsx:1615
msgid "Report post…"
msgstr "Segnala post…"

#: src/components/status.jsx:1989
#: src/components/status.jsx:2025
#: src/components/status.jsx:2737
msgid "Liked"
msgstr "Ti piace"

#: src/components/status.jsx:2022
#: src/components/status.jsx:2724
msgid "Boosted"
msgstr "Rebloggato"

#: src/components/status.jsx:2032
#: src/components/status.jsx:2749
msgid "Bookmarked"
msgstr "Aggiunto ai segnalibri"

#: src/components/status.jsx:2036
msgid "Pinned"
msgstr "Fissato"

#: src/components/status.jsx:2082
#: src/components/status.jsx:2561
msgid "Deleted"
msgstr "Eliminato"

#: src/components/status.jsx:2123
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {# risposta} other {# risposte}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2213
msgid "Thread{0}"
msgstr "Thread{0}"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
#: src/components/status.jsx:2457
msgid "Show less"
msgstr "Mostra meno"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
msgid "Show content"
msgstr "Mostra contenuto"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2453
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Filtrati: {0}"

#: src/components/status.jsx:2457
msgid "Show media"
msgstr "Mostra contenuti"

#: src/components/status.jsx:2597
msgid "Edited"
msgstr "Modificato"

#: src/components/status.jsx:2674
msgid "Comments"
msgstr "Commenti"

#. More from [Author]
#: src/components/status.jsx:2974
msgid "More from <0/>"
msgstr "Altro da <0/>"

#: src/components/status.jsx:3276
msgid "Edit History"
msgstr "Modifica cronologia"

#: src/components/status.jsx:3280
msgid "Failed to load history"
msgstr "Impossibile caricare cronologia"

#: src/components/status.jsx:3285
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "Carico…"

#: src/components/status.jsx:3521
msgid "HTML Code"
msgstr "Codice HTML"

#: src/components/status.jsx:3538
msgid "HTML code copied"
msgstr "Codice HTML copiato"

#: src/components/status.jsx:3541
msgid "Unable to copy HTML code"
msgstr "Impossibile copiare codice HTML"

#: src/components/status.jsx:3553
msgid "Media attachments:"
msgstr "Allegati multimediali:"

#: src/components/status.jsx:3575
msgid "Account Emojis:"
msgstr "Emoji account:"

#: src/components/status.jsx:3606
#: src/components/status.jsx:3651
msgid "static URL"
msgstr "URL statico"

#: src/components/status.jsx:3620
msgid "Emojis:"
msgstr "Emoji:"

#: src/components/status.jsx:3665
msgid "Notes:"
msgstr "Note:"

#: src/components/status.jsx:3669
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "È statico, senza stile e senza script. Potresti dover applicare i tuoi stili e modificare di conseguenza."

#: src/components/status.jsx:3675
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "I sondaggi non sono interattivi, diventano una lista con i conteggi dei voti."

#: src/components/status.jsx:3680
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "Gli allegati multimediali possono essere immagini, video, audio o qualsiasi tipo di file."

#: src/components/status.jsx:3686
msgid "Post could be edited or deleted later."
msgstr "Puoi modificare o eliminare il post in seguito."

#: src/components/status.jsx:3692
msgid "Preview"
msgstr "Anteprima"

#: src/components/status.jsx:3701
msgid "Note: This preview is lightly styled."
msgstr "Nota: a questa anteprima è stato applicato uno stile."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3954
msgid "<0/> <1/> boosted"
msgstr "<0/> <1/> ha rebloggato"

#: src/components/status.jsx:4056
msgid "Post hidden by your filters"
msgstr "Post nascosto dai tuoi filtri"

#: src/components/status.jsx:4057
msgid "Post pending"
msgstr ""

#: src/components/status.jsx:4058
#: src/components/status.jsx:4059
#: src/components/status.jsx:4060
#: src/components/status.jsx:4061
msgid "Post unavailable"
msgstr ""

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "Nuovi post"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1078
#: src/pages/status.jsx:1455
msgid "Try again"
msgstr "Riprova"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, one {# reblog} other {# reblog}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr "Post fissati"

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "Thread"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>Filtrati</0>: <1>{0}</1>"

#: src/components/translation-block.jsx:196
msgid "Auto-translated from {sourceLangText}"
msgstr "Traduzione automatica da {sourceLangText}"

#: src/components/translation-block.jsx:234
msgid "Translating…"
msgstr "Traduco…"

#: src/components/translation-block.jsx:237
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "Traduci da {sourceLangText} (rilevato automaticamente)"

#: src/components/translation-block.jsx:238
msgid "Translate from {sourceLangText}"
msgstr "Traduci da {sourceLangText}"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:266
msgid "Auto ({0})"
msgstr "Auto ({0})"

#: src/components/translation-block.jsx:279
msgid "Failed to translate"
msgstr "Traduzione fallita"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "Modificando post originale"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "In risposta a @{0}"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "Adesso puoi chiudere questa pagina."

#: src/compose.jsx:71
msgid "Close window"
msgstr "Chiudi finestra"

#: src/compose.jsx:87
msgid "Login required."
msgstr "Accesso richiesto."

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:280
msgid "Go home"
msgstr "Vai alla Home"

#: src/pages/account-statuses.jsx:241
msgid "Account posts"
msgstr "Post dell'account"

#: src/pages/account-statuses.jsx:248
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ Risposte)"

#: src/pages/account-statuses.jsx:250
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- Reblog)"

#: src/pages/account-statuses.jsx:252
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:254
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (contenuti)"

#: src/pages/account-statuses.jsx:260
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:331
msgid "Clear filters"
msgstr "Rimuovi filtri"

#: src/pages/account-statuses.jsx:348
msgid "Showing post with replies"
msgstr "Mostrando post con risposte"

#: src/pages/account-statuses.jsx:353
msgid "+ Replies"
msgstr "+ Risposte"

#: src/pages/account-statuses.jsx:359
msgid "Showing posts without boosts"
msgstr "Mostrando post senza reblog"

#: src/pages/account-statuses.jsx:364
msgid "- Boosts"
msgstr "- Reblog"

#: src/pages/account-statuses.jsx:370
msgid "Showing posts with media"
msgstr "Mostrando i post con contenuti"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:387
msgid "Showing posts tagged with #{0}"
msgstr "Mostrando post con #{0}"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:426
msgid "Showing posts in {0}"
msgstr "Mostrando post in {0}"

#: src/pages/account-statuses.jsx:516
msgid "Nothing to see here yet."
msgstr "Niente da vedere qui, per ora."

#: src/pages/account-statuses.jsx:517
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "Impossibile caricare i post"

#: src/pages/account-statuses.jsx:558
#: src/pages/account-statuses.jsx:588
msgid "Unable to fetch account info"
msgstr "Impossibile recuperare info account"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:565
msgid "Switch to account's instance {0}"
msgstr "Passa all'istanza dell'account {0}"

#: src/pages/account-statuses.jsx:595
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "Passa alla mia istanza (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:668
msgid "Month"
msgstr "Mese"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "Attuale"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "Default"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "Passa a questo account"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "Passa in nuova scheda/finestra"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "Visualizza profilo…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "Imposta come default"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr "Uscire da <0>@{0}</0>?"

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "Esci…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr "Connessione: {0} (<0/>)"

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "Aggiungi un account esistente"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "Nota: l'account <0>di default</0> verrà sempre usato per il primo caricamento. Gli account a cui passi rimarranno attivi durante la sessione."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "Nessun segnalibro. Vai e aggiungi qualcosa!"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "Impossibile caricare i segnalibri."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "ultima ora"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "ultime 2 ore"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "ultime 3 ore"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "ultime 4 ore"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "ultime 5 ore"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "ultime 6 ore"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "ultime 7 ore"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "ultime 8 ore"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "ultime 9 ore"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "ultime 10 ore"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "ultime 11 ore"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "ultime 12 ore"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "oltre 12 ore"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "Tag seguiti"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "Gruppi"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "Mostrando {selectedFilterCategory, select, all {tutti i post} original {post originali} replies {risposte} boosts {reblog} followedTags {tag seguiti} groups {gruppi} filtered {post filtrati}}{sortBy, select, createdAt {{sortOrder, select, asc {dal più vecchio} desc {al più recente}}} reblogsCount {{sortOrder, select, asc {meno reblog} desc {più reblog}}} favouritesCount {{sortOrder, select, asc {meno Mi piace} desc {più Mi piace}}} repliesCount {{sortOrder, select, asc {meno risposte} desc {più risposte}}} density {{sortOrder, select, asc {meno densi} desc {più densi}}}} prima{groupBy, select, account {, raggruppati per autori} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "Recupera <0>beta</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "Aiuto"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "Cos'è questo?"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "Recupera è una timeline separata per i tuoi seguiti, offre una visione panoramica con un'interfaccia semplice ispirata alle email per organizzare e filtrare i post con facilità."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Anteprima dell'interfaccia di Recupera"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "Recuperiamo"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "Recuperiamo i post dai tuoi seguiti."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "Mostrami tutti i post da…"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "fino al massimo"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "Recupera"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "Si sovrappone con il tuo ultimo recupero"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "Fino all'ultimo recupero ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "Nota: la tua istanza potrebbe mostrare solamente un massimo di 800 post nella Home a prescindere dall'intervallo di tempo, il valore massimo può variare."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "Precedentemente…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, one {# post} other {# post}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "Rimuovere questo recupero?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr "Rimuovo Recupera {0}"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr "Recupera {0} rimossa"

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "Nota: solo un massimo di 3 verrà conservato. Il resto verrà rimosso automaticamente."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "Recupero post…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "Potrebbe volerci un po'."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "Reimposta filtri"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "Link popolari"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "Condiviso da {0}"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:330
msgid "All"
msgstr "Tutto"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, one {# autore} other {# autori}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "Ordina"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Data"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Densità"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr "group.filter"

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "Autori"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "Nessuno"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "Mostra tutti gli autori"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "Non c'è bisogno di leggere tutto."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "È tutto."

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "Torna su"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "Link condivisi da account che segui, ordinati per numero di condivisioni, reblog e Mi piace."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "Ordina: Densità"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "I post sono ordinati in base alla loro densità o profondità informativa. I post brevi sono più \"leggeri\" mentre i post lunghi sono più \"pesanti\". I post con foto sono più \"pesanti\" dei post senza foto."

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "Gruppo: Autori"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "I post vengono raggruppati in base agli autori, ordinati per numero di post per autore."

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "Autore successivo"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "Autore precedente"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "Torna su"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "Nessun Mi piace. Metti Mi piace a qualcosa!"

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "Impossibile caricare i Mi piace."

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "Home e liste"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "Timeline pubbliche"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "Conversazioni"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "Profili"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "Mai"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "Nuovo filtro"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0, plural, one {# filtro} other {# filtri}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "Impossibile caricare filtri."

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "Nessun filtro."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "Aggiungi filtro"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "Modifica filtro"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "Impossibile modificare filtro"

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "Impossibile creare filtro"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "Titolo"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "Intera parola"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "Nessuna parola chiave. Aggiungine una."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "Aggiungi parola chiave"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, one {# parola chiave} other {# parole chiave}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "Filtra da…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* Non ancora implementato"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "Stato: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "Modifica scadenza"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "Scadenza"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "I post filtrati saranno…"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr "oscurati (solo i contenuti)"

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "contratti"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "nascosti"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "Eliminare questo filtro?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "Impossibile eliminare filtro."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "Scaduto"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "In scadenza <0/>"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "Non scade"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural, one {# hashtag} other {# hashtag}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "Impossibile caricare hashtag sguiti."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "Nessun hashtag seguito."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "Niente da vedere qui."

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "Impossibile caricare i post."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{hashtagTitle} (solo contenuti) su {instance}"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{hashtagTitle} su {instance}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (solo contenuti)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "Nessuno ha ancora pubblicato nulla con questo tag."

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "Impossibile caricare i post con questo tag"

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "Smettere di seguire #{hashtag}?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "Non segui più #{hashtag}"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "Ora segui #{hashtag}"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "Seguiti…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "Non più consigliato sul profilo"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "Impossibile rimuovere dai contenuti consigliati sul profilo"

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "Consigliato sul profilo"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr "{TOTAL_TAGS_LIMIT, plural, one {Max # tag} other {Max # tag}}"

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "Aggiungi hashtag"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "Rimuovi hashtag"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr "{SHORTCUTS_LIMIT, plural, one {Max # scorciatoia raggiunta. Impossibile aggiungere scorciatoia.} other {Max # scorciatoie raggiunte. Impossibile aggiungere scorciatoia.}}"

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "Questa scorciatoia esiste già"

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr "Scorciatoia hashtag aggiunta"

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "Aggiungi a Scorciatoie"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "Inserisci una nuova istanza, es. \"mastodon.social\""

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "Istanza non valida"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "Vai a un'altra istanza…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "Vai alla mia istanza (<0>{currentInstance}</0>)"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "Impossibile recuperare notifiche."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<0>Nuove</0> <1>richieste di seguire</1>"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "Risolvo…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "Impossibile risolvere URL"

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "Ancora niente."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "Gestisci membri"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "Rimuovere <0>@{0}</0> dalla lista?"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "Rimuovi…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, one {# lista} other {# liste}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "Nessuna lista."

#: src/pages/login.jsx:122
#: src/pages/login.jsx:132
msgid "Failed to register application"
msgstr "Registrazione applicazione fallita"

#: src/pages/login.jsx:218
msgid "instance domain"
msgstr "dominio istanza"

#: src/pages/login.jsx:242
msgid "e.g. “mastodon.social”"
msgstr "es. \"mastodon.social\""

#: src/pages/login.jsx:253
msgid "Failed to log in. Please try again or try another instance."
msgstr "Accesso fallito. Riprova o prova con un'altra istanza."

#: src/pages/login.jsx:265
msgid "Continue with {selectedInstanceText}"
msgstr "Continua con {selectedInstanceText}"

#: src/pages/login.jsx:266
msgid "Continue"
msgstr "Continua"

#: src/pages/login.jsx:274
msgid "Don't have an account? Create one!"
msgstr "Non hai un account? Creane uno!"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "Menzioni private"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "Private"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "Nessuno ti ha menzionato :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "Impossibile caricare menzioni."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "Non segui"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "Non ti segue"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "Ha un nuovo account"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr "Ti menziona in privato dal nulla"

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr "Ha un account limitato dai moderatori"

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "Impostazioni notifiche"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "Nuove notifiche"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, one {Comunicazione} other {Comunicazioni}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "Richieste di seguirti"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, one {# richiesta di seguire} other {# richieste di seguire}}"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr "{0, plural, one {Notifiche filtrate da # persona} other {Notifiche filtrate da # persone}}"

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "Solo menzioni"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "Oggi"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "Sai già tutto."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "Ieri"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "Impossibile caricare notifiche"

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "Impostazioni notifiche aggiornate"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "Filtra le notifiche di chi:"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "Filtra"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "Ignora"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "Aggiornato <0>{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr "Visualizza notifiche da <0>@{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr "Notifiche da <0>@{0}</0>"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr "Da ora in poi le notifiche da @{0} non verranno filtrate."

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr "Impossibile accettare richiesta di notifica"

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "Consenti"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr "Da ora in poi le notifiche da @{0} non appariranno in Notifiche filtrate."

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr "Impossibile ignorare richiesta di notifica"

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "Ignora"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "Ignorata"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "Timeline locale ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "Timeline federata ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "Timeline locale"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "Timeline federata"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "Nessuno ha ancora pubblicato nulla."

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "Passa a Federata"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "Passa a Locale"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr "Nessun post programmato."

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr "Programmato <0><1/></0> <2>({0})</2>"

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr "Programmato <0><1/></0>"

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr "Post programmato aggiornato"

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr "Aggiornamento post programmato fallito"

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr "Riprogramma"

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr "Eliminare il post programmato?"

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr "Post programmato eliminato"

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr "Eliminazione post programmato fallita"

#: src/pages/search.jsx:51
msgid "Search: {q} (Posts)"
msgstr "Cerca: {q} (post)"

#: src/pages/search.jsx:54
msgid "Search: {q} (Accounts)"
msgstr "Cerca: {q} (account)"

#: src/pages/search.jsx:57
msgid "Search: {q} (Hashtags)"
msgstr "Cerca: {q} (hashtag)"

#: src/pages/search.jsx:60
msgid "Search: {q}"
msgstr "Cerca: {q}"

#: src/pages/search.jsx:340
#: src/pages/search.jsx:422
msgid "Hashtags"
msgstr "Hashtag"

#: src/pages/search.jsx:372
#: src/pages/search.jsx:426
#: src/pages/search.jsx:496
msgid "See more"
msgstr "Mostra altro"

#: src/pages/search.jsx:398
msgid "See more accounts"
msgstr "Mostra altri account"

#: src/pages/search.jsx:412
msgid "No accounts found."
msgstr "Nessun account trovato."

#: src/pages/search.jsx:468
msgid "See more hashtags"
msgstr "Mostra altri hashtag"

#: src/pages/search.jsx:482
msgid "No hashtags found."
msgstr "Nessun hashtag trovato."

#: src/pages/search.jsx:526
msgid "See more posts"
msgstr "Mostra altri post"

#: src/pages/search.jsx:540
msgid "No posts found."
msgstr "Nessun post trovato."

#: src/pages/search.jsx:585
msgid "Enter your search term or paste a URL above to get started."
msgstr "Inserisci il termine di ricerca o incolla un URL sopra per iniziare."

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "Impostazioni"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "Aspetto"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "Chiaro"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "Scuro"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "Auto"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "Dimensione testo"

#. Preview of one character, in largest size
#. Preview of one character, in smallest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "A"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "Lingua interfaccia"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "Traduzioni volontarie"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "Pubblicazione"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "Visibilità di default"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "Sincronizzato"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "Impossibile aggiornare visibilità pubblicazione"

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "Sincronizzato con le impostazioni del server della tua istanza. <0>Visita la tua istanza ({instance}) per altre impostazioni.</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "Esperimenti"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "Aggiorna automaticamente i post della timeline"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "Carosello reblog"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "Traduzione post"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr "Traduci in "

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "Lingua di sistema ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr "{0, plural, =0 {Nascondi tasto \"Traduci\" per:} other {Nascondi tasto \"Traduci\" per (#):}}"

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr "Nota: questa funzionalità utilizza servizi di traduzione esterni, forniti da <0>{TRANSLATION_API_NAME}</0>."

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "Traduzione automatica in linea"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr "Mostra automaticamente la traduzione per i post nella timeline. Funziona solo per i post <0>corti</0> senza avvisi, contenuti o sondaggi."

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "Selettore GIF"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "Nota: Questa funzione utilizza un servizio di ricerca GIF esterno fornito da <0>GIPHY</0>. Classificazione T (adatto a tutte le età), i parametri traccianti sono eliminati e le informazioni di referer sono omesse dalle richieste, ma i termini di ricerca e le informazioni sull'indirizzo IP raggiungeranno comunque i loro server."

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "Generatore descrizioni immagini"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr "Solo per le nuove immagini mentre si compone un nuovo post."

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "Nota: questa funzionalità usa un servizio IA esterno, fornito da <0>img-alt-api</0>. Potrebbe non funzionare correttamente. Solo per le immagini e in inglese."

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr "Notifiche raggruppate dal server"

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr "Funzionalità in stato alfa. Potenziale miglioramento della finestra di raggruppamento ma con una logica di raggruppamento basica."

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "Importazione/esportazione \"cloud\" per le impostazioni delle scorciatoie."

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr "⚠️⚠️⚠️ Molto sperimentale.<0/>Conservato nelle tue note del profilo. Le note (private) del profilo sono usate principalmente per gli altri profili e sono nascoste per il tuo profilo."

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "Nota: questa funzionalità usa l'API del server dell'istanza a cui si è collegati."

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr "Modalità mantello <0>(<1>Testo</1> → <2>████</2>)</0>"

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr "Oscura il testo con dei blocchi, utile per gli screenshot e per proteggere la privacy."

#: src/pages/settings.jsx:710
msgid "About"
msgstr "Informazioni"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr "<0>Sviluppato</0> da <1>@cheeaun</1>"

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "Sponsor"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Dona"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr "Novità"

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "Politica sulla privacy"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>Sito:</0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>Versione:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "Stringa versione copiata"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr "Impossibile copiare stringa versione"

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr "Impossibile aggiornare abbonamento. Riprova."

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr "Impossibile rimuovere abbonamento. Riprova."

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "Notifiche push (beta)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr "Le notifiche push sono bloccate. Abilitale nelle impostazioni del browser."

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "Consenti da <0>{0}</0>"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "chiunque"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "chi seguo"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "chi mi segue"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "Nuovi seguaci"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "Sondaggi"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "Modifiche post"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr "L'autorizzazione push non è stata concessa dall'ultimo accesso. <0><1>Accedi</1> di nuovo per concedere l'autorizzazione push</0>."

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "NOTA: Le notifiche push funzionano solo per <0>un account</0>."

#. js-lingui-explicit-id
#: src/pages/status.jsx:658
#: src/pages/status.jsx:1221
msgid "post.title"
msgstr "Post"

#: src/pages/status.jsx:912
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "Non hai effettuato l'accesso. Le interazioni (risposte, reblog, ecc.) sono disabilitate."

#: src/pages/status.jsx:932
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "Questo post è di un'altra istanza (<0>{instance}</0>). Le interazioni (risposte, reblog, ecc.) sono disabilitate."

#: src/pages/status.jsx:960
msgid "Error: {e}"
msgstr "Errore: {e}"

#: src/pages/status.jsx:967
msgid "Switch to my instance to enable interactions"
msgstr "Passa alla mia istanza per abilitare le interazioni"

#: src/pages/status.jsx:1069
msgid "Unable to load replies."
msgstr "Impossibile caricare risposte."

#: src/pages/status.jsx:1181
msgid "Back"
msgstr "Indietro"

#: src/pages/status.jsx:1212
msgid "Go to main post"
msgstr "Vai al post principale"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1235
msgid "{0} posts above ‒ Go to top"
msgstr "{0} post sopra - Torna in cima"

#: src/pages/status.jsx:1282
#: src/pages/status.jsx:1345
msgid "Switch to Side Peek view"
msgstr "Passa a vista laterale"

#: src/pages/status.jsx:1346
msgid "Switch to Full view"
msgstr "Passa a vista completa"

#: src/pages/status.jsx:1364
msgid "Show all sensitive content"
msgstr "Mostra tutti i contenuti sensibili"

#: src/pages/status.jsx:1369
msgid "Experimental"
msgstr "Sperimentale"

#: src/pages/status.jsx:1378
msgid "Unable to switch"
msgstr "Impossibile cambiare"

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1385
msgid "Switch to post's instance ({0})"
msgstr "Passa all'istanza del post ({0})"

#: src/pages/status.jsx:1388
msgid "Switch to post's instance"
msgstr "Passa all'istanza del post"

#: src/pages/status.jsx:1446
msgid "Unable to load post"
msgstr "Impossibile caricare post"

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1582
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, one {# risposta} other {<0>{1}</0> risposte}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1600
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, one {# commento} other {<0>{0}</0> commenti}}"

#: src/pages/status.jsx:1622
msgid "View post with its replies"
msgstr "Visualizza post con le risposte"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "In tendenza ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "Notizie in tendenza"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr "Di {0}"

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "Ritorna a visualizzare i post di tendenza"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "Mostra i post che citano <0>{0}</0>"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "Post in tendenza"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "Nessun post in tendenza."

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "Un client web minimalista per Mastodon."

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Accedi con Mastodon"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "Registrati"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "Collega il tuo account Mastodon o del Fediverso.<0/>Le tue credenziali non vengono salvate su questo server."

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr "<0>Sviluppato</0> da <1>@cheeaun</1>. <2>Politica sulla privacy</2>."

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "Screenshot del carosello dei reblog"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "Carosello dei reblog"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "Separa visivamente i post originali e i post ricondivisi (post rebloggati)."

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr "Screenshot del thread con commenti annidati"

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr "Thread con commenti annidati"

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "Segui le conversazioni senza fatica. Risposte semi-comprimibili."

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr "Screenshot delle notifiche raggruppate"

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "Notifiche raggruppate"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr "Le notifiche simili sono raggruppate e compresse per ridurre la confusione."

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "Screenshot dell'interfaccia multi-colonna"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "Una o più colonne"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "Di default, una sola colonna per gli utenti zen. Modalità multi-colonna per gli utenti avanzati."

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "Screenshot della timeline multi-hashtag con un modulo per aggiungere altri hashtag"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "Timeline multi-hashtag"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "Fino a 5 hastag combinati in una sola timeline."

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "Sembra che il tuo browser stia bloccando i popup."

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr "Un post bozza è attualmente contratto. Pubblicato o scartalo prima di crearne un altro."

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr "Un post è attualmente aperto. Pubblicato o scartalo prima di crearne un altro."

