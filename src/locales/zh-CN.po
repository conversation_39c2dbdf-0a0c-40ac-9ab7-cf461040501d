msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: zh\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-08-05 04:12\n"
"Last-Translator: \n"
"Language-Team: Chinese Simplified\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: zh-CN\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:141
msgid "Locked"
msgstr "已锁嘟"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:163
msgid "Posts: {0}"
msgstr "嘟文: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:168
msgid "Last posted: {0}"
msgstr "上次发嘟: {0}"

#: src/components/account-block.jsx:183
#: src/components/account-info.jsx:705
msgid "Automated"
msgstr "机器人"

#: src/components/account-block.jsx:190
#: src/components/account-info.jsx:710
#: src/components/status.jsx:748
msgid "Group"
msgstr "群组"

#: src/components/account-block.jsx:200
msgid "Mutual"
msgstr "互相关注"

#: src/components/account-block.jsx:204
#: src/components/account-info.jsx:1907
msgid "Requested"
msgstr "已请求"

#: src/components/account-block.jsx:208
#: src/components/account-info.jsx:1898
msgid "Following"
msgstr "正在关注"

#: src/components/account-block.jsx:212
#: src/components/account-info.jsx:1212
msgid "Follows you"
msgstr "关注了你"

#: src/components/account-block.jsx:220
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, other {# 粉丝}}"

#: src/components/account-block.jsx:229
#: src/components/account-info.jsx:753
msgid "Verified"
msgstr "已认证"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:244
#: src/components/account-info.jsx:892
msgid "Joined <0>{0}</0>"
msgstr "加入于 <0>{0}</0>"

#: src/components/account-info.jsx:64
msgid "Forever"
msgstr "永久"

#: src/components/account-info.jsx:398
msgid "Unable to load account."
msgstr "无法加载账户。"

#: src/components/account-info.jsx:413
msgid "Go to account page"
msgstr "转到账户页"

#: src/components/account-info.jsx:442
#: src/components/account-info.jsx:775
msgid "Followers"
msgstr "粉丝"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:446
#: src/components/account-info.jsx:830
msgid "following.stats"
msgstr "关注"

#: src/components/account-info.jsx:449
#: src/pages/account-statuses.jsx:495
#: src/pages/search.jsx:345
#: src/pages/search.jsx:492
msgid "Posts"
msgstr "嘟文"

#: src/components/account-info.jsx:457
#: src/components/account-info.jsx:1268
#: src/components/compose.jsx:2819
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1998
#: src/components/status.jsx:2015
#: src/components/status.jsx:2140
#: src/components/status.jsx:2769
#: src/components/status.jsx:2772
#: src/pages/account-statuses.jsx:539
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1306
#: src/pages/trending.jsx:474
msgid "More"
msgstr "更多"

#: src/components/account-info.jsx:469
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> 已声明 Ta 现在的新账户是："

#: src/components/account-info.jsx:614
#: src/components/account-info.jsx:1496
msgid "Handle copied"
msgstr "已复制用户名"

#: src/components/account-info.jsx:617
#: src/components/account-info.jsx:1499
msgid "Unable to copy handle"
msgstr "无法复制用户名"

#: src/components/account-info.jsx:623
#: src/components/account-info.jsx:1505
msgid "Copy handle"
msgstr "复制用户名"

#: src/components/account-info.jsx:629
msgid "Go to original profile page"
msgstr "转到原始账户页"

#: src/components/account-info.jsx:647
msgid "View profile image"
msgstr "查看头像"

#: src/components/account-info.jsx:665
msgid "View profile header"
msgstr "查看横幅图片"

#: src/components/account-info.jsx:681
#: src/components/account-info.jsx:1806
#: src/components/account-info.jsx:2332
msgid "Edit profile"
msgstr "编辑个人资料"

#: src/components/account-info.jsx:700
msgid "In Memoriam"
msgstr "悼念账户"

#: src/components/account-info.jsx:782
#: src/components/account-info.jsx:840
msgid "This user has chosen to not make this information available."
msgstr "该用户选择不提供此信息。"

#. placeholder {0}: shortenNumber(followersCount)
#. placeholder {1}: shortenNumber(followersCount)
#: src/components/account-info.jsx:802
msgid "{followersCount, plural, one {<0>{0}</0> Follower} other {<1>{1}</1> Followers}}"
msgstr ""

#. placeholder {0}: shortenNumber(followingCount)
#: src/components/account-info.jsx:846
msgid "{followingCount, plural, other {<0>{0}</0> Following}}"
msgstr ""

#. placeholder {0}: shortenNumber(statusesCount)
#. placeholder {1}: shortenNumber(statusesCount)
#: src/components/account-info.jsx:870
msgid "{statusesCount, plural, one {<0>{0}</0> Post} other {<1>{1}</1> Posts}}"
msgstr ""

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:917
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0} 为原创嘟文，{1} 为回复，{2} 为转嘟"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:933
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural, other {{3, plural, other {最近 {6} 天发了 {5} 条嘟文}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:949
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, other {近几年的 {1} 条嘟文}}"

#: src/components/account-info.jsx:974
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "原创"

#: src/components/account-info.jsx:978
#: src/components/status.jsx:2553
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1025
#: src/pages/status.jsx:1652
msgid "Replies"
msgstr "回复"

#: src/components/account-info.jsx:982
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "转嘟"

#: src/components/account-info.jsx:988
msgid "Post stats unavailable."
msgstr "嘟文统计不可用"

#: src/components/account-info.jsx:1019
msgid "View post stats"
msgstr "查看嘟文统计"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1216
msgid "Last post: <0>{0}</0>"
msgstr "上次发嘟: <0>{0}</0>"

#: src/components/account-info.jsx:1230
msgid "Muted"
msgstr "已静音"

#: src/components/account-info.jsx:1235
msgid "Blocked"
msgstr "已屏蔽"

#: src/components/account-info.jsx:1244
msgid "Private note"
msgstr "私人备注"

#: src/components/account-info.jsx:1301
msgid "Mention <0>@{username}</0>"
msgstr "提及 <0>@{username}</0>"

#: src/components/account-info.jsx:1313
msgid "Translate bio"
msgstr "翻译简介"

#: src/components/account-info.jsx:1324
msgid "Edit private note"
msgstr "编辑私人备注"

#: src/components/account-info.jsx:1324
msgid "Add private note"
msgstr "添加私人备注"

#: src/components/account-info.jsx:1344
msgid "Notifications enabled for @{username}'s posts."
msgstr "已启用 @{username} 的嘟文通知。"

#: src/components/account-info.jsx:1345
msgid " Notifications disabled for @{username}'s posts."
msgstr " 已停用 @{username} 的发嘟通知。"

#: src/components/account-info.jsx:1357
msgid "Disable notifications"
msgstr "停用通知"

#: src/components/account-info.jsx:1358
msgid "Enable notifications"
msgstr "启用通知"

#: src/components/account-info.jsx:1375
msgid "Boosts from @{username} enabled."
msgstr "将显示 @{username} 的转嘟。"

#: src/components/account-info.jsx:1376
msgid "Boosts from @{username} disabled."
msgstr "将不再显示 @{username} 的转嘟。"

#: src/components/account-info.jsx:1387
msgid "Disable boosts"
msgstr "不显示转嘟"

#: src/components/account-info.jsx:1387
msgid "Enable boosts"
msgstr "显示转嘟"

#: src/components/account-info.jsx:1406
msgid "@{username} is no longer featured on your profile."
msgstr "@{username} 将不再出现在你的账户页的精选账户列表中。"

#: src/components/account-info.jsx:1416
msgid "@{username} is now featured on your profile."
msgstr "@{username} 已被加入你的账户页中的精选账户列表。"

#: src/components/account-info.jsx:1424
msgid "Unable to unfeature @{username} on your profile."
msgstr "无法将 @{username} 从账户页的精选账户列表移出。"

#: src/components/account-info.jsx:1428
msgid "Unable to feature @{username} on your profile."
msgstr "无法将 @{username} 加入账户页的精选账户列表。"

#: src/components/account-info.jsx:1437
msgid "Don't feature on profile"
msgstr "从个人页精选中移除"

#: src/components/account-info.jsx:1438
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "加入个人页精选"

#: src/components/account-info.jsx:1447
msgid "Show featured profiles"
msgstr "展示精选账户列表"

#: src/components/account-info.jsx:1462
#: src/components/account-info.jsx:1472
#: src/components/account-info.jsx:2078
msgid "Add/Remove from Lists"
msgstr "加入/移出列表"

#: src/components/account-info.jsx:1522
#: src/components/status.jsx:1420
msgid "Link copied"
msgstr "已复制链接"

#: src/components/account-info.jsx:1525
#: src/components/status.jsx:1423
msgid "Unable to copy link"
msgstr "无法复制链接"

#: src/components/account-info.jsx:1531
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1429
#: src/components/status.jsx:3547
msgid "Copy"
msgstr "复制"

#: src/components/account-info.jsx:1546
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1445
msgid "Sharing doesn't seem to work."
msgstr "分享似乎无法正常工作。"

#: src/components/account-info.jsx:1552
#: src/components/status.jsx:1451
msgid "Share…"
msgstr "分享…"

#: src/components/account-info.jsx:1572
msgid "Unmuted @{username}"
msgstr "已取消静音 @{username}"

#: src/components/account-info.jsx:1584
msgid "Unmute <0>@{username}</0>"
msgstr "取消静音 <0>@{username}</0>"

#: src/components/account-info.jsx:1600
msgid "Mute <0>@{username}</0>…"
msgstr "静音 <0>@{username}</0>…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1632
msgid "Muted @{username} for {0}"
msgstr "已静音 @{username} {0}"

#: src/components/account-info.jsx:1644
msgid "Unable to mute @{username}"
msgstr "无法静音 @{username}"

#: src/components/account-info.jsx:1665
msgid "Remove <0>@{username}</0> from followers?"
msgstr "是否将 <0>@{username}</0> 从粉丝中移除？"

#: src/components/account-info.jsx:1685
msgid "@{username} removed from followers"
msgstr "@{username} 已被从粉丝中移除"

#: src/components/account-info.jsx:1697
msgid "Remove follower…"
msgstr "移除粉丝…"

#: src/components/account-info.jsx:1708
msgid "Block <0>@{username}</0>?"
msgstr "是否屏蔽 <0>@{username}</0>？"

#: src/components/account-info.jsx:1732
msgid "Unblocked @{username}"
msgstr "已解除屏蔽 @{username}"

#: src/components/account-info.jsx:1740
msgid "Blocked @{username}"
msgstr "已屏蔽 @{username}"

#: src/components/account-info.jsx:1748
msgid "Unable to unblock @{username}"
msgstr "无法解除屏蔽 @{username}"

#: src/components/account-info.jsx:1750
msgid "Unable to block @{username}"
msgstr "无法屏蔽 @{username}"

#: src/components/account-info.jsx:1760
msgid "Unblock <0>@{username}</0>"
msgstr "取消屏蔽 <0>@{username}</0>"

#: src/components/account-info.jsx:1769
msgid "Block <0>@{username}</0>…"
msgstr "屏蔽 <0>@{username}</0>…"

#: src/components/account-info.jsx:1786
msgid "Report <0>@{username}</0>…"
msgstr "举报 <0>@{username}</0>…"

#: src/components/account-info.jsx:1842
msgid "Withdraw follow request?"
msgstr "是否撤回关注请求？"

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1843
msgid "Unfollow @{0}?"
msgstr "是否取关 @{0} ？"

#: src/components/account-info.jsx:1901
msgid "Unfollow…"
msgstr "取关…"

#: src/components/account-info.jsx:1910
msgid "Withdraw…"
msgstr "撤回…"

#: src/components/account-info.jsx:1917
#: src/components/account-info.jsx:1921
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "关注"

#: src/components/account-info.jsx:2018
#: src/components/account-info.jsx:2073
#: src/components/account-info.jsx:2207
#: src/components/account-info.jsx:2327
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:892
#: src/components/compose.jsx:2775
#: src/components/compose.jsx:3255
#: src/components/compose.jsx:3464
#: src/components/compose.jsx:3694
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:118
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3271
#: src/components/status.jsx:3511
#: src/components/status.jsx:4020
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1393
msgid "Close"
msgstr "关闭"

#: src/components/account-info.jsx:2023
msgid "Translated Bio"
msgstr "简介(已翻译)"

#: src/components/account-info.jsx:2118
msgid "Unable to remove from list."
msgstr "无法从列表中移除。"

#: src/components/account-info.jsx:2119
msgid "Unable to add to list."
msgstr "无法添加到列表。"

#: src/components/account-info.jsx:2138
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "无法加载列表。"

#: src/components/account-info.jsx:2142
msgid "No lists."
msgstr "暂无列表。"

#: src/components/account-info.jsx:2153
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "新建列表"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2212
msgid "Private note about <0>@{0}</0>"
msgstr "对 <0>@{0}</0> 的私人备注"

#: src/components/account-info.jsx:2242
msgid "Unable to update private note."
msgstr "无法更新私人备注。"

#: src/components/account-info.jsx:2265
#: src/components/account-info.jsx:2563
msgid "Cancel"
msgstr "取消"

#: src/components/account-info.jsx:2270
msgid "Save & close"
msgstr "保存并关闭"

#: src/components/account-info.jsx:2387
msgid "Unable to update profile."
msgstr "无法更新个人资料。"

#: src/components/account-info.jsx:2394
msgid "Header picture"
msgstr "横幅图片"

#: src/components/account-info.jsx:2446
msgid "Profile picture"
msgstr "头像"

#: src/components/account-info.jsx:2498
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "名称"

#: src/components/account-info.jsx:2511
msgid "Bio"
msgstr "简介"

#: src/components/account-info.jsx:2524
msgid "Extra fields"
msgstr "附加信息"

#: src/components/account-info.jsx:2530
msgid "Label"
msgstr "名称"

#: src/components/account-info.jsx:2533
msgid "Content"
msgstr "内容"

#: src/components/account-info.jsx:2566
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "保存"

#: src/components/account-info.jsx:2620
msgid "username"
msgstr "用户名"

#: src/components/account-info.jsx:2624
msgid "server domain name"
msgstr "实例域名"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2690
msgid "Profiles featured by @{0}"
msgstr "@{0} 的精选账户"

#: src/components/account-info.jsx:2716
msgid "No featured profiles."
msgstr "暂无精选账户。"

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "已停用文字打码模式"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "已启用文字打码模式"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "主页"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "发嘟"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr "定时嘟文"

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr "添加到嘟文串"

#: src/components/compose.jsx:212
msgid "Take photo or video"
msgstr "拍摄"

#: src/components/compose.jsx:213
msgid "Add media"
msgstr "添加媒体"

#: src/components/compose.jsx:214
msgid "Add custom emoji"
msgstr "添加自定义表情"

#: src/components/compose.jsx:215
msgid "Add GIF"
msgstr "添加GIF"

#: src/components/compose.jsx:216
msgid "Add poll"
msgstr "添加投票"

#: src/components/compose.jsx:217
msgid "Schedule post"
msgstr "设置定时发送"

#: src/components/compose.jsx:417
msgid "You have unsaved changes. Discard this post?"
msgstr "你有尚未保存的更改。是否丢弃这条嘟文？"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:655
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr "{0, plural, other {文件 {2} 的类型不受支持。}}"

#: src/components/compose.jsx:665
#: src/components/compose.jsx:683
#: src/components/compose.jsx:1795
#: src/components/compose.jsx:1930
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, other {你最多可以添加 # 个附件。}}"

#: src/components/compose.jsx:873
msgid "Pop out"
msgstr "弹出"

#: src/components/compose.jsx:880
msgid "Minimize"
msgstr "最小化"

#: src/components/compose.jsx:916
msgid "Looks like you closed the parent window."
msgstr "你似乎关闭了上级窗口。"

#: src/components/compose.jsx:923
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "你似乎已在上级窗口打开了一个撰写框并正在发布内容。请等待其完成后再试。"

#: src/components/compose.jsx:928
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "你似乎已在上级窗口中打开了一个撰写框。如果在此窗口中新建撰写框，你在上级窗口中所做的更改将丢失。是否继续？"

#: src/components/compose.jsx:971
msgid "Pop in"
msgstr "弹回"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:981
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "正在回复 @{0} 的嘟文 (<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:991
msgid "Replying to @{0}’s post"
msgstr "正在回复 @{0} 的嘟文"

#: src/components/compose.jsx:1004
msgid "Editing source post"
msgstr "编辑原嘟"

#: src/components/compose.jsx:1057
msgid "Poll must have at least 2 options"
msgstr "投票必须至少包含 2 个选项"

#: src/components/compose.jsx:1061
msgid "Some poll choices are empty"
msgstr "某些投票选项为空"

#: src/components/compose.jsx:1074
msgid "Some media have no descriptions. Continue?"
msgstr "某些媒体附件没有描述文本。是否继续？"

#: src/components/compose.jsx:1126
msgid "Attachment #{i} failed"
msgstr "附加附件 #{i} 失败"

#: src/components/compose.jsx:1222
#: src/components/status.jsx:2328
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "内容警告"

#: src/components/compose.jsx:1238
msgid "Content warning or sensitive media"
msgstr "内容警告或敏感媒体"

#: src/components/compose.jsx:1274
#: src/components/status.jsx:99
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "公开"

#: src/components/compose.jsx:1279
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:100
msgid "Local"
msgstr "本站"

#: src/components/compose.jsx:1283
#: src/components/status.jsx:101
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "不列出"

#: src/components/compose.jsx:1286
#: src/components/status.jsx:102
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "仅粉丝"

#: src/components/compose.jsx:1289
#: src/components/status.jsx:103
#: src/components/status.jsx:2204
msgid "Private mention"
msgstr "私信"

#: src/components/compose.jsx:1298
msgid "Post your reply"
msgstr "发送回复"

#: src/components/compose.jsx:1300
msgid "Edit your post"
msgstr "编辑嘟文"

#: src/components/compose.jsx:1301
msgid "What are you doing?"
msgstr "在做什么呢？"

#: src/components/compose.jsx:1380
msgid "Mark media as sensitive"
msgstr "将媒体标记为敏感"

#: src/components/compose.jsx:1417
msgid "Posting on <0/>"
msgstr "发布于 <0/>"

#: src/components/compose.jsx:1448
#: src/components/compose.jsx:3313
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "添加"

#: src/components/compose.jsx:1676
msgid "Schedule"
msgstr "定时发嘟"

#: src/components/compose.jsx:1678
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1192
#: src/components/status.jsx:1978
#: src/components/status.jsx:1979
#: src/components/status.jsx:2673
msgid "Reply"
msgstr "回复"

#: src/components/compose.jsx:1680
msgid "Update"
msgstr "更新"

#: src/components/compose.jsx:1681
msgctxt "Submit button in composer"
msgid "Post"
msgstr "发布"

#: src/components/compose.jsx:1807
msgid "Downloading GIF…"
msgstr "正在下载 GIF…"

#: src/components/compose.jsx:1835
msgid "Failed to download GIF"
msgstr "GIF 下载失败"

#: src/components/compose.jsx:2060
#: src/components/compose.jsx:2153
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "更多…"

#: src/components/compose.jsx:2589
msgid "Uploaded"
msgstr "已上传"

#: src/components/compose.jsx:2602
msgid "Image description"
msgstr "图片描述"

#: src/components/compose.jsx:2603
msgid "Video description"
msgstr "视频描述"

#: src/components/compose.jsx:2604
msgid "Audio description"
msgstr "音频描述"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2639
#: src/components/compose.jsx:2659
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "文件大小过大。上传将有可能出现问题。可尝试将文件大小从 {0} 压缩至 {1} 或更小。"

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2651
#: src/components/compose.jsx:2671
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr "文件尺寸过大。上传将有可能出现问题。可尝试将尺寸从 {0}×{1}px 裁剪至 {2}×{3}px。"

#: src/components/compose.jsx:2679
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "帧率过高。上传将有可能出现问题。"

#: src/components/compose.jsx:2739
#: src/components/compose.jsx:2989
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "移除"

#: src/components/compose.jsx:2756
#: src/compose.jsx:84
msgid "Error"
msgstr "错误"

#: src/components/compose.jsx:2781
msgid "Edit image description"
msgstr "编辑图片描述"

#: src/components/compose.jsx:2782
msgid "Edit video description"
msgstr "编辑视频描述"

#: src/components/compose.jsx:2783
msgid "Edit audio description"
msgstr "编辑音频描述"

#: src/components/compose.jsx:2828
#: src/components/compose.jsx:2877
msgid "Generating description. Please wait…"
msgstr "正在生成描述。请稍候…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2848
msgid "Failed to generate description: {0}"
msgstr "描述生成失败: {0}"

#: src/components/compose.jsx:2849
msgid "Failed to generate description"
msgstr "描述生成失败"

#: src/components/compose.jsx:2861
#: src/components/compose.jsx:2867
#: src/components/compose.jsx:2913
msgid "Generate description…"
msgstr "生成描述…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2900
msgid "Failed to generate description{0}"
msgstr "描述生成失败{0}"

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2915
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>— 实验性功能</0>"

#: src/components/compose.jsx:2934
msgid "Done"
msgstr "已完成"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2970
msgid "Choice {0}"
msgstr "选项 {0}"

#: src/components/compose.jsx:3017
msgid "Multiple choices"
msgstr "多选"

#: src/components/compose.jsx:3020
msgid "Duration"
msgstr "时长"

#: src/components/compose.jsx:3051
msgid "Remove poll"
msgstr "删除投票"

#: src/components/compose.jsx:3272
msgid "Search accounts"
msgstr "搜索账户"

#: src/components/compose.jsx:3326
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "加载账户时出错"

#: src/components/compose.jsx:3470
msgid "Custom emojis"
msgstr "自定义表情"

#: src/components/compose.jsx:3490
msgid "Search emoji"
msgstr "搜索表情"

#: src/components/compose.jsx:3521
msgid "Error loading custom emojis"
msgstr "加载自定义表情时出错"

#: src/components/compose.jsx:3532
msgid "Recently used"
msgstr "最近使用"

#: src/components/compose.jsx:3533
msgid "Others"
msgstr "其它"

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3571
msgid "{0} more…"
msgstr "更多 {0} 个…"

#: src/components/compose.jsx:3709
msgid "Search GIFs"
msgstr "搜索动图"

#: src/components/compose.jsx:3724
msgid "Powered by GIPHY"
msgstr "由 GIPHY 驱动"

#: src/components/compose.jsx:3732
msgid "Type to search GIFs"
msgstr "输入以搜索 GIF"

#: src/components/compose.jsx:3830
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "上一页"

#: src/components/compose.jsx:3848
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "下一页"

#: src/components/compose.jsx:3865
msgid "Error loading GIFs"
msgstr "加载 GIF 时出错"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "未发送的草稿"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "你似乎有未发送的草稿。让我们从你上次离开的地方继续。"

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "是否删除该草稿？"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "删除草稿时出错！请重试。"

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1595
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "删除…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "获取回复的上级嘟文时出错！"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "是否删除全部草稿？"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "删除草稿时出错！请重试。"

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "删除全部…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "暂无草稿。"

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "投票"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:375
msgid "Media"
msgstr "媒体"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "在新窗口打开"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "接受"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "拒绝"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "已接受"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "已拒绝"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:335
#: src/pages/search.jsx:368
msgid "Accounts"
msgstr "账号"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:562
#: src/pages/status.jsx:1426
msgid "Show more…"
msgstr "显示更多…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:567
msgid "The end."
msgstr "到底了。"

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "暂无内容"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "快捷键"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "快捷键帮助"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "下一条嘟文"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "上一条嘟文"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "跳转到下一条嘟文"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Shift</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "跳转到上一条嘟文"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Shift</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "加载新嘟文"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "打开嘟文详情页"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Enter</0> 或 <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "展开内容警告或<0/>展开/折叠嘟文串"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "关闭嘟文或对话"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc</0> 或 <1>Backspace</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "在多栏模式中选中对应栏"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> 到 <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr "在多栏模式中选中下一栏"

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr "在多栏模式中选中上一栏"

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "撰写新嘟文"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "撰写新嘟文(新窗口)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Shift</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "发送嘟文"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Ctrl</0> + <1>Enter</1> 或 <2>⌘</2> + <3>Enter</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:204
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:47
#: src/pages/search.jsx:317
msgid "Search"
msgstr "搜索"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "回复(新窗口)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Shift</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "喜欢(点赞)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> 或 <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1200
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
#: src/components/status.jsx:2724
msgid "Boost"
msgstr "转嘟"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Shift</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
#: src/components/status.jsx:2749
msgid "Bookmark"
msgstr "收藏"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "切换文字打码模式"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Shift</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "编辑列表"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "无法编辑列表。"

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "无法创建列表。"

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "显示对列表成员的回复"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "显示对我关注的人的回复"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "不显示回复"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "将此列表下的嘟文从主页/关注时间线隐藏"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "创建"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "是否删除该列表？"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "无法删除列表。"

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr "此列表中的嘟文不会出现在首页/关注时间线中"

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "媒体描述"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1306
#: src/components/status.jsx:1315
#: src/components/translation-block.jsx:239
msgid "Translate"
msgstr "翻译"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1334
msgid "Speak"
msgstr "朗读"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "在新窗口中打开原始媒体"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "打开原始媒体"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "正在尝试描述图像。请稍候…"

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "描述图像失败"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "描述图像"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "查看嘟文"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "敏感媒体"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "已过滤: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3850
#: src/components/status.jsx:3946
#: src/components/status.jsx:4024
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "已过滤"

#: src/components/media.jsx:479
msgid "Open file"
msgstr "打开文件"

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr "已设置定时发送"

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "嘟文已发布。点击查看。"

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr "已设置定时回复"

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "回复已发送。点击查看。"

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "嘟文已更新。点击查看。"

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "菜单"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "是否现在刷新页面以更新？"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "有更新可用…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr "关注"

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "补看"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "提及"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "通知"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "新"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "个人资料"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "收藏夹"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "点赞列表"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "关注的话题标签"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:341
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "过滤规则"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "已静音的用户"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "已静音的用户…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "已屏蔽的用户"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "已屏蔽的用户…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "账号…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:199
#: src/pages/status.jsx:925
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "登录"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "热门"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "跨站"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "快捷方式 / 栏…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "设置…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "列表"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "全部列表"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "通知"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "此通知来自你的其他账号。"

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "查看所有通知"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} 对你的嘟文进行了 {emojiObject} 的回应"

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} 发布了一条嘟文。"

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account}转嘟了你的回复。} other {{account} 转嘟了你的嘟文。}}} other {{account} 转嘟了你的 {postsCount} 条嘟文}}} other {{postType, select, reply {<0><1>{0}</1> 人</0> 转嘟了你的回复。} other {<2><3>{1}</3> 人</2> 转嘟了你的嘟文。}}}}"

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, =1 {{account} 关注了你。} other {<0><1>{0}</1> 人</0> 关注了你。}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} 请求关注你。"

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} 点赞了你的回复。} other {{account} 点赞了你的嘟文。}}} other {{account} 点赞了你的 {postsCount} 条嘟文}}} other {{postType, select, reply {<0><1>{0}</1> 人</0> 点赞了你的回复。} other {<2><3>{1}</3> 人</2> 点赞了你的嘟文。}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "你参与或创建的投票已结束。"

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "你创建的投票已结束。"

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "你参与的投票已结束。"

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "你互动过的嘟文已被编辑。"

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} 转嘟和点赞了你的回复。} other {{account} 转嘟和点赞了你的嘟文。}}} other {{account} 转嘟和点赞了你的 {postsCount} 条嘟文。}}} other {{postType, select, reply {<0><1>{0}</1> 人</0> 转嘟和点赞了你的回复。} other {<2><3>{1}</3> 人</2> 转嘟和点赞了你的嘟文。}}}}"

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} 已注册。"

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} 举报了 {targetAccount}"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "失去了与 <0>{name}</0> 的联系。"

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "管理警告"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr "你的 {year} #Wrapstodon 年度回顾在此！"

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "<0>{from}</0> 的一位管理员封禁了 <1>{targetName}</1> 的账户，你不再能接收其更新或与之互动。"

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "一名来自<0>{from}</0>的管理员已屏蔽<1>{targetName}</1>。受影响的粉丝数:{followersCount}，关注数:{followingCount}。"

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "你已屏蔽<0>{targetName}</0>。被移除的粉丝数:{followersCount}，关注数:{followingCount}。"

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "你的账号收到了一次管理警告。"

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "你的账号已被禁用。"

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "你的某些嘟文已被标记为敏感内容。"

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "你的某些嘟文已被删除。"

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "今后你的嘟文将被标记为敏感内容。"

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "你的账户已被限制。"

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "你的账户已被封禁。"

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[未知通知类型:{type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1277
#: src/components/status.jsx:1287
msgid "Boosted/Liked by…"
msgstr "查看转嘟/点赞…"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "查看点赞…"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "查看转嘟…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "被关注…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "了解更多 <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr "查看 #Wrapstodon 年度回顾"

#: src/components/notification.jsx:801
#: src/components/status.jsx:486
msgid "Read more →"
msgstr "阅读更多 →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "已投票"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr "{optionVotesCount, plural, other {# 票}}"

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "隐藏结果"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "投票"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1295
#: src/pages/status.jsx:1318
msgid "Refresh"
msgstr "刷新"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "显示结果"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, other {<1>{1}</1> 票}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, other {<1>{1}</1> 人投票}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "结束于 <0/>"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "已结束"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "<0/> 结束"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "即将结束"

#: src/components/recent-searches.jsx:27
msgid "Cleared recent searches"
msgstr "搜索记录已清除"

#: src/components/recent-searches.jsx:49
#: src/components/search-form.jsx:328
msgid "Recent searches"
msgstr "搜索记录"

#: src/components/recent-searches.jsx:59
msgid "Clear all"
msgstr "全部清除"

#: src/components/recent-searches.jsx:94
#: src/pages/account-statuses.jsx:334
msgid "Clear"
msgstr "清除"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0} 秒"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0} 分钟"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0} 小时"

#: src/components/report-modal.jsx:30
msgid "Spam"
msgstr "骚扰信息"

#: src/components/report-modal.jsx:31
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "恶意链接、虚假互动或重复回复"

#: src/components/report-modal.jsx:34
msgid "Illegal"
msgstr "违法"

#: src/components/report-modal.jsx:35
msgid "Violates the law of your or the server's country"
msgstr "违反你或你的实例所在国的法律"

#: src/components/report-modal.jsx:38
msgid "Server rule violation"
msgstr "违反实例规则"

#: src/components/report-modal.jsx:39
msgid "Breaks specific server rules"
msgstr "违反特定的实例规则"

#: src/components/report-modal.jsx:40
msgid "Violation"
msgstr "违规"

#: src/components/report-modal.jsx:43
msgid "Other"
msgstr "其它"

#: src/components/report-modal.jsx:44
msgid "Issue doesn't fit other categories"
msgstr "问题不适合其它的类别"

#: src/components/report-modal.jsx:111
msgid "Report Post"
msgstr "举报嘟文"

#: src/components/report-modal.jsx:111
msgid "Report @{username}"
msgstr "举报 @{username}"

#: src/components/report-modal.jsx:147
msgid "Pending review"
msgstr "等待审核"

#: src/components/report-modal.jsx:189
msgid "Post reported"
msgstr "已举报该嘟文"

#: src/components/report-modal.jsx:189
msgid "Profile reported"
msgstr "已举报该账户"

#: src/components/report-modal.jsx:197
msgid "Unable to report post"
msgstr "无法举报该嘟文"

#: src/components/report-modal.jsx:198
msgid "Unable to report profile"
msgstr "无法举报该账户"

#: src/components/report-modal.jsx:206
msgid "What's the issue with this post?"
msgstr "这条嘟文有什么问题？"

#: src/components/report-modal.jsx:207
msgid "What's the issue with this profile?"
msgstr "这个账户有什么问题？"

#: src/components/report-modal.jsx:276
msgid "Additional info"
msgstr "附加信息"

#: src/components/report-modal.jsx:299
msgid "Forward to <0>{domain}</0>"
msgstr "转发到 <0>{domain}</0>"

#: src/components/report-modal.jsx:309
msgid "Send Report"
msgstr "发送举报"

#: src/components/report-modal.jsx:318
msgid "Muted {username}"
msgstr "已静音 {username}"

#: src/components/report-modal.jsx:321
msgid "Unable to mute {username}"
msgstr "无法静音 {username}"

#: src/components/report-modal.jsx:326
msgid "Send Report <0>+ Mute profile</0>"
msgstr "发送举报 <0>+ 静音账户</0>"

#: src/components/report-modal.jsx:337
msgid "Blocked {username}"
msgstr "已屏蔽 {username}"

#: src/components/report-modal.jsx:340
msgid "Unable to block {username}"
msgstr "无法屏蔽 {username}"

#: src/components/report-modal.jsx:345
msgid "Send Report <0>+ Block profile</0>"
msgstr "发送举报 <0>并屏蔽账户</0>"

#: src/components/search-form.jsx:18
msgid "Posts with <0>{query}</0>"
msgstr "包含 <0>{query}</0> 的嘟文"

#: src/components/search-form.jsx:26
msgid "Accounts with <0>{query}</0>"
msgstr "包含 <0>{query}</0> 的账户"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:34
msgid "Posts tagged with <0>#{0}</0>"
msgstr "带有 <0>#{0}</0> 话题标签的嘟文"

#: src/components/search-form.jsx:43
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>- 账户、话题标签与嘟文</0>"

#: src/components/search-form.jsx:135
msgid "Look up <0>{query}</0>"
msgstr "查找 <0>{query}</0>"

#: src/components/search-form.jsx:361
#: src/pages/home.jsx:251
msgid "See all"
msgstr "查看全部"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "主页 / 关注"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "公共 (本站 / 跨站)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "账户"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "话题标签"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "列表ID"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "仅限本站"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:203
msgid "Instance"
msgstr "实例"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "可选，如 mastodon.social"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "搜索关键词"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "可选，除非为多栏模式"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "例如:像素艺术 (最多 5 个，用空格分隔)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "仅媒体"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "快捷方式"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "beta"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "指定一组快捷方式，显示样式为:"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "悬浮按钮"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "标签/菜单栏"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "多栏"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "在当前视图模式下不可用"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "向上移动"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "向下移动"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1557
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "编辑"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "需要添加一个以上快捷方式/栏以使其正常工作。"

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "尚未添加任何栏。点击以添加栏。"

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "尚未添加任何快捷方式。点击以添加快捷方式。"

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "不确定要添加哪些？<0/>可以先尝试添加<1>主页 / 关注和通知</1>。"

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "最多 {SHORTCUTS_LIMIT} 栏"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "最多 {SHORTCUTS_LIMIT} 个快捷方式"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "导入/导出"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "添加栏…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "添加快捷方式…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "特定的某个列表是可选的。但在多栏模式下，必须添加列表，否则对应的栏将不会显示。"

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "在多栏模式下，搜索关键词是必须的，否则对应的栏将不会显示。"

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "支持添加多个话题标签。以空格分隔。"

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "编辑快捷方式"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "添加快捷方式"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "时间线"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "列表"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "导入/导出 <0>快捷方式</0>"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "导入"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "在此粘贴快捷方式配置"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "从实例服务器下载保存的快捷方式…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "无法下载快捷方式"

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "从实例服务器下载快捷方式"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "* 已存在于当前快捷方式中"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "如果列表来自不同的账户，则可能无法使用。"

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "设置格式非法"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "是否追加到当前快捷方式？"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr "只有不存在于当前快捷方式中的快捷方式才会被追加。"

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "没有新的快捷方式可供导入"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "已导入快捷方式。但总数超出最大限制 {SHORTCUTS_LIMIT}，因此超出的部分未被导入。"

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "已导入快捷方式"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "导入并追加…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "是否覆盖当前快捷方式配置？"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "是否导入快捷方式？"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "或覆盖…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "导入…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "导出"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "已复制快捷方式"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "无法复制快捷方式"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "已复制快捷方式设置"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "无法复制快捷方式设置"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "分享"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "将快捷方式保存到实例服务器…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "已保存快捷方式"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "无法保存快捷方式"

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "同步到实例服务器"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0, plural, other {# 个字符}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "原始快捷方式 JSON"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "导入/导出配置到实例服务器(很不稳定)"

#: src/components/status.jsx:351
msgid "Unable to format math"
msgstr "无法渲染数学公式"

#: src/components/status.jsx:365
msgid "Math expressions found."
msgstr "检测到数学公式。"

#. Action to switch from rendered math back to raw (LaTeX) markup
#: src/components/status.jsx:368
msgid "Show markup"
msgstr "显示 LaTeX 标记"

#. Action to render math expressions from raw (LaTeX) markup
#: src/components/status.jsx:373
msgid "Format math"
msgstr "渲染公式"

#: src/components/status.jsx:772
msgid "<0/> <1>boosted</1>"
msgstr "<0/> <1>转嘟了</1>"

#: src/components/status.jsx:875
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "抱歉，你当前登录的实例无法与该外站嘟文互动。"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1029
msgid "Unliked @{0}'s post"
msgstr "已取消点赞 @{0} 的嘟文"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1030
msgid "Liked @{0}'s post"
msgstr "已点赞 @{0} 的嘟文"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1069
msgid "Unbookmarked @{0}'s post"
msgstr "已取消收藏 @{0} 的嘟文"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1070
msgid "Bookmarked @{0}'s post"
msgstr "已收藏 @{0} 的嘟文"

#: src/components/status.jsx:1169
msgid "Some media have no descriptions."
msgstr "某些媒体附件没有描述文本。"

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1176
msgid "Old post (<0>{0}</0>)"
msgstr "旧嘟文 (<0>{0}</0>)"

#: src/components/status.jsx:1200
#: src/components/status.jsx:1240
#: src/components/status.jsx:2700
#: src/components/status.jsx:2723
msgid "Unboost"
msgstr "取消转嘟"

#: src/components/status.jsx:1216
#: src/components/status.jsx:2715
msgid "Quote"
msgstr "引用"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1228
#: src/components/status.jsx:1694
msgid "Unboosted @{0}'s post"
msgstr "已取消转嘟 @{0} 的嘟文"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1229
#: src/components/status.jsx:1695
msgid "Boosted @{0}'s post"
msgstr "已转嘟 @{0} 的嘟文"

#: src/components/status.jsx:1241
msgid "Boost…"
msgstr "转嘟…"

#: src/components/status.jsx:1253
#: src/components/status.jsx:1988
#: src/components/status.jsx:2736
msgid "Unlike"
msgstr "取消喜欢"

#: src/components/status.jsx:1254
#: src/components/status.jsx:1988
#: src/components/status.jsx:1989
#: src/components/status.jsx:2736
#: src/components/status.jsx:2737
msgid "Like"
msgstr "赞"

#: src/components/status.jsx:1263
#: src/components/status.jsx:2748
msgid "Unbookmark"
msgstr "取消收藏"

#: src/components/status.jsx:1346
msgid "Post text copied"
msgstr "已复制嘟文文本"

#: src/components/status.jsx:1349
msgid "Unable to copy post text"
msgstr "无法复制嘟文文本"

#: src/components/status.jsx:1355
msgid "Copy post text"
msgstr "复制嘟文文本"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1373
msgid "View post by <0>@{0}</0>"
msgstr "查看 <0>@{0}</0> 的嘟文"

#: src/components/status.jsx:1394
msgid "Show Edit History"
msgstr "显示编辑记录"

#: src/components/status.jsx:1397
msgid "Edited: {editedDateText}"
msgstr "编辑于: {editedDateText}"

#: src/components/status.jsx:1464
#: src/components/status.jsx:3516
msgid "Embed post"
msgstr "嵌入嘟文"

#: src/components/status.jsx:1478
msgid "Conversation unmuted"
msgstr "已取消静音该对话"

#: src/components/status.jsx:1478
msgid "Conversation muted"
msgstr "已静音该对话"

#: src/components/status.jsx:1484
msgid "Unable to unmute conversation"
msgstr "无法取消静音该对话"

#: src/components/status.jsx:1485
msgid "Unable to mute conversation"
msgstr "无法静音该对话"

#: src/components/status.jsx:1494
msgid "Unmute conversation"
msgstr "取消静音对话"

#: src/components/status.jsx:1501
msgid "Mute conversation"
msgstr "静音对话"

#: src/components/status.jsx:1517
msgid "Post unpinned from profile"
msgstr "已取消置顶该嘟文"

#: src/components/status.jsx:1518
msgid "Post pinned to profile"
msgstr "已置顶该嘟文"

#: src/components/status.jsx:1523
msgid "Unable to unpin post"
msgstr "无法取消置顶该嘟文"

#: src/components/status.jsx:1523
msgid "Unable to pin post"
msgstr "无法置顶该嘟文"

#: src/components/status.jsx:1532
msgid "Unpin from profile"
msgstr "取消置顶"

#: src/components/status.jsx:1539
msgid "Pin to profile"
msgstr "置顶"

#: src/components/status.jsx:1568
msgid "Delete this post?"
msgstr "是否删除此嘟文？"

#: src/components/status.jsx:1584
msgid "Post deleted"
msgstr "嘟文已删除"

#: src/components/status.jsx:1587
msgid "Unable to delete post"
msgstr "无法删除嘟文"

#: src/components/status.jsx:1615
msgid "Report post…"
msgstr "举报嘟文…"

#: src/components/status.jsx:1989
#: src/components/status.jsx:2025
#: src/components/status.jsx:2737
msgid "Liked"
msgstr "已点赞"

#: src/components/status.jsx:2022
#: src/components/status.jsx:2724
msgid "Boosted"
msgstr "已转嘟"

#: src/components/status.jsx:2032
#: src/components/status.jsx:2749
msgid "Bookmarked"
msgstr "已收藏"

#: src/components/status.jsx:2036
msgid "Pinned"
msgstr "已置顶"

#: src/components/status.jsx:2082
#: src/components/status.jsx:2561
msgid "Deleted"
msgstr "已删除"

#: src/components/status.jsx:2123
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, other {# 条回复}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2213
msgid "Thread{0}"
msgstr "嘟文串{0}"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
#: src/components/status.jsx:2457
msgid "Show less"
msgstr "折叠"

#: src/components/status.jsx:2291
#: src/components/status.jsx:2353
msgid "Show content"
msgstr "显示内容"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2453
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "已过滤: {0}"

#: src/components/status.jsx:2457
msgid "Show media"
msgstr "显示媒体"

#: src/components/status.jsx:2597
msgid "Edited"
msgstr "已编辑"

#: src/components/status.jsx:2674
msgid "Comments"
msgstr "评论"

#. More from [Author]
#: src/components/status.jsx:2974
msgid "More from <0/>"
msgstr "<0/> 的更多内容"

#: src/components/status.jsx:3276
msgid "Edit History"
msgstr "编辑记录"

#: src/components/status.jsx:3280
msgid "Failed to load history"
msgstr "无法加载编辑记录"

#: src/components/status.jsx:3285
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "正在加载…"

#: src/components/status.jsx:3521
msgid "HTML Code"
msgstr "HTML 代码"

#: src/components/status.jsx:3538
msgid "HTML code copied"
msgstr "已复制 HTML 代码"

#: src/components/status.jsx:3541
msgid "Unable to copy HTML code"
msgstr "无法复制 HTML 代码"

#: src/components/status.jsx:3553
msgid "Media attachments:"
msgstr "媒体附件:"

#: src/components/status.jsx:3575
msgid "Account Emojis:"
msgstr "账户表情:"

#: src/components/status.jsx:3606
#: src/components/status.jsx:3651
msgid "static URL"
msgstr "静态URL"

#: src/components/status.jsx:3620
msgid "Emojis:"
msgstr "表情:"

#: src/components/status.jsx:3665
msgid "Notes:"
msgstr "注意:"

#: src/components/status.jsx:3669
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "此代码是静态代码，不包含样式和脚本。你可能需要进行编辑并按需应用自己的样式。"

#: src/components/status.jsx:3675
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "代码中的投票无法交互，将显示为一个带有投票数的列表。"

#: src/components/status.jsx:3680
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "媒体附件可以是图片、视频、音频或任何文件类型。"

#: src/components/status.jsx:3686
msgid "Post could be edited or deleted later."
msgstr "嘟文可以稍后编辑或删除。"

#: src/components/status.jsx:3692
msgid "Preview"
msgstr "预览"

#: src/components/status.jsx:3701
msgid "Note: This preview is lightly styled."
msgstr "注意: 此预览带有少量额外的样式"

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3954
msgid "<0/> <1/> boosted"
msgstr "<0/> <1/> 转嘟了"

#: src/components/status.jsx:4056
msgid "Post hidden by your filters"
msgstr "嘟文已被你的过滤规则隐藏"

#: src/components/status.jsx:4057
msgid "Post pending"
msgstr "待发布嘟文"

#: src/components/status.jsx:4058
#: src/components/status.jsx:4059
#: src/components/status.jsx:4060
#: src/components/status.jsx:4061
msgid "Post unavailable"
msgstr "嘟文不可用"

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "新嘟文"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1078
#: src/pages/status.jsx:1455
msgid "Try again"
msgstr "重试"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, other {# 转嘟}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr "置顶嘟文"

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "嘟文串"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>已过滤</0>: <1>{0}</1>"

#: src/components/translation-block.jsx:196
msgid "Auto-translated from {sourceLangText}"
msgstr "已自动从 {sourceLangText} 翻译"

#: src/components/translation-block.jsx:234
msgid "Translating…"
msgstr "正在翻译…"

#: src/components/translation-block.jsx:237
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "从 {sourceLangText} 翻译 (语言为自动检测)"

#: src/components/translation-block.jsx:238
msgid "Translate from {sourceLangText}"
msgstr "从 {sourceLangText} 翻译"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:266
msgid "Auto ({0})"
msgstr "自动 ({0})"

#: src/components/translation-block.jsx:279
msgid "Failed to translate"
msgstr "翻译失败"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "正在编辑原嘟"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "正在回复 @{0}"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "你现在可以关闭此页面。"

#: src/compose.jsx:71
msgid "Close window"
msgstr "关闭窗口"

#: src/compose.jsx:87
msgid "Login required."
msgstr "需要登录。"

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:280
msgid "Go home"
msgstr "返回主页"

#: src/pages/account-statuses.jsx:241
msgid "Account posts"
msgstr "该账户的嘟文"

#: src/pages/account-statuses.jsx:248
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ 回复)"

#: src/pages/account-statuses.jsx:250
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- 转嘟)"

#: src/pages/account-statuses.jsx:252
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:254
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (媒体)"

#: src/pages/account-statuses.jsx:260
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:331
msgid "Clear filters"
msgstr "清除过滤规则"

#: src/pages/account-statuses.jsx:348
msgid "Showing post with replies"
msgstr "已展示回复"

#: src/pages/account-statuses.jsx:353
msgid "+ Replies"
msgstr "+ 回复"

#: src/pages/account-statuses.jsx:359
msgid "Showing posts without boosts"
msgstr "已隐藏转嘟"

#: src/pages/account-statuses.jsx:364
msgid "- Boosts"
msgstr "- 转嘟"

#: src/pages/account-statuses.jsx:370
msgid "Showing posts with media"
msgstr "已展示媒体"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:387
msgid "Showing posts tagged with #{0}"
msgstr "已展示带有 #{0} 话题标签的嘟文"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:426
msgid "Showing posts in {0}"
msgstr "已显示 {0} 中的嘟文"

#: src/pages/account-statuses.jsx:516
msgid "Nothing to see here yet."
msgstr "空空如也"

#: src/pages/account-statuses.jsx:517
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "无法加载嘟文"

#: src/pages/account-statuses.jsx:558
#: src/pages/account-statuses.jsx:588
msgid "Unable to fetch account info"
msgstr "无法获取账户信息"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:565
msgid "Switch to account's instance {0}"
msgstr "切换到该账户所在实例 {0}"

#: src/pages/account-statuses.jsx:595
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "切换到我的实例 (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:668
msgid "Month"
msgstr "月"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "当前账号"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "默认"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "切换到此账户"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "在新标签页/窗口中切换"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "查看个人资料…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "设为默认账号"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr "是否退出 <0>@{0}</0>？"

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "登出…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr "在 {0} (<0/>) 建立连接"

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "添加现有账号"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "注意: 首次加载时始终使用<0>默认</0>帐户。当前帐户被切换后将在会话期间保持有效。"

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "尚无收藏。去收藏一些嘟文吧！"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "无法加载收藏夹。"

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "最近 1 小时"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "最近 2 小时"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "最近 3 小时"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "最近 4 小时"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "最近 5 小时"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "最近 6 小时"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "最近 7 小时"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "最近 8 小时"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "最近 9 小时"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "最近 10 小时"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "最近 11 小时"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "最近 12 小时"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "12 小时之前"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "关注的话题标签"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "分组"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "已显示 {selectedFilterCategory, select, all {所有嘟文} original {原创嘟文} replies {回复} boosts {转嘟} followedTags {关注的话题标签} groups {群组} filtered {过滤后的嘟文}}, {sortBy, select, createdAt {{sortOrder, select, asc {最旧嘟文} desc {最新嘟文}}} reblogsCount {{sortOrder, select, asc {转嘟最少} desc {转嘟最多}}} favouritesCount {{sortOrder, select, asc {点赞最少} desc {点赞最多}}} repliesCount {{sortOrder, select, asc {回复最少} desc {回复最多}}} density {{sortOrder, select, asc {内容最少} desc {内容最多}}}} first{groupBy, select, account {, 按作者分组} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "补看 <0>beta</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "帮助"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "这是什么？"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "“补看”是你关注的内容的一个独立时间线，提供一种一目了然的高级视图，它拥有灵感来源于电子邮件的简洁界面，能够让你轻松对嘟文进行排序和筛选。"

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "补看界面预览"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "让我们开始补看吧"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "让我们补看你所关注的人的嘟文。"

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "选择要显示的嘟文的时间范围…"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "尽可能多"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "补看"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "与上次补看的时间范围重叠"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "直至上次补看 ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "注意: 无论时间范围如何，你的实例可能在主页时间线中最多只显示800条嘟文。这个值可能会更少或更多。"

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "之前的补看…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, other {# 条嘟文}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "是否删除这次补看？"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr "正在移除补看 {0}"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr "补看 {0} 已被移除"

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "注意: 最多只存储 3 次。更早的补看将被自动删除。"

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "正在获取嘟文…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "这可能需要一段时间。"

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "重置过滤规则"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "热门链接"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "由 {0} 分享"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:330
msgid "All"
msgstr "全部"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, other {# 名作者}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "排序"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "日期"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "内容"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr "分组"

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "作者"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "无"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "显示所有作者"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "你不必阅读所有内容。"

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "这就是全部内容。"

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "返回顶部"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "你关注的人分享的链接，按分享次数、转嘟次数和点赞数排序。"

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "排序: 内容"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "嘟文按信息密度或深度排序。较短的嘟文“更轻”，而较长的嘟文“更重”。带图嘟文比不带图的嘟文“更重”。"

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "分组: 作者"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "嘟文按作者分组，按每位作者的嘟文数量排序。"

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "下一位作者"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "上一位作者"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "返回顶部"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "尚无点赞。去点赞一些嘟文吧！"

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "无法加载点赞列表。"

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "主页与列表"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "公共时间线"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "对话"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "账户"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "永不"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "新建过滤规则"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0, plural, other {# 条过滤规则}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "无法加载过滤规则。"

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "尚无过滤规则。"

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "添加过滤规则。"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "编辑过滤规则"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "无法编辑过滤规则"

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "无法创建过滤规则"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "标题"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "整词匹配"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "尚无关键词。请添加一个。"

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "添加关键词"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, other {# 个关键词}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "过滤范围…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* 此功能尚未实现"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "状态: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "更改到期时间"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "到期时间"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "过滤后的嘟文将被…"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr "已隐藏 (仅媒体)"

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "最小化"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "隐藏"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "是否删除此过滤规则？"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "无法删除过滤规则。"

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "已到期"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "将于 <0/> 到期"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "永不到期"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural, other {# 个话题标签}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "无法加载已关注的话题标签。"

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "尚无关注的话题标签。"

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "这里空空如也。"

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "无法加载嘟文."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{instance} 上的 {hashtagTitle} (仅查看媒体)"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{instance} 上的 {hashtagTitle}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (仅查看媒体)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "尚无任何用户在此话题标签下发布内容。"

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "无法加载此话题标签下的嘟文"

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "是否取关 #{hashtag}？"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "已取消关注 #{hashtag}"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "已关注 #{hashtag}"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "正在关注…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "已从个人页精选中移除"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "无法从个人页精选中移除"

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "已加入个人页精选"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr "{TOTAL_TAGS_LIMIT, plural, other {最多 # 个话题标签}}"

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "添加话题标签"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "移除话题标签"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr "{SHORTCUTS_LIMIT, plural, other {已达到最多 # 个快捷方式的限制。无法添加快捷方式。}}"

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "此快捷方式已存在"

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr "已添加话题标签快捷方式"

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "添加到快捷方式"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "输入一个新实例地址，例如 ”mastodon.social“"

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "实例地址无效"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "转到其它实例…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "转到我所在的实例 (<0>{currentInstance}</0>)"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "无法获取通知。"

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<0>新的</0> <1>关注请求</1>"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "正在解析…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "无法解析 URL"

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "空空如也。"

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "管理成员"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "是否从列表中移除 <0>@{0}</0>？"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "移除…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, other {# 个列表}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "尚无列表。"

#: src/pages/login.jsx:122
#: src/pages/login.jsx:132
msgid "Failed to register application"
msgstr "注册应用失败"

#: src/pages/login.jsx:218
msgid "instance domain"
msgstr "实例域名"

#: src/pages/login.jsx:242
msgid "e.g. “mastodon.social”"
msgstr "例如：“mastodon.social”"

#: src/pages/login.jsx:253
msgid "Failed to log in. Please try again or try another instance."
msgstr "登录失败。请重试或登录其它实例。"

#: src/pages/login.jsx:265
msgid "Continue with {selectedInstanceText}"
msgstr "继续登录并使用 {selectedInstanceText}"

#: src/pages/login.jsx:266
msgid "Continue"
msgstr "继续"

#: src/pages/login.jsx:274
msgid "Don't have an account? Create one!"
msgstr "还没有账户？创建一个吧！"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "私信"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "私信"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "没有他人提到你的记录 :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "无法加载提及列表。"

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "你没有关注的人"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "没有关注你的人"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "新注册的账户"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr "不请自来地提及你的人"

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr "被实例管理员限制的人"

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "通知设置"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "新通知"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, other {公告}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "关注请求"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, other {# 个关注请求}}"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr "{0, plural, other {过滤了 # 人的通知}}"

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "仅提及"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "今天"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "你已经全读完了。"

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "昨天"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "无法加载通知"

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "通知设置已更新"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "过滤来自这些人的通知:"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "过滤规则"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "忽略"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "更新于 <0>{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr "查看来自 <0>@{0}</0> 的通知"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr "来自 <0>@{0}</0> 的通知"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr "现在起，将不在过滤来自 @{0} 的通知。"

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr "无法接受通知请求。"

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "允许"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr "现在起，将过滤来自 @{0} 的通知。"

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr "无法忽略通知请求"

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "忽略"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "已忽略"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "本站时间线 ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "跨站时间线 ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "本站时间线"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "跨站时间线"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "还没有人发布内容。"

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "切换到跨站时间线"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "切换到本站时间线"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr "暂无定时嘟文。"

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr "将在 <0><1/></0> <2>({0})</2> 发布"

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr "将在 <0><1/></0> 发布"

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr "已修改发布时间"

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr "修改发布时间失败"

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr "修改发布时间"

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr "是否删除该定时嘟文？"

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr "已删除该定时嘟文"

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr "删除定时嘟文失败"

#: src/pages/search.jsx:51
msgid "Search: {q} (Posts)"
msgstr "搜索: {q} (嘟文)"

#: src/pages/search.jsx:54
msgid "Search: {q} (Accounts)"
msgstr "搜索: {q} (账户)"

#: src/pages/search.jsx:57
msgid "Search: {q} (Hashtags)"
msgstr "搜索: {q} (话题标签)"

#: src/pages/search.jsx:60
msgid "Search: {q}"
msgstr "搜索: {q}"

#: src/pages/search.jsx:340
#: src/pages/search.jsx:422
msgid "Hashtags"
msgstr "话题标签"

#: src/pages/search.jsx:372
#: src/pages/search.jsx:426
#: src/pages/search.jsx:496
msgid "See more"
msgstr "查看更多"

#: src/pages/search.jsx:398
msgid "See more accounts"
msgstr "查看更多账户"

#: src/pages/search.jsx:412
msgid "No accounts found."
msgstr "未找到账户。"

#: src/pages/search.jsx:468
msgid "See more hashtags"
msgstr "查看更多话题标签"

#: src/pages/search.jsx:482
msgid "No hashtags found."
msgstr "未找到话题标签。"

#: src/pages/search.jsx:526
msgid "See more posts"
msgstr "查看更多嘟文"

#: src/pages/search.jsx:540
msgid "No posts found."
msgstr "未找到嘟文。"

#: src/pages/search.jsx:585
msgid "Enter your search term or paste a URL above to get started."
msgstr "输入你要搜索的关键词或粘贴一条 URL 以开始搜索。"

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "设置"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "外观"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "浅色"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "深色"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "自动"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "文字大小"

#. Preview of one character, in largest size
#. Preview of one character, in smallest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "字"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "界面语言"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "贡献翻译"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "发嘟"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "默认可见性"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "已同步"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "更新默认可见性失败"

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "已同步到你账号所在的实例服务端的设置。<0>前往你所在的实例 ({instance}) 查看更多设置。</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "实验性功能"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "实时刷新时间线嘟文"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "转嘟轮播"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "嘟文翻译"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr "翻译为 "

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "系统语言 ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr "{0, plural, =0 {对下列语言隐藏“翻译”按钮: } other {对下列语言隐藏“翻译”按钮 (#):}}"

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr "注意: 此功能使用外部翻译服务，翻译服务由 <0>{TRANSLATION_API_NAME}</0> 驱动。"

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "自动翻译"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr "自动显示时间线中的嘟文的翻译。仅适用于不含内容警告、媒体或投票的<0>短</0>嘟文。"

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "为嘟文撰写框集成 GIF 选择器"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "注意: 此功能使用外部 GIF 搜索服务，由 <0>GIPHY</0> 驱动。该服务为 G 级(适合所有年龄浏览)，请求已去除跟踪参数，并省略了 referrer 信息，但搜索关键词和 IP 地址信息仍会到达 GIPHY 的服务器。"

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "图片描述文本生成器"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr "仅适用于发布新嘟文时新插入的图片。"

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "注意: 此功能使用外部人工智能服务，由 <0>img-alt-api</0> 驱动。可能效果不佳。仅适用于图像，描述文本内容为英文。"

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr "服务端通知分组"

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr "开发阶段特性。启用该功能可能会为通知窗口的分组带来改进，但分组逻辑较为简单。"

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "“云”导入/导出快捷方式配置。"

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr "⚠️⚠️⚠️ 非常不稳定。<0/>配置存储于你对自己账户的备注中。账户的(私人)备注主要用于其他账户，对自己的账户是隐藏的。"

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "注意: 此功能使用了当前登录实例的服务端 API。"

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr "文字打码模式 <0>(<1>文本</1> → <2>████</2>)</0>"

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr "将文字替换为块，在截图时保护隐私。"

#: src/pages/settings.jsx:710
msgid "About"
msgstr "关于"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr "由 <1>@cheeaun</1> <0>开发</0>"

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "赞助者"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "赞助"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr "新增功能"

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "隐私政策"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>站点: </0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0> 版本: </0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "已复制版本号"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr "无法复制版本号"

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr "订阅更新失败。请重试。"

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr "订阅删除失败。请重试。"

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "通知推送 (beta)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr "通知推送已被阻止。请在你的浏览器设置中授予相关权限。"

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "推送范围 <0>{0}</0>"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "任何人"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "我关注的人"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "粉丝"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "关注"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "投票"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "嘟文被编辑"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr "本次登录未授予通知推送权限。你需要<0>再次<1>登录</1>以授予推送权限</0>。"

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "注意: 只能推送 <0>一个账户</0> 的通知。"

#. js-lingui-explicit-id
#: src/pages/status.jsx:658
#: src/pages/status.jsx:1221
msgid "post.title"
msgstr "嘟文"

#: src/pages/status.jsx:912
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "你尚未登录。互动 ()"

#: src/pages/status.jsx:932
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "此嘟文加载自其它实例 (<0>{instance}</0>)。 无法进行互动 (回复、转嘟等)。"

#: src/pages/status.jsx:960
msgid "Error: {e}"
msgstr "错误: {e}"

#: src/pages/status.jsx:967
msgid "Switch to my instance to enable interactions"
msgstr "切换到我所在的实例以进行互动"

#: src/pages/status.jsx:1069
msgid "Unable to load replies."
msgstr "无法加载回复。"

#: src/pages/status.jsx:1181
msgid "Back"
msgstr "返回"

#: src/pages/status.jsx:1212
msgid "Go to main post"
msgstr "跳转到主嘟文"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1235
msgid "{0} posts above ‒ Go to top"
msgstr "还有 {0} 条上级嘟文 ‒ 转到顶部"

#: src/pages/status.jsx:1282
#: src/pages/status.jsx:1345
msgid "Switch to Side Peek view"
msgstr "切换至侧边预览视图"

#: src/pages/status.jsx:1346
msgid "Switch to Full view"
msgstr "切换至完整视图"

#: src/pages/status.jsx:1364
msgid "Show all sensitive content"
msgstr "显示所有敏感内容"

#: src/pages/status.jsx:1369
msgid "Experimental"
msgstr "实验性功能"

#: src/pages/status.jsx:1378
msgid "Unable to switch"
msgstr "无法切换"

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1385
msgid "Switch to post's instance ({0})"
msgstr "切换到原嘟所属的实例 ({0})"

#: src/pages/status.jsx:1388
msgid "Switch to post's instance"
msgstr "切换到原嘟所属实例"

#: src/pages/status.jsx:1446
msgid "Unable to load post"
msgstr "无法加载嘟文"

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1582
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, other {<0>{1}</0> 条回复}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1600
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, other {<0>{0}</0> 条评论}}"

#: src/pages/status.jsx:1622
msgid "View post with its replies"
msgstr "查看嘟文及其回复"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "热门 ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "热门新闻"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr "作者 {0}"

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "返回热门嘟文页"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "已显示提到 <0>{0}</0> 的嘟文"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "热门嘟文"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "没有热门嘟文。"

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "简约、有特点的 Mastodon 网页客户端。"

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "使用 Mastodon 登录"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "注册"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "连接你现有的 Mastodon/Fediverse 账户。<0/>你的凭据不会在此客户端的服务器上存储。"

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr "由 <1>@cheeaun</1> <0>开发</0>。<2>隐私政策</2>。"

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "转嘟轮播功能的效果图"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "转嘟轮播"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "在视觉上区分原创嘟文和被再次分享的嘟文 (转嘟)。"

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr "带嵌套评论的嘟文串的显示效果图"

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr "带嵌套评论的嘟文串"

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "轻松跟踪对话。半可折叠式回复。"

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr "通知分组的效果图"

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "通知分组"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr "相似的通知被分组并折叠，以减少混乱度。"

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "多栏界面的效果图"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "单栏或多栏"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "默认使用单栏视图，满足沉浸体验需求。为高级用户提供可配置的多栏视图。"

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "多话题标签时间线的显示效果图，包含一个添加更多标签的配置表"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "多话题标签时间线"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "将最多 5 个话题标签合并为一个单独的时间线显示。"

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "你的浏览器似乎阻止了弹出窗口。"

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr "当前已有被最小化的嘟文草稿。在创建新嘟文之前，请发布或丢弃它。"

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr "当前已打开一个嘟文撰写框。在创建新嘟文之前，请发布或丢弃它。"

