{"name": "phanpy", "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "fetch-instances": "node scripts/fetch-instances-list.js", "sourcemap": "npx source-map-explorer dist/assets/*.js", "bundle-visualizer": "npx vite-bundle-visualizer", "messages:extract": "lingui extract", "messages:extract:clean": "lingui extract --locale en --clean", "messages:compile": "lingui compile", "fetch-i18n-volunteers": "env $(cat .env.local | grep -v \"#\" | xargs) node scripts/fetch-i18n-volunteers.js", "readme:i18n-volunteers": "node scripts/update-i18n-volunteers-readme.js", "test": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed", "formatting-check": "npx biome check"}, "dependencies": {"@formatjs/intl-localematcher": "~0.6.1", "@formatjs/intl-segmenter": "~11.7.10", "@formkit/auto-animate": "~0.8.2", "@github/text-expander-element": "~2.9.2", "@iconify-icons/mingcute": "~1.2.9", "@justinribeiro/lite-youtube": "~1.8.2", "@lingui/detect-locale": "~5.4.1", "@lingui/macro": "~5.4.1", "@lingui/react": "~5.4.1", "@szhsin/react-menu": "~4.4.1", "chroma-js": "~3.1.2", "compare-versions": "~6.1.1", "fast-blurhash": "~1.1.4", "fast-equals": "~5.2.2", "fuse.js": "~7.1.0", "html-prettify": "~1.0.7", "idb-keyval": "~6.2.2", "intl-locale-textinfo-polyfill": "~2.1.1", "js-cookie": "~3.0.5", "just-debounce-it": "~3.2.0", "lz-string": "~1.5.0", "masto": "~7.2.0", "moize": "~6.1.6", "p-retry": "~6.2.1", "p-throttle": "~7.0.0", "preact": "10.27.1", "punycode": "~2.3.1", "react-hotkeys-hook": "~5.1.0", "react-intersection-observer": "~9.16.0", "react-quick-pinch-zoom": "~5.1.0", "react-router-dom": "6.6.2", "string-length": "6.0.0", "swiped-events": "~1.2.0", "temml": "~0.11.10", "tinyld": "~1.3.4", "toastify-js": "~1.12.0", "uid": "~2.0.2", "use-debounce": "~10.0.5", "use-long-press": "~3.3.0", "use-resize-observer": "~9.1.0", "valtio": "2.1.7"}, "devDependencies": {"@biomejs/biome": "2.2.0", "@lingui/babel-plugin-lingui-macro": "~5.4.1", "@lingui/cli": "~5.4.1", "@lingui/vite-plugin": "~5.4.1", "@playwright/test": "~1.54.2", "@preact/preset-vite": "~2.10.2", "@types/node": "~24.3.0", "postcss": "~8.5.6", "postcss-dark-theme-class": "~1.3.0", "postcss-preset-env": "~10.2.4", "sonda": "~0.9.0", "twitter-text": "~3.1.0", "vite": "~7.0.6", "vite-plugin-generate-file": "~0.3.1", "vite-plugin-html-config": "~2.0.2", "vite-plugin-pwa": "~1.0.3", "vite-plugin-remove-console": "~2.2.0", "vite-plugin-run": "~0.6.1", "workbox-cacheable-response": "~7.3.0", "workbox-expiration": "~7.3.0", "workbox-navigation-preload": "~7.3.0", "workbox-routing": "~7.3.0", "workbox-strategies": "~7.3.0"}, "postcss": {"plugins": {"postcss-dark-theme-class": {}, "postcss-preset-env": {"features": {"logical-properties-and-values": false}}}}, "overrides": {"vite": {"rollup": ">=4.5.1"}}, "browserslist": ["defaults", "android >= 4"]}